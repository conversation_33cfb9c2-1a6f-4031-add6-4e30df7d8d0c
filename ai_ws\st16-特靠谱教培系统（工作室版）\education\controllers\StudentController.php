<?php
/**
 * 学生控制器
 * 处理学生相关的请求
 */
class StudentController extends Controller {
    /**
     * 学生模型
     * @var StudentModel
     */
    private $studentModel;
    
    /**
     * 用户模型
     * @var UserModel
     */
    private $userModel;
    
    /**
     * 构造函数
     */
    public function __construct() {
        parent::__construct();
        
        // 初始化模型
        $this->studentModel = new StudentModel();
        $this->userModel = new UserModel();
        
        // 检查权限（只有管理员和教师可以访问学生列表，学生只能访问自己的资料）
        $this->checkPermission();
    }
    
    /**
     * 检查权限
     */
    private function checkPermission() {
        // 获取当前用户
        $user = isset($_SESSION['user']) ? $_SESSION['user'] : null;
        
        // 获取当前操作
        $action = isset($_GET['action']) ? $_GET['action'] : 'index';
        
        // 学生只能访问自己的资料
        if ($user && $user['role'] === 'student' && !in_array($action, ['profile', 'updateProfile'])) {
            $this->redirect('error/forbidden');
        }
    }
    
    /**
     * 学生列表页面
     */
    public function index() {
        // 检查权限（只有管理员和教师可以访问学生列表）
        if ($_SESSION['user']['role'] === 'student') {
            $this->redirect('error/forbidden');
        }
        
        // 获取当前页码
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $page = max(1, $page);
        
        // 每页显示数量
        $perPage = 10;
        
        // 获取搜索关键词
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        
        // 获取学生列表
        if (!empty($keyword)) {
            $students = $this->studentModel->searchStudents($keyword, $page, $perPage);
            $total = $this->studentModel->getSearchCount($keyword);
        } else {
            $students = $this->studentModel->getStudents($page, $perPage);
            $total = $this->studentModel->getStudentCount();
        }
        
        // 计算总页数
        $totalPages = ceil($total / $perPage);
        
        // 渲染视图
        $this->render('index', [
            'students' => $students,
            'page' => $page,
            'perPage' => $perPage,
            'total' => $total,
            'totalPages' => $totalPages,
            'keyword' => $keyword
        ]);
    }
    
    /**
     * 添加学生页面
     */
    public function add() {
        // 检查权限（只有管理员可以添加学生）
        if ($_SESSION['user']['role'] !== 'admin') {
            $this->redirect('error/forbidden');
        }
        
        // 渲染视图
        $this->render('add');
    }
    
    /**
     * 处理添加学生请求
     */
    public function doAdd() {
        // 检查权限（只有管理员可以添加学生）
        if ($_SESSION['user']['role'] !== 'admin') {
            $this->json(null, 403, '权限不足');
        }
        
        // 检查是否为POST请求
        if (!$this->isPost()) {
            $this->json(null, 400, '无效的请求方法');
        }
        
        // 获取表单数据
        $name = $this->post('name');
        $email = $this->post('email');
        $password = $this->post('password');
        $phone = $this->post('phone');
        $studentNumber = $this->post('student_number');
        $parentName = $this->post('parent_name');
        $parentPhone = $this->post('parent_phone');
        $grade = $this->post('grade');
        
        // 验证数据
        if (empty($name) || empty($email) || empty($password)) {
            $this->json(null, 400, '请填写必填字段');
        }
        
        // 检查邮箱是否已存在
        if ($this->userModel->emailExists($email)) {
            $this->json(null, 400, '邮箱已被使用');
        }
        
        // 开始事务
        $db = Database::getInstance();
        $db->beginTransaction();
        
        try {
            // 创建用户
            $userId = $this->userModel->createUser([
                'name' => $name,
                'email' => $email,
                'password' => password_hash($password, PASSWORD_DEFAULT),
                'role' => 'student',
                'phone' => $phone,
                'status' => 1
            ]);
            
            // 创建学生
            $studentId = $this->studentModel->createStudent([
                'user_id' => $userId,
                'student_number' => $studentNumber,
                'parent_name' => $parentName,
                'parent_phone' => $parentPhone,
                'grade' => $grade
            ]);
            
            // 提交事务
            $db->commit();
            
            // 返回成功响应
            $this->json(['id' => $studentId], 200, '学生添加成功');
        } catch (Exception $e) {
            // 回滚事务
            $db->rollBack();
            
            // 返回错误响应
            $this->json(null, 500, '添加学生失败：' . $e->getMessage());
        }
    }
    
    /**
     * 编辑学生页面
     * @param int $id 学生ID
     */
    public function edit($id) {
        // 检查权限（只有管理员可以编辑学生）
        if ($_SESSION['user']['role'] !== 'admin') {
            $this->redirect('error/forbidden');
        }
        
        // 获取学生信息
        $student = $this->studentModel->getStudentById($id);
        
        // 检查学生是否存在
        if (!$student) {
            $this->redirect('error/notFound');
        }
        
        // 渲染视图
        $this->render('edit', ['student' => $student]);
    }
    
    /**
     * 处理编辑学生请求
     */
    public function doEdit() {
        // 检查权限（只有管理员可以编辑学生）
        if ($_SESSION['user']['role'] !== 'admin') {
            $this->json(null, 403, '权限不足');
        }
        
        // 检查是否为POST请求
        if (!$this->isPost()) {
            $this->json(null, 400, '无效的请求方法');
        }
        
        // 获取表单数据
        $id = $this->post('id');
        $name = $this->post('name');
        $email = $this->post('email');
        $password = $this->post('password');
        $phone = $this->post('phone');
        $studentNumber = $this->post('student_number');
        $parentName = $this->post('parent_name');
        $parentPhone = $this->post('parent_phone');
        $grade = $this->post('grade');
        
        // 验证数据
        if (empty($id) || empty($name) || empty($email)) {
            $this->json(null, 400, '请填写必填字段');
        }
        
        // 获取学生信息
        $student = $this->studentModel->getStudentById($id);
        
        // 检查学生是否存在
        if (!$student) {
            $this->json(null, 404, '学生不存在');
        }
        
        // 检查邮箱是否已被其他用户使用
        if ($email !== $student['email'] && $this->userModel->emailExists($email)) {
            $this->json(null, 400, '邮箱已被使用');
        }
        
        // 开始事务
        $db = Database::getInstance();
        $db->beginTransaction();
        
        try {
            // 更新用户信息
            $userData = [
                'name' => $name,
                'email' => $email,
                'phone' => $phone
            ];
            
            // 如果提供了新密码，则更新密码
            if (!empty($password)) {
                $userData['password'] = password_hash($password, PASSWORD_DEFAULT);
            }
            
            $this->userModel->updateUser($student['user_id'], $userData);
            
            // 更新学生信息
            $this->studentModel->updateStudent($id, [
                'student_number' => $studentNumber,
                'parent_name' => $parentName,
                'parent_phone' => $parentPhone,
                'grade' => $grade
            ]);
            
            // 提交事务
            $db->commit();
            
            // 返回成功响应
            $this->json(['id' => $id], 200, '学生信息更新成功');
        } catch (Exception $e) {
            // 回滚事务
            $db->rollBack();
            
            // 返回错误响应
            $this->json(null, 500, '更新学生信息失败：' . $e->getMessage());
        }
    }
    
    /**
     * 处理删除学生请求
     */
    public function delete() {
        // 检查权限（只有管理员可以删除学生）
        if ($_SESSION['user']['role'] !== 'admin') {
            $this->json(null, 403, '权限不足');
        }
        
        // 检查是否为POST请求
        if (!$this->isPost()) {
            $this->json(null, 400, '无效的请求方法');
        }
        
        // 获取学生ID
        $id = $this->post('id');
        
        // 验证数据
        if (empty($id)) {
            $this->json(null, 400, '请提供学生ID');
        }
        
        // 获取学生信息
        $student = $this->studentModel->getStudentById($id);
        
        // 检查学生是否存在
        if (!$student) {
            $this->json(null, 404, '学生不存在');
        }
        
        // 开始事务
        $db = Database::getInstance();
        $db->beginTransaction();
        
        try {
            // 删除学生
            $this->studentModel->deleteStudent($id);
            
            // 删除用户
            $this->userModel->deleteUser($student['user_id']);
            
            // 提交事务
            $db->commit();
            
            // 返回成功响应
            $this->json(null, 200, '学生删除成功');
        } catch (Exception $e) {
            // 回滚事务
            $db->rollBack();
            
            // 返回错误响应
            $this->json(null, 500, '删除学生失败：' . $e->getMessage());
        }
    }
    
    /**
     * 学生详情页面
     * @param int $id 学生ID
     */
    public function view($id) {
        // 检查权限（只有管理员和教师可以查看学生详情）
        if ($_SESSION['user']['role'] === 'student' && $_SESSION['user']['id'] !== $id) {
            $this->redirect('error/forbidden');
        }
        
        // 获取学生信息
        $student = $this->studentModel->getStudentById($id);
        
        // 检查学生是否存在
        if (!$student) {
            $this->redirect('error/notFound');
        }
        
        // 渲染视图
        $this->render('view', ['student' => $student]);
    }
    
    /**
     * 学生个人资料页面
     */
    public function profile() {
        // 获取当前用户ID
        $userId = $_SESSION['user']['id'];
        
        // 获取学生信息
        $student = $this->studentModel->getStudentByUserId($userId);
        
        // 检查学生是否存在
        if (!$student) {
            $this->redirect('error/notFound');
        }
        
        // 渲染视图
        $this->render('profile', ['student' => $student]);
    }
    
    /**
     * 处理更新学生个人资料请求
     */
    public function updateProfile() {
        // 检查是否为POST请求
        if (!$this->isPost()) {
            $this->json(null, 400, '无效的请求方法');
        }
        
        // 获取当前用户ID
        $userId = $_SESSION['user']['id'];
        
        // 获取学生信息
        $student = $this->studentModel->getStudentByUserId($userId);
        
        // 检查学生是否存在
        if (!$student) {
            $this->json(null, 404, '学生不存在');
        }
        
        // 获取表单数据
        $name = $this->post('name');
        $email = $this->post('email');
        $password = $this->post('password');
        $phone = $this->post('phone');
        $parentName = $this->post('parent_name');
        $parentPhone = $this->post('parent_phone');
        
        // 验证数据
        if (empty($name) || empty($email)) {
            $this->json(null, 400, '请填写必填字段');
        }
        
        // 检查邮箱是否已被其他用户使用
        if ($email !== $_SESSION['user']['email'] && $this->userModel->emailExists($email)) {
            $this->json(null, 400, '邮箱已被使用');
        }
        
        // 开始事务
        $db = Database::getInstance();
        $db->beginTransaction();
        
        try {
            // 更新用户信息
            $userData = [
                'name' => $name,
                'email' => $email,
                'phone' => $phone
            ];
            
            // 如果提供了新密码，则更新密码
            if (!empty($password)) {
                $userData['password'] = password_hash($password, PASSWORD_DEFAULT);
            }
            
            $this->userModel->updateUser($userId, $userData);
            
            // 更新学生信息
            $this->studentModel->updateStudent($student['id'], [
                'parent_name' => $parentName,
                'parent_phone' => $parentPhone
            ]);
            
            // 提交事务
            $db->commit();
            
            // 更新会话中的用户信息
            $_SESSION['user']['name'] = $name;
            $_SESSION['user']['email'] = $email;
            
            // 返回成功响应
            $this->json(null, 200, '个人资料更新成功');
        } catch (Exception $e) {
            // 回滚事务
            $db->rollBack();
            
            // 返回错误响应
            $this->json(null, 500, '更新个人资料失败：' . $e->getMessage());
        }
    }
}