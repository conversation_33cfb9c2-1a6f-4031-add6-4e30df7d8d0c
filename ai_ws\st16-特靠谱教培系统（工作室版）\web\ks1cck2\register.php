<?php
require '_ks1.php';
@session_start();
if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    $username = $_POST['username'];
    $password = $_POST['password'];
    $wechat_name = $_POST['wechat_name'] ?? null;
    $wechat_openid = $_POST['wechat_openid'] ?? null;
    $email = $_POST['email'] ?? null;
    $phone = $_POST['phone'] ?? null;
    $public_account_name = $_POST['public_account_name'] ?? null;

    // 默认角色为 'guest'
    $role = 'guest';

    $password = md5($password. PASSWD_SALT);


    $sql = "INSERT INTO cck_users (username, password, role, wechat_name, wechat_openid, email, phone, public_account_name) 
            VALUES (?s, ?s, ?s, ?s, ?s, ?s, ?s, ?s)";
    $sql = prepare($sql, array($username, $password_hash, $role, $wechat_name, $wechat_openid, $email, $phone, $public_account_name));
  try {
    if (run_sql($sql)) {
         echo "注册成功。 <a href='login.php'>请登录</a>";
    } else {
        echo "Failed to register.";
    }
  } catch (PDOException $e) {
    echo "注册失败： " . $e->getMessage();
  }    
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册</title>
</head>
<body>
    <h1>注册</h1>
    <form method="post" action="register.php">
        <label for="username">用户名：</label>
        <input type="text" name="username" id="username" required><br>
        
        <label for="password">密码：</label>
        <input type="password" name="password" id="password" required><br>
        
        <label for="wechat_name">微信名称：</label>
        <input type="text" name="wechat_name" id="wechat_name"><br>
        
        <label for="wechat_openid">微信OpenID：</label>
        <input type="text" name="wechat_openid" id="wechat_openid"><br>
        
        <label for="email">邮箱：</label>
        <input type="email" name="email" id="email"><br>
        
        <label for="phone">手机号码：</label>
        <input type="tel" name="phone" id="phone"><br>
        
        <label for="public_account_name">公众号名称：</label>
        <input type="text" name="public_account_name" id="public_account_name"><br>
        
        <button type="submit">注册</button>
    </form>
</body>
</html>