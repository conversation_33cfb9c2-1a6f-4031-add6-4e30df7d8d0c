<?php

define('PRODUCTION_HOST', 'pubhelper.shdic.com');
$host = strtolower($_SERVER['HTTP_HOST']);
define("DEBUG_MODE", $host !== strtolower(PRODUCTION_HOST));
//define( "DEBUG_MODE" , true );

define( 'DS' , DIRECTORY_SEPARATOR );

define( 'APP_ROOT' , dirname( __FILE__ ) . DS  );
define( 'V_ROOT' , APP_ROOT . 'view'.DS );
define( 'P_ROOT' , APP_ROOT . 'php'.DS );


define( 'CACHE_ROOT' , APP_ROOT . 'temp'.DS.'cache'.DS );
define( 'ERROR_LOGS' , APP_ROOT . 'temp'.DS );

define( 'MAIN_APP_ROOT' , dirname( dirname( __FILE__ ) ) . DS );
define( 'ADM_ROOT' , MAIN_APP_ROOT . 'admin'.DS );
define( 'PLUGINS_ROOT' , MAIN_APP_ROOT . '_plugins_by_ks'.DS );

require P_ROOT.'common/init.php';


define( 'MINI_APP_ROOT' , dirname( __FILE__ ) . DS  );
define( 'MINI_V_ROOT' , MINI_APP_ROOT . 'view'.DS );


// 自动加载器函数
function autoload($class_name) {
    // 假设类文件都在 P_ROOT 目录下
    $file_path =  dirname( __FILE__ ).'/php/' . $class_name . '.class.php';
    
    // 检查文件是否存在
    if (file_exists($file_path)) {
        require_once $file_path;
    } else {
        // 可以在这里记录日志或进行其他错误处理
        $errorMessage = "Class file not found: $file_path" .__METHOD__;
        error_log($errorMessage);
        throw new Exception($errorMessage);
    }
}

// 注册自动加载器
spl_autoload_register('autoload');


$time_stamp=strtotime("now");   //time stamp
$max_cache_time=900;


define( "MYSQL_ASSOC" , MYSQLI_ASSOC );

$config = config('DB_CONNECT');

// 配置文件
$GLOBALS['config']['db']['db_host'] = $config['host'];
$GLOBALS['config']['db']['db_port'] = $config['port'];
$GLOBALS['config']['db']['db_user'] = $config['user'];
$GLOBALS['config']['db']['db_password'] = $config['pass'];
$GLOBALS['config']['db']['db_name'] = $config['dbname'];


// 迅捷函数
function v( $str )
{
    return isset( $_REQUEST[$str] ) ? $_REQUEST[$str] : false;
}
function z( $str )
{
    return strip_tags( $str );
}
function c( $str )
{
    return isset( $GLOBALS['config'][$str] ) ? $GLOBALS['config'][$str] : false;
}
function g( $str )
{
    return isset( $GLOBALS[$str] ) ? $GLOBALS[$str] : false;
}
function t( $str )
{
    return trim($str);
}
function u( $str )
{
    return urlencode( $str );
}


// 数据库相关 Mysqli 实现
// db functions
function db( $host = null , $port = null , $user = null , $password = null , $db_name = null )
{
	$db_key = MD5( $host .'-'. $port .'-'. $user .'-'. $password .'-'. $db_name  );

	if( !isset( $GLOBALS['LP_'.$db_key] ) )
	{
		$db_config = $GLOBALS['config']['db'];

		if( $host == null ) $host = $db_config['db_host'];
		if( $port == null ) $port = $db_config['db_port'];
		if( $user == null ) $user = $db_config['db_user'];
		if( $password == null ) $password = $db_config['db_password'];
		if( $db_name == null ) $db_name = $db_config['db_name'];

/*	echo 'db_name='.$db_name.'<br>'.PHP_EOL;
	echo 'user='.$user.'<br>'.PHP_EOL;
	echo 'password='.$password.'<br>'.PHP_EOL;
*/

		if( !$GLOBALS['LP_'.$db_key] = mysqli_connect( $host , $user , $password , '' , $port ) )
		{
			//
			echo 'can\'t connect to database';
			return false;
		}
		else
		{
			if( $db_name != '' )
			{
				if( !mysqli_select_db( $GLOBALS['LP_'.$db_key] , $db_name ) )
				{
					echo 'can\'t select database ' . $db_name ;
					return false;
				}
			}
		}

		mysqli_query( $GLOBALS['LP_'.$db_key] , "SET NAMES 'UTF8'"  );
	}

	return $GLOBALS['LP_'.$db_key];
}
function s( $str , $db = NULL )
{
	if( $db == NULL ) $db = db();
	return  mysqli_real_escape_string( $db , $str )  ;

}
// $sql = "SELECT * FROM `user` WHERE `name` = ?s AND `id` = ?i LIMIT 1 "
function prepare( $sql , $array )
{

	foreach( $array as $k=>$v )
		$array[$k] = s($v );

	$reg = '/\?([is])/i';
	$sql = preg_replace_callback( $reg , 'prepair_string' , $sql  );
	$count = count( $array );
	for( $i = 0 ; $i < $count; $i++ )
	{
		$str[] = '$array[' .$i . ']';
	}

	$statement = '$sql = sprintf( $sql , ' . join( ',' , $str ) . ' );';
	eval( $statement );
	return $sql;

}
function prepair_string( $matches )
{
	if( $matches[1] == 's' ) return "'%s'";
	if( $matches[1] == 'i' ) return "'%d'";
}
function get_data( $sql , $db = NULL )
{
	if( $db == NULL ) $db = db();

	$GLOBALS['LP_LAST_SQL'] = $sql;

	if(!isset($GLOBALS['LP_SQL_QUERIES'])){
		$GLOBALS['LP_SQL_QUERIES']=1;
	}else{
		++$GLOBALS['LP_SQL_QUERIES'];	
	}
	
	
	$data = Array();
	$i = 0;
	$result = mysqli_query( $db , $sql );

	if( mysqli_errno( $db ) != 0 )
		echo mysqli_error( $db ) .' ' . $sql;

	while( $Array = mysqli_fetch_array($result, MYSQL_ASSOC ) )
	{
		$data[$i++] = $Array;
	}

	if( mysqli_errno( $db ) != 0 )
		echo mysqli_error( $db ) .' ' . $sql;

	mysqli_free_result($result);
	if( count( $data ) > 0 )
		return $data;
	else
		return false;
}
function get_line( $sql , $db = NULL )
{
	$data = get_data( $sql , $db  );
	return @reset($data);
}
function get_var( $sql , $db = NULL )
{
	$data = get_line( $sql , $db );
	return $data[ @reset(@array_keys( $data )) ];
}
function last_id( $db = NULL )
{
	if( $db == NULL ) $db = db();
	return get_var( "SELECT LAST_INSERT_ID() " , $db );
}
function run_sql( $sql , $db = NULL )
{
	if( $db == NULL ) $db = db();
	$GLOBALS['LP_LAST_SQL'] = $sql;
	if(isset($GLOBALS['LP_SQL_QUERIES'])){
		++$GLOBALS['LP_SQL_QUERIES'];
	}else{
		$GLOBALS['LP_SQL_QUERIES']=1;
	}	
	return mysqli_query( $db , $sql  );
}
function db_errno( $db = NULL )
{
	if( $db == NULL ) $db = db();
	return mysqli_errno( $db );
}
function db_error( $db = NULL )
{
	if( $db == NULL ) $db = db();
	return mysqli_error( $db );
}
function last_error()
{
	if( isset( $GLOBALS['LP_DB_LAST_ERROR'] ) )
	return $GLOBALS['LP_DB_LAST_ERROR'];
}
function close_db( $db = NULL )
{
	if( $db == NULL )
		$db = $GLOBALS['LP_DB'];

	unset( $GLOBALS['LP_DB'] );
	mysqli_close( $db );
}

function send_json( $obj )
{
    header("Access-Control-Allow-Origin: *");
    header("Content-Type application/json");
    echo json_encode( $obj , JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT );
}
function send_result( $data ,$msg=null)
{
    $ret['code'] = 0 ;
    $ret['message'] = '' ;
    if(!empty($msg)){
    	$ret['message']=$msg;
    }
    $ret['data'] = $data ;
    send_json( $ret );
    exit;
}
function send_error( $info )
{
	$error['code'] = -1;
	$error['message'] = $info ;
	$error['js'] = 'alert("' . $info . '")' ;
    send_json($error);
    exit;
}

function sql_affe_rows( $db = NULL )
{
	if( $db == NULL ) $db = db();	
	return @mysqli_affected_rows( $db);
}
