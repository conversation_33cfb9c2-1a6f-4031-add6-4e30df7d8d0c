<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册/编辑群组信息</title>
    <link rel="stylesheet" href="res/ks1table.css">    
</head>
<body>
<?php
require '_ks1.php';
@session_start();

if (!isset($_SESSION[SESSION_KEY]['user']['id']) || !in_array($_SESSION[SESSION_KEY]['user']['role'], ['user', 'admin'])) {
    header('Location: login.php');
    exit;
}

$group_id = isset($_GET['group_id']) ? intval($_GET['group_id']) : 0;
$group_data = [];

if ($group_id > 0) {
    // 获取群组信息用于编辑
    $sql = "SELECT * FROM cck1_groups WHERE group_id = ?i LIMIT 1";
    $sql = prepare($sql, array($group_id));
    $group_data = get_line($sql);
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // 处理表单提交
    $group_name = trim($_POST['group_name']);
    $password = trim($_POST['password']);
    $description = trim($_POST['description']);
    $owner_id = intval($_POST['owner_id']);

    // 校验
    if (empty($group_name)) {
        $error = "群组名称不能为空！";
    } elseif ($group_id > 0 && $group_data['group_name'] != $group_name) {
        // 检查群组名称是否重复
        $sql = "SELECT COUNT(*) AS count FROM cck1_groups WHERE group_name = ?s";
        $sql = prepare($sql, array($group_name));
        $result = get_line($sql);
        if ($result['count'] > 0) {
            $error = "群组名称已存在，请选择其他名称！";
        }
    }

    if (!isset($error)) {
        // 保存数据
        if ($group_id > 0) {
            // 更新群组信息
            $sql = "UPDATE cck1_groups SET group_name = ?s, password = ?s, description = ?s, owner_id = ?i WHERE group_id = ?i";
            $sql = prepare($sql, array($group_name, $password, $description, $owner_id, $group_id));
        } else {
            // 新增群组信息
            $sql = "INSERT INTO cck1_groups (group_name, password, description, owner_id) VALUES (?s, ?s, ?s, ?i)";
            $sql = prepare($sql, array($group_name, $password, $description, $owner_id));
        }

        if (run_sql($sql)) {
            header('Location: groups_list.php'); // 跳转到群组列表页面
            exit;
        } else {
            $error = "保存失败，请稍后再试！ #1001" . $sql ;
        }
    }
}
?>

<link rel="stylesheet" href="<?php echo $css_file; ?>">
<div style="text-align: center;">
    <h1><?php echo $group_id > 0 ? '编辑' : '注册'; ?>群组信息</h1>
    <?php if (isset($error)): ?>
        <p style="color: red;"><?php echo $error; ?></p>
    <?php endif; ?>
    <form method="post">
        <label for="group_name">群组名称：</label>
        <input type="text" id="group_name" name="group_name" value="<?php echo $group_data['group_name'] ?? ''; ?>" required><br><br>
        
        <label for="password">群组口令：</label>
        <input type="text" id="password" name="password" value="<?php echo $group_data['password'] ?? ''; ?>"><br><br>
        
        <label for="description">群组介绍：</label>
        <textarea id="description" name="description"><?php echo $group_data['description'] ?? ''; ?></textarea><br><br>
        
        <label for="owner_id">群主用户ID：</label>
        <input type="number" id="owner_id" name="owner_id" value="<?php echo $group_data['owner_id'] ?? ''; ?>" required><br><br>
        
        <input type="submit" value="<?php echo $group_id > 0 ? '更新' : '注册'; ?>">
    </form>
    <div>
        <?php echo $nav_html; ?>
    </div>
</div>
</body>
</html>