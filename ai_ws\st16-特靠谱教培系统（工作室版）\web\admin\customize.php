<?php
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_WARNING);
require_once('check_admin.php');
require_once('mysqlconn.php');
$row = $conn->get('expireday','*',['id'=>1]);
$smday = $row["smday"];
$getday = $row["getday"];
$delayday = $row["delayday"];

$row = $conn->get('main','*',['id'=>1]);
$caseidtemplate = $row["caseidtemplate"];
$showofficer = $row["showofficer"];
if (isset($_REQUEST["action"]) && ($_REQUEST["action"] == "smday")) {
    $conn->update('expireday',['smday'=>$_REQUEST["smday"],'getday'=>$_REQUEST["getday"],'delayday'=>$_REQUEST["delayday"]],['id'=>1]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');</script>";
    exit;
}
if (isset($_REQUEST["action"]) && ($_REQUEST["action"] == "caseid")) {
    $conn->update('main',['caseidtemplate'=>$_REQUEST["caseidtemplateshortname"] . "|" . $_REQUEST["caseidtemplatetype"]],['id'=>1]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');</script>";
    exit;
}
if (isset($_REQUEST["action"]) && ($_REQUEST["action"] == "remotehelp")) {
    file_put_contents("isneedhelp.php", $_REQUEST["open"]);
    echo "<html><head><meta charset=utf-8></head><body><script>window.parent.location.reload();</script>";
    exit;
}
if (isset($_REQUEST["action"]) && ($_REQUEST["action"] == "showofficer")) {
    $showofficer = ($_REQUEST["showofficer"]=="on")?1:0;
    $conn->update('main',['showofficer'=>$showofficer],['id'=>1]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');</script>";
    exit;
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>定制化参数设置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css"/>

    <meta name="description" content="overview &amp; stats"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon"/>

    <link rel="stylesheet" href="/bootstrap/bootstrap.min.css"/>
    <link rel="stylesheet" href="/bootstrap/font-awesome.min.css"/>

    <link rel="stylesheet" href="/bootstrap/ace.min.css"/>
    <script src="/bootstrap/jquery.min.js"></script>
    <!--[if lte IE 9]>
    <link rel="stylesheet" href="/bootstrap/ace-part2.min.css"/>
    <![endif]-->
    <link rel="stylesheet" href="/bootstrap/ace-skins.min.css"/>
    <link rel="stylesheet" href="/bootstrap/ace-rtl.min.css"/>
    <!--[if lte IE 9]>
    <link rel="stylesheet" href="/bootstrap/ace-ie.min.css"/>
    <![endif]-->
    <script src="/bootstrap/ace-extra.min.js"></script>
    <!--[if lte IE 8]>
    <script src="/bootstrap/html5shiv.min.js"></script>
    <script src="/bootstrap/respond.min.js"></script>
    <![endif]-->
    <script src="/bootstrap/bootstrap.min.js"></script>
    <!--[if lte IE 8]>
    <script src="/bootstrap/excanvas.min.js"></script>
    <![endif]-->
    <script src="/bootstrap/jquery-ui.custom.min.js"></script>
    <script src="/bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="/bootstrap/jquery.easypiechart.min.js"></script>
    <script src="/bootstrap/jquery.sparkline.min.js"></script>
    <!-- 	<script src="/bootstrap/jquery.flot.min.js"></script> -->
    <!-- 	<script src="/bootstrap/jquery.flot.pie.min.js"></script> -->
    <!-- 	<script src="/bootstrap/jquery.flot.resize.min.js"></script> -->
    <script src="/bootstrap/ace-elements.min.js"></script>
    <script src="/bootstrap/ace.min.js"></script>

    <link rel="stylesheet" href="/bootstrap/ace.onpage-help.css"/>
    <link rel="stylesheet" href="/bootstrap/sunburst.css"/>
    <script type="text/javascript"> ace.vars['base'] = '.'; </script>
    <script src="/bootstrap/elements.onpage-help.js"></script>
    <script src="/bootstrap/ace.onpage-help.js"></script>
    <script src="/bootstrap/rainbow.js"></script>
    <script src="/bootstrap/generic.js"></script>
    <script src="/bootstrap/html.js"></script>
    <script src="/bootstrap/css.js"></script>
    <script src="/bootstrap/javascript.js"></script>

    <script type="text/javascript" src="/bootstrap/common.js"></script>
    <script type="text/javascript" src="/bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>
<body class="no-skin" style="font-family:'微软雅黑'">

<div id="onthego"><img border=0 src=/img/loading.gif></div>

<div class="main-container" id="main-container">
    <div class="breadcrumbs" id="breadcrumbs">
        <script type="text/javascript">
            try {
                ace.settings.check('breadcrumbs', 'fixed')
            } catch (e) {
            }
        </script>
        <ul class="breadcrumb" style="padding-top:5px;">
            <li><i class="ace-icon fa fa-print"></i> 系统管理</li>
            <li class="active">定制化参数设置</li>
        </ul>
    </div>
    <div class="main-content" style="margin:10px">

        <div class="page-content-area"
             style="float:left;margin:10px;display:inline;width:450px;height:350px;background:#F5F5F5;border:1px solid silver;border-radius:10px;text-align:center;font-size:16px;padding:10px;">
            <div style="margin:10px;"><b>远程协助设置</b></div>
            <div class="row">
                <div class="space-6"></div>
                <div class="col-xs-12">
                    <form class="form-horizontal" target=_blank>
                        <input type=hidden name="action" value="remotehelp">

                        <div class="form-group" style="margin:23px 0;">
                            <label class="col-lg-6 control-label">访问外网：</label>

                            <div class="col-lg-4" style="text-align:left;">
                                <span id='onlinestatus' style='line-height:30px;'><span style='color:grey;'>检测中...</span></span>
                            </div>
                        </div>
                        <div class="form-group" style="margin:23px 0;">
                            <label class="col-lg-6 control-label">服务状态：</label>

                            <div class="col-lg-4" style="text-align:left;">
                                <?php
                                $isneedhelp = file_get_contents('isneedhelp.php', true);
                                if ($isneedhelp == "0") {
                                    echo "<span style='color:red;line-height:30px;'>关闭</span>";
                                } else {
                                    echo "<span style='color:green;line-height:30px;'>开启</span>";
                                }
                                ?>
                            </div>
                        </div>
                        <div class="form-group" align="center" style="padding:10px;">
                            <?php
                            if ($html && ($isneedhelp == "0")) {
                                echo '<button type="button" class="btn btn-primary" onclick="document.getElementById(\'temp\').src=\'customize.php?action=remotehelp&open=1\';">开启</button>';
                            } else if ($isneedhelp == "1") {
                                echo '<button type="button" class="btn btn-primary" onclick="document.getElementById(\'temp\').src=\'customize.php?action=remotehelp&open=0\';">关闭</button>';
                            } else {
                                echo '<button type="button" disabled class="btn btn-primary">无法开启</button>';
                            }
                            ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="page-content-area"
             style="float:left;margin:10px;display:inline;width:450px;height:350px;background:#F5F5F5;border:1px solid silver;border-radius:10px;text-align:center;font-size:16px;padding:10px;">
            <div style="margin:10px;"><b>案号版式设置</b></div>
            <div class="row">
                <div class="space-6"></div>
                <div class="col-xs-12">
                    <form class="form-horizontal" target=temp>
                        <input type=hidden name="action" value="caseid">

                        <div class="form-group" style="margin:23px 0;">
                            <label class="col-lg-4 control-label">法院简称：</label>

                            <div class="col-lg-6">
                                <input type="text" id="caseidtemplateshortname" name="caseidtemplateshortname"
                                       value="<?php echo explode("|",$caseidtemplate)[0]; ?>" class="bg-focus form-control">
                            </div>
                        </div>
                        <div class="form-group" style="margin:23px 0;">
                            <label class="col-lg-4 control-label">案件类型：</label>

                            <div class="col-lg-6">
                                <input type="text" id="caseidtemplatetype" name="caseidtemplatetype"
                                       value="<?php echo explode("|",$caseidtemplate)[1]; ?>" class="bg-focus form-control">
                            </div>
                        </div>
                        <div class="form-group" align="center" style="padding:10px;">
                            <button type="submit" class="btn btn-primary">确认设置</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="page-content-area"
             style="float:left;margin:10px;display:inline;width:450px;height:350px;background:#F5F5F5;border:1px solid silver;border-radius:10px;text-align:center;font-size:16px;padding:10px;">
            <div style="margin:10px;"><b>助理显示设置</b></div>
            <div class="row">
                <div class="space-6"></div>
                <div class="col-xs-12">
                    <form class="form-horizontal" target=temp>
                        <input type=hidden name="action" value="showofficer">

                        <div class="form-group" style="margin:23px 0;">
                            <div class="col-lg-12">
                                <input type="checkbox" style="vertical-align:middle;width:15px;height:15px;margin-top:-3px;" id="showofficer" name="showofficer" <?php echo ($showofficer?"checked":""); ?>> 投递页面显示助理信息（用于接收短信）
                            </div>
                        </div>
                        <div class="form-group" align="center" style="padding:10px;">
                            <button type="submit" class="btn btn-primary">确认设置</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="page-content-area"
             style="float:left;display:inline;margin:10px;width:450px;height:350px;background:#F5F5F5;border:1px solid silver;border-radius:10px;text-align:center;font-size:16px;padding:10px;">
            <div style="margin:10px;"><b>超时未取设置</b></div>
            <div class="row">
                <div class="space-6"></div>
                <div class="col-xs-12">
                    <form class="form-horizontal" target=temp>
                        <input type=hidden name="action" value="smday">

                        <div class="form-group" style="margin:23px 0;">
                            <label class="col-lg-7 control-label">短信提醒超时天数：</label>

                            <div class="col-lg-3">
                                <input type="text" id="smday" name="smday" value="<?php echo $smday; ?>"
                                       class="bg-focus form-control">
                            </div>
                        </div>
                        <div class="form-group" style="margin:23px 0;">
                            <label class="col-lg-7 control-label">提醒法官取出超时天数：</label>

                            <div class="col-lg-3">
                                <input type="text" id="getday" name="getday" value="<?php echo $getday; ?>"
                                       class="bg-focus form-control">
                            </div>
                        </div>
                        <div class="form-group" style="margin:23px 0;">
                            <label class="col-lg-7 control-label">法官每次最多延期天数：</label>

                            <div class="col-lg-3">
                                <input type="text" id="delayday" name="delayday" value="<?php echo $delayday; ?>"
                                       class="bg-focus form-control">
                            </div>
                        </div>
                        <div class="form-group" align="center" style="padding:10px;">
                            <button type="submit" class="btn btn-primary">确认设置</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        

    </div>
</div>
<iframe style="display:none;" id=temp name=temp></iframe>
<script>
	function createXMLHttpRequest() {	//创建xmlhttp对象（AJAX技术）
      if (window.ActiveXObject) {
          return new ActiveXObject("Microsoft.XMLHTTP");
      } else if (window.XMLHttpRequest) {
          return new XMLHttpRequest();
      }
  }

  var xmlHttp = createXMLHttpRequest();
	xmlHttp.open('GET', 'onlinecheck.php' , true);
  xmlHttp.onreadystatechange = handleStateChange;
  xmlHttp.send();
  function handleStateChange() {
        if (xmlHttp.readyState === 4) {
            if (xmlHttp.status == 200) {
                if (xmlHttp.responseText=="true"){
                    document.getElementById("onlinestatus").innerHTML="<span style='color:green'>成功</span>";
                } else {
                    document.getElementById("onlinestatus").innerHTML="<span style='color:red'>失败</span>";
                }
            } else {
                alert("无法获取在线状态");
            }
        }
    }
</script>
</body>
</html>
						