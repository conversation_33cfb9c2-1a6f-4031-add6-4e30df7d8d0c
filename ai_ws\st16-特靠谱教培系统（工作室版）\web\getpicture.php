<?php
error_reporting(E_ALL ^ E_WARNING);
header("Access-Control-Allow-Origin: *");
set_time_limit(0);
require_once('admin/mysqlconn.php');
$alistablediffusionfinish = false;
$starttime = time();
$proxyaddress = $conn->get('main', 'proxyaddress', ['id' => 1]);
$row = $conn->get('user', '*', ['rndstr' => $_GET["user"]]);
if (empty($row)) {
    echo '{"error":{"code":"invalid_user","message":""}}';
    exit;
}
$userid = $row["id"];
$quota = $row["quota"];
$conversationid = explode(",", $row["lastquestion"])[0];
$postdata = substr($row["lastquestion"], strlen($conversationid) + 1);
$postdatajson = json_decode($postdata, true);
$lastmodelid = $row["lastmodelid"];
$row = $conn->get('model', '*', ['id' => $lastmodelid]);
$modelprice = $row["modelprice"];
$modeltype = $row["modeltype"];
$modelvalue = $row["modelvalue"];
$timestampfortengxun = time();
if ($quota < $modelprice) {
    echo '{"error":{"code":"out_of_money","message":""}}';
    exit;
}
$responsedata = "";
$OPENAI_API_KEY = "";
$isimage = $_GET["isimage"];

if (($isimage != '1') && ($isimage != '2')) {
    echo '{"error":{"code":"unknown","message":"本文件仅限生成图片时调用"}}';
    exit;
}

$row = $conn->get("apikey", "*", ["AND" => ["isvalid" => true, "keytype" => $lastmodelid], "ORDER" => ["lasttime", "id"]]);
if (empty($row)) {
    echo '{"error":{"code":"no_valid_apikey","message":""}}';
    exit;
}
$OPENAI_API_KEY = $row["apikey"];
$apikeyid = $row["id"];
$apiaddress = $row["apiaddress"];
$oldapiaddress = $row["apiaddress"];

function get_baidu_accesscode()
{
    global $conn, $oldapiaddress, $OPENAI_API_KEY, $apikeyid;
    $apikey = explode(",", $OPENAI_API_KEY);
    $newtoken = json_decode(file_get_contents($oldapiaddress . "/oauth/2.0/token?grant_type=client_credentials&client_id=" . $apikey[0] . "&client_secret=" . $apikey[1]))->access_token;
    $conn->update('apikey', ['apikey' => $apikey[0] . "," . $apikey[1] . "," . $newtoken], ['id' => $apikeyid]);
    return $newtoken;
}

function jwt_encode($payload, $secret, $algorithm, $headers)
{
    $header = base64_encode(json_encode($headers));
    $payload = base64_encode(json_encode($payload));
    $signature = hash_hmac($algorithm, "$header.$payload", $secret, true);
    $signature = base64_encode($signature);
    return "$header.$payload.$signature";
}

function get_qinghuazhipu_token($apikey, $exp_seconds)
{
    try {
        list($id, $secret) = explode(".", $apikey);
    } catch (Exception $e) {
        throw new Exception("invalid apikey", $e);
    }

    $payload = array(
        "api_key" => $id,
        "exp" => round(microtime(true) * 1000) + $exp_seconds * 1000,
        "timestamp" => round(microtime(true) * 1000),
    );

    return jwt_encode(
        $payload,
        $secret,
        "sha256",
        array("alg" => "HS256", "sign_type" => "SIGN")
    );
}

function get_tencenthunyuan_token()
{
    global $OPENAI_API_KEY, $timestampfortengxun, $postdata;
    $apikey = explode(",", $OPENAI_API_KEY);
    $secretId = $apikey[0];
    $secretKey = $apikey[1];
    $algorithm = "TC3-HMAC-SHA256";
    $canonicalHeaders = implode("\n", ["content-type:application/json; charset=utf-8", "host:hunyuan.tencentcloudapi.com", "x-tc-action:texttoimagelite", ""]);
    $signedHeaders = implode(";", ["content-type", "host", "x-tc-action",]);
    $hashedRequestPayload = hash("SHA256", $postdata);
    $canonicalRequest = "POST\n/\n\n" . $canonicalHeaders . "\n" . $signedHeaders . "\n" . $hashedRequestPayload;
    $date = gmdate("Y-m-d", $timestampfortengxun);
    $credentialScope = $date . "/hunyuan/tc3_request";
    $hashedCanonicalRequest = hash("SHA256", $canonicalRequest);
    $stringToSign = $algorithm . "\n" . $timestampfortengxun . "\n" . $credentialScope . "\n" . $hashedCanonicalRequest;
    $secretDate = hash_hmac("SHA256", $date, "TC3" . $secretKey, true);
    $secretService = hash_hmac("SHA256", "hunyuan", $secretDate, true);
    $secretSigning = hash_hmac("SHA256", "tc3_request", $secretService, true);
    $signature = hash_hmac("SHA256", $stringToSign, $secretSigning);
    $authorization = $algorithm . " Credential=" . $secretId . "/" . $credentialScope . ", SignedHeaders=" . $signedHeaders . ", Signature=" . $signature;
    return $authorization;
}
function assembleAuthUrl($addr, $apiSecret, $apiKey)
{
    $ul = parse_url($addr);
    if (($apiKey . $apiSecret == "") || ($ul === false)) {
        return $addr;
    }
    $timestamp = time();
    $rfc1123_format = gmdate("D, d M Y H:i:s \G\M\T", $timestamp);
    $signString = array("host: " . $ul["host"], "date: " . $rfc1123_format, "POST " . $ul["path"] . " HTTP/1.1");
    $sgin = implode("\n", $signString);
    $sha = hash_hmac('sha256', $sgin, $apiSecret, true);
    $signature_sha_base64 = base64_encode($sha);
    $authUrl = 'api_key="' . $apiKey . '", algorithm="hmac-sha256", headers="host date request-line", signature="' . $signature_sha_base64 . '"';
    $authAddr = $addr . '?' . http_build_query(
        array(
            'host' => $ul['host'],
            'date' => $rfc1123_format,
            'authorization' => base64_encode($authUrl),
        )
    );
    return $authAddr;
}

$headers = [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $OPENAI_API_KEY
];
if ($modeltype == "通义千问") {
    $apiaddress .= '/api/v1/services/aigc/text2image/image-synthesis';
    $headers = [
        'Accept: application/json',
        'Content-Type: application/json',
        'Authorization: Bearer ' . $OPENAI_API_KEY,
        'X-DashScope-Async: enable'
    ];
} else if (($modeltype == "文心千帆") && ($modelvalue == "Stable-Diffusion-XL")) {
    $apikeyarray = explode(",", $OPENAI_API_KEY);
    if (count($apikeyarray) == 2) {
        $accesstoken = get_baidu_accesscode();
    } else {
        $accesstoken = $apikeyarray[2];
    }
    $apiaddress .= '/rpc/2.0/ai_custom/v1/wenxinworkshop/text2image/sd_xl?access_token=' . $accesstoken;
    $headers = [
        'Content-Type: application/json'
    ];
} else if (($modeltype == "腾讯混元") && ($modelvalue == "TextToImageLite")) {
    $authorization = get_tencenthunyuan_token();
    $headers = [
        "Authorization: " . $authorization,
        "Content-Type: application/json; charset=utf-8",
        "Host: hunyuan.tencentcloudapi.com",
        "X-TC-Action: TextToImageLite",
        "X-TC-Timestamp: " . $timestampfortengxun,
        "X-TC-Version: 2023-09-01",
        "X-TC-Region: ap-guangzhou"
    ];
} else if (($modeltype == "清华智谱") && ($modelvalue == "cogview-3")) {
    $apiaddress .= '/api/paas/v4/images/generations';
    $token = get_qinghuazhipu_token($OPENAI_API_KEY, 3600);
    $headers = [
        'Accept: text/event-stream',
        'Content-Type: application/json',
        'Authorization: ' . $token
    ];
} else if (($modeltype == "讯飞星火")) {
    $apikeyarray = explode(",", $OPENAI_API_KEY);
    $authUrl = assembleAuthUrl($apiaddress, $apikeyarray[1], $apikeyarray[2]);
    $headers = [
        'Accept: application/json',
        'Content-Type: application/json;charset=UTF-8'
    ];
    $apiaddress = $authUrl;
    $postdatajson["header"]["app_id"] = $apikeyarray[0];
    $postdata = json_encode($postdatajson);
} else if (($modeltype == "360智脑")) {
    $apiaddress .= '/v1/images/text2img';
} else if (($isimage == "2") && ($modeltype != "stablediffusion")) {
    $apiaddress .= '/v1/images/variations';
    $headers = [
        'Authorization: Bearer ' . $OPENAI_API_KEY
    ];
} else if ($modeltype != "stablediffusion") {
    $apiaddress .= '/v1/images/generations';
}

$conn->update('apikey', ['lasttime' => date('Y-m-d H:i:s')], ['id' => $apikeyid]);

$ch = curl_init();
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
curl_setopt($ch, CURLOPT_URL, $apiaddress);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_POST, 1);
if ($isimage == "2") {
    if ($modeltype == "stablediffusion") {
        $postdata = json_encode(
            array(
                'url' => $postdatajson["url"],
                'prompt' => $postdatajson["prompt"]
            )
        );
    } else {
        $imageUrl = $postdatajson["url"];
        $imageData = file_get_contents($imageUrl);
        // 创建临时文件并写入图片数据
        $tmpfname = tempnam(sys_get_temp_dir(), 'tmp');
        file_put_contents($tmpfname, $imageData);
        $cfile = new CURLFile($tmpfname, 'image/png', 'testpic');
        $postdata = array(
            'image' => $cfile,
            'model' => $postdatajson["model"],
            'n' => 1,
            'size' => '1024x1024'
        );
    }
}
curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 300);
curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_AUTOREFERER, true);
if (!empty($proxyaddress)) {
    curl_setopt($ch, CURLOPT_PROXY, $proxyaddress);
}
$responsedata = curl_exec($ch);
$headerstr = implode("|||", $headers);
error_log(date('Y-m-d H:i:s') . " Header: {$headerstr}\n APIAddress: {$apiaddress}\n Postdata: {$postdata}", 3, "pic.log");
error_log(date('Y-m-d H:i:s') . " Response: {$responsedata}\n", 3, "pic.log");
curl_close($ch);
if (($isimage == "2") && ($modeltype != "stablediffusion")) {
    // 删除临时文件
    unlink($tmpfname);
}

if (empty($responsedata)) {
    echo '{"error":{"code":"wrong_api_address","message":"API接口地址配置错误，请联系网址管理员"}}';

    exit;
}
$complete = json_decode($responsedata);
if (isset($complete->error)) {
    $errcode = "";
    $errmsg = "";
    if (isset($complete->error->code)) {
        $errcode = $complete->error->code;
    }
    if (isset($complete->error->message)) {
        $errmsg = $complete->error->message;
    }
    if (strpos($errmsg, "Rate limit reached") === 0) { //访问频率超限错误返回的code为空，特殊处理一下
        $errcode = "rate_limit_reached";
    }
    if (strpos($errmsg, "Your access was terminated") === 0) { //违规使用，被封禁，特殊处理一下
        $errcode = "access_terminated";
    }
    if (strpos($errmsg, "You didn't provide an API key") === 0) { //未提供API-KEY
        $errcode = "no_api_key";
    }
    if (strpos($errmsg, "You exceeded your current quota") === 0) { //API-KEY余额不足
        $errcode = "insufficient_quota";
    }
    if (strpos($errmsg, "That model is currently overloaded") === 0) { //OpenAI模型超负荷
        $errcode = "model_overloaded";
    }
    if (strpos($complete->error->message, "The server had an error") === 0) { //OpenAI服务器超负荷
        $errcode = "server_overloaded";
    }
    if (strpos($complete->error->message, "Something went wrong with your generation") === 0) { //上传的图片不合法
        $errcode = "picture_not_allowed";
    }
    if (($errcode == "access_terminated") || ($errcode == "insufficient_quota") || ($errcode == "invalid_api_key") || ($errcode == "account_deactivated")) {
        $conn->update('apikey', ['isvalid' => 0, 'lasttime' => date('Y-m-d H:i:s'), 'errmsg' => $errmsg], ['id' => $apikeyid]);
    }
    $questionarr = json_decode($postdata, true);
    if ($isimage == "2") {
        $goodquestion = '![IMG](' . $postdatajson["url"] . ')';
        if ($modeltype == "stablediffusion") {
            $goodquestion .= addslashes($questionarr['prompt']);
        }
    } else {
        $goodquestion = addslashes($questionarr['prompt']);
    }
    $goodanswer = addslashes(trim($responsedata));
    $sql = "INSERT INTO errorlog (question,errmsg,conversationid,modelid,realtime,userid,apikey) VALUES ('" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",'" . $OPENAI_API_KEY . "')";
    $complete->error->code = $errcode;
    $responsedata = json_encode($complete);
} else if (isset($complete->error_code)) { //百度问心千帆错误提示
    $errcode = $complete->error_code;
    $errmsg = $complete->error_msg;
    if ((($errcode == 110) || ($errcode == 111))) {
        $accesstoken = get_baidu_accesscode();
    }
    $questionarr = json_decode($postdata, true);
    $goodquestion = addslashes($questionarr['prompt']);
    $goodanswer = addslashes(trim($responsedata));
    $sql = "INSERT INTO errorlog (question,errmsg,conversationid,modelid,realtime,userid,apikey) VALUES ('" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",'" . $OPENAI_API_KEY . "')";
    $newcomplete = ["error" => ""];
    $newcomplete["error"] = ["code" => $errcode, "message" => $errmsg];
    $responsedata = json_encode($newcomplete);
} else if (isset($complete->detail)) {
    $questionarr = json_decode($postdata, true);
    $goodquestion = addslashes($questionarr['prompt']);
    $goodanswer = addslashes(trim($responsedata));
    $sql = "INSERT INTO errorlog (question,errmsg,conversationid,modelid,realtime,userid,apikey) VALUES ('" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",'" . $OPENAI_API_KEY . "')";
    $newcomplete = ["error" => ""];
    $newcomplete["error"] = ["code" => "api_error", "message" => $complete->detail];
    $responsedata = json_encode($newcomplete);
} else if (isset($complete->data)) {
    $conn->update("user", ["quota[-]" => $modelprice, "credits[+]" => $modelprice, "questioncount[+]" => 1], ["id" => $userid]);
    $answer = json_decode($responsedata, true);
    $questionarr = json_decode($postdata, true);
    if ($isimage == "2") {
        $goodquestion = '![IMG](' . $postdatajson["url"] . ')';
        if ($modeltype == "stablediffusion") {
            $goodquestion .= addslashes($questionarr['prompt']);
        }
    } else {
        $goodquestion = addslashes($questionarr['prompt']);
    }
    if (($modeltype == "文心千帆") && ($modelvalue == "Stable-Diffusion-XL")) {
        $row = $conn->get('main', '*', ['id' => 1]);
        $imagesiteurl = $row["imagesiteurl"];
        $imagesitetoken = $row["imagesitetoken"];

        $timestamp = file_get_contents($imagesiteurl . "?action=getnonce");
        $sign = md5(md5($timestamp) . $imagesitetoken);
        $imageBase64 = $complete->data[0]->b64_image;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_URL, $imagesiteurl);
        curl_setopt($ch, CURLOPT_POST, true);
        $postFields = [
            'action' => 'upload',
            'nonce' => $timestamp,
            'sign' => $sign,
        ];
        // 将base64编码的图片转换为临时文件
        $tmpFilePath = tempnam(sys_get_temp_dir(), 'image');
        file_put_contents($tmpFilePath, base64_decode($imageBase64));
        $postFields['image'] = new CURLFile($tmpFilePath, 'image/jpeg', 'image.jpg');

        curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $goodanswer = "图片上传失败：" . curl_error($ch);
        } else {
            if (preg_match('/^(http|https):\/\/[^ "]+$/', $response)) {
                $goodanswer = '![IMG](' . $response . ')';
            } else {
                $goodanswer = "图片上传失败：" . $response;
            }
        }
        $responsedata = '{"data":[{"url":"' . $response . '"}]}';
        curl_close($ch);
        unlink($tmpFilePath);
    } else {
        //$goodanswer = '![IMG](' . $answer['data'][0]['url'] . ')';
        //将OpenAI返回的图片URL上传到图床保存，如果还想用原来的模式，可以取消上面的注释，删掉下面的代码。另外新版js/index.js已经去掉pictureproxy.php图片代理，也要修改下。
        $row = $conn->get('main', '*', ['id' => 1]);
        $imagesiteurl = $row["imagesiteurl"];
        $imagesitetoken = $row["imagesitetoken"];

        $timestamp = file_get_contents($imagesiteurl . "?action=getnonce");
        $sign = md5(md5($timestamp) . $imagesitetoken);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_URL, $imagesiteurl);
        curl_setopt($ch, CURLOPT_POST, true);
        $postFields = [
            'action' => 'upload',
            'nonce' => $timestamp,
            'sign' => $sign,
        ];
        $tmpFilePath = tempnam(sys_get_temp_dir(), 'image');
        file_put_contents($tmpFilePath, file_get_contents($answer['data'][0]['url']));
        $postFields['image'] = new CURLFile($tmpFilePath, 'image/jpeg', 'image.jpg');

        curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $goodanswer = "图片上传失败：" . curl_error($ch);
        } else {
            if (preg_match('/^(http|https):\/\/[^ "]+$/', $response)) {
                $goodanswer = '![IMG](' . $response . ')';
            } else {
                $goodanswer = "图片上传失败：" . $response;
            }
        }
        $responsedata = '{"data":[{"url":"' . $response . '"}]}';
        curl_close($ch);
        unlink($tmpFilePath);
    }
    $sql = "INSERT INTO chathistory (title,question,answer,conversationid,modelid,realtime,userid,iserror) VALUES ('" . substr(parseMarkdownImage($goodquestion), 0, 100) . "','" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",0)";
} else if (isset($complete->request_id)) { //阿里版stablediffusion
    if (isset($complete->output)) { //成功返回任务id
        $apiaddress = str_replace('/api/v1/services/aigc/text2image/image-synthesis', '/api/v1/tasks/' . $complete->output->task_id, $apiaddress);
        while (!$alistablediffusionfinish) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
            curl_setopt($ch, CURLOPT_URL, $apiaddress);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_AUTOREFERER, true);
            if (!empty($proxyaddress)) {
                curl_setopt($ch, CURLOPT_PROXY, $proxyaddress);
            }
            $responsedata2 = curl_exec($ch);
            curl_close($ch);
            $complete2 = json_decode($responsedata2);
            if ((isset($complete2->output)) && (isset($complete2->output->task_status)) && ($complete2->output->task_status == 'SUCCEEDED')) {
                $alistablediffusionfinish = true;
                $responsedata = '{"data":[{"url":"' . $complete2->output->results[0]->url . '"}]}';
                $conn->update("user", ["quota[-]" => $modelprice, "credits[+]" => $modelprice, "questioncount[+]" => 1], ["id" => $userid]);
                $answer = json_decode($responsedata, true);
                $questionarr = json_decode($postdata, true);
                $goodquestion = addslashes($questionarr['input']['prompt']);
                $goodanswer = '![IMG](' . $answer['data'][0]['url'] . ')';
                $sql = "INSERT INTO chathistory (title,question,answer,conversationid,modelid,realtime,userid,iserror) VALUES ('" . substr(parseMarkdownImage($goodquestion), 0, 100) . "','" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",0)";
            } else {
                $nowtime = time();
                if ($nowtime - $starttime > 60) {
                    $responsedata = $responsedata2;
                    break;
                } else {
                    sleep(1);
                }
            }
        }
    }
    if (!$alistablediffusionfinish) { //遇到错误，包括首次请求返回错误和二次请求查询任务超时错误两种情况。
        $questionarr = json_decode($postdata, true);
        $goodquestion = addslashes($questionarr['input']['prompt']);
        $goodanswer = addslashes(trim($responsedata));
        $sql = "INSERT INTO errorlog (question,errmsg,conversationid,modelid,realtime,userid,apikey) VALUES ('" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",'" . $OPENAI_API_KEY . "')";
        $responsedata = '{"error":{"code":"unknown","message":"' . rawurlencode($responsedata) . '"}}';
    }
} else if (isset($complete->Response)) { //腾讯混元
    if (isset($complete->Response->ResultImage)) {
        $responsedata = '{"data":[{"url":"' . $complete->Response->ResultImage . '"}]}';
        $conn->update("user", ["quota[-]" => $modelprice, "credits[+]" => $modelprice, "questioncount[+]" => 1], ["id" => $userid]);
        $answer = json_decode($responsedata, true);
        $questionarr = json_decode($postdata, true);
        $goodquestion = addslashes($questionarr['Prompt']);
        $goodanswer = '![IMG](' . $answer['data'][0]['url'] . ')';

        $row = $conn->get('main', '*', ['id' => 1]);
        $imagesiteurl = $row["imagesiteurl"];
        $imagesitetoken = $row["imagesitetoken"];

        $timestamp = file_get_contents($imagesiteurl . "?action=getnonce");
        $sign = md5(md5($timestamp) . $imagesitetoken);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_URL, $imagesiteurl);
        curl_setopt($ch, CURLOPT_POST, true);
        $postFields = [
            'action' => 'upload',
            'nonce' => $timestamp,
            'sign' => $sign,
        ];
        $tmpFilePath = tempnam(sys_get_temp_dir(), 'image');
        file_put_contents($tmpFilePath, file_get_contents($answer['data'][0]['url']));
        $postFields['image'] = new CURLFile($tmpFilePath, 'image/jpeg', 'image.jpg');

        curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $goodanswer = "图片上传失败：" . curl_error($ch);
        } else {
            if (preg_match('/^(http|https):\/\/[^ "]+$/', $response)) {
                $goodanswer = '![IMG](' . $response . ')';
            } else {
                $goodanswer = "图片上传失败：" . $response;
            }
        }
        $responsedata = '{"data":[{"url":"' . $response . '"}]}';
        curl_close($ch);
        unlink($tmpFilePath);
        $sql = "INSERT INTO chathistory (title,question,answer,conversationid,modelid,realtime,userid,iserror) VALUES ('" . substr(parseMarkdownImage($goodquestion), 0, 100) . "','" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",0)";
    } else {
        $questionarr = json_decode($postdata, true);
        $goodquestion = addslashes($questionarr['Prompt']);
        $goodanswer = addslashes(trim($responsedata));
        $sql = "INSERT INTO errorlog (question,errmsg,conversationid,modelid,realtime,userid,apikey) VALUES ('" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",'" . $OPENAI_API_KEY . "')";
        $responsedata = '{"error":{"code":"unknown","message":"' . rawurlencode($responsedata) . '"}}';
    }
} else if ($modeltype == "讯飞星火") { //讯飞星火
    $questionarr = json_decode($postdata, true);
    $goodquestion = addslashes($questionarr['payload']['message']['text'][0]['content']);
    if (isset($complete->header)) {
        if ($complete->header->code == 0) {
            $row = $conn->get('main', '*', ['id' => 1]);
            $imagesiteurl = $row["imagesiteurl"];
            $imagesitetoken = $row["imagesitetoken"];

            $timestamp = file_get_contents($imagesiteurl . "?action=getnonce");
            $sign = md5(md5($timestamp) . $imagesitetoken);
            $imageBase64 = $complete->payload->choices->text[0]->content;
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
            curl_setopt($ch, CURLOPT_URL, $imagesiteurl);
            curl_setopt($ch, CURLOPT_POST, true);
            $postFields = [
                'action' => 'upload',
                'nonce' => $timestamp,
                'sign' => $sign,
            ];
            // 将base64编码的图片转换为临时文件
            $tmpFilePath = tempnam(sys_get_temp_dir(), 'image');
            file_put_contents($tmpFilePath, base64_decode($imageBase64));
            $postFields['image'] = new CURLFile($tmpFilePath, 'image/jpeg', 'image.jpg');

            curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                $goodanswer = "图片上传失败：" . curl_error($ch);
            } else {
                if (preg_match('/^(http|https):\/\/[^ "]+$/', $response)) {
                    $goodanswer = '![IMG](' . $response . ')';
                } else {
                    $goodanswer = "图片上传失败：" . $response;
                }
            }
            $responsedata = '{"data":[{"url":"' . $response . '"}]}';
            curl_close($ch);
            unlink($tmpFilePath);

            $sql = "INSERT INTO chathistory (title,question,answer,conversationid,modelid,realtime,userid,iserror) VALUES ('" . substr(parseMarkdownImage($goodquestion), 0, 100) . "','" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",0)";
        } else {
            $goodanswer = addslashes(trim($responsedata));
            $sql = "INSERT INTO errorlog (question,errmsg,conversationid,modelid,realtime,userid,apikey) VALUES ('" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",'" . $OPENAI_API_KEY . "')";
            $responsedata = '{"error":{"code":"unknown","message":"' . rawurlencode($responsedata) . '"}}';
        }
    } else {
        $goodanswer = addslashes(trim($responsedata));
        $sql = "INSERT INTO errorlog (question,errmsg,conversationid,modelid,realtime,userid,apikey) VALUES ('" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",'" . $OPENAI_API_KEY . "')";
        $responsedata = '{"error":{"code":"unknown","message":"' . rawurlencode($responsedata) . '"}}';
    }
} else if (isset($complete->status)) { //360智脑
    $questionarr = json_decode($postdata, true);
    $goodquestion = addslashes($questionarr['prompt']);
    if (isset($complete->output)) {
        $responsedata = '{"data":[{"url":"' . $complete->output[0] . '"}]}';
        $conn->update("user", ["quota[-]" => $modelprice, "credits[+]" => $modelprice, "questioncount[+]" => 1], ["id" => $userid]);
        $answer = json_decode($responsedata, true);
        $goodanswer = '![IMG](' . $answer['data'][0]['url'] . ')';
        $sql = "INSERT INTO chathistory (title,question,answer,conversationid,modelid,realtime,userid,iserror) VALUES ('" . substr(parseMarkdownImage($goodquestion), 0, 100) . "','" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",0)";
    } else {
        $goodanswer = addslashes(trim($responsedata));
        $sql = "INSERT INTO errorlog (question,errmsg,conversationid,modelid,realtime,userid,apikey) VALUES ('" . $goodquestion . "','" . $goodanswer . "','" . $conversationid . "'," . $lastmodelid . ",'" . date('Y-m-d H:i:s') . "'," . $userid . ",'" . $OPENAI_API_KEY . "')";
        $responsedata = '{"error":{"code":"unknown","message":"' . rawurlencode($responsedata) . '"}}';
    }
}

function parseMarkdownImage($str)
{
    if (substr($str, 0, 6) === '![IMG]') {
        $regex = '/!\[IMG]\((.*?)\)/';
        return preg_replace($regex, '[图片]', $str);
    } else {
        return $str;
    }
}

echo $responsedata;
$result = $conn->query($sql);
$conn->update('user', ['lastquestion' => '', 'lastmodelid' => NULL], ['rndstr' => $_GET["user"]]);
