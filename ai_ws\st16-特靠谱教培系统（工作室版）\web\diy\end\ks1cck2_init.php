<!-- ks1cck start -->

<iframe style="display:none;" id="ks1cck2_ws" name="ks1cck2_ws"></iframe>

<script>
jQuery(document).ready(function(){
    try{
        let referral_code='';

<?php

if (isset($_GET["referral_code"]) ) {
    if(!isset($_COOKIE['referral_code'])){
        $referral_code=$_GET["referral_code"];
        echo '      referral_code="'.$referral_code.'";';
    }
}
?>
        $('#ks1cck2_ws').attr('src',"ks1cck2/login.php?userrndstr="+userrndstr+"&referral_code="+referral_code);

        //console.log('ks1cck-init start');

        setTimeout(function(){
            var interval = setInterval(function(){

            //console.log('ks1cck-init check');

            var divElements = document.querySelectorAll('.button_icon-button-text');
            for (var i = 0; i < divElements.length; i++) {
              var divElement = divElements[i];
              var textContent = divElement.textContent;
              if (textContent.includes('退出')) {
                clearInterval(interval);
                fn_ks1_add_menu_cck2();
                break;
              }
            }
            
            }, 50);
        }, 50); 

    }catch(error){
        console.log('ks1cck2 init error')
        console.error(error);
    }


    try{

        setTimeout(function(){
            
             var originalDiv = document.querySelector(".home_chat-input-panel");
                originalDiv.style.display="none";
        }, 50); 

    }catch(error){
        console.log('ks1cck2 init2 error')
        console.error(error);
    }

    hide_div('.home_sidebar-body');

    hide_div('.home_sidebar-tail');


    //console.log('ks1cck-init end');
    
});


function hide_div(classname){
    //home_sidebar-body
    try{

        setTimeout(function(){
            
            var originalDiv = document.querySelector(classname);
                originalDiv.style.display="none";
        }, 50); 

    }catch(error){
        console.log('ks1cck2 hide_div error '+classname)
        console.error(error);
    }
}


function fn_ks1_add_menu_cck2(){
 
    var newDiv = document.createElement("div");
    newDiv.className = "home_sidebar-header-bar";

    var newButton = document.createElement("button");
    newButton.className = "button_icon-button button_shadow home_sidebar-bar-button clickable";
    newButton.onclick = function() {
      window.open("./ks1cck2/login.php?rndstr="+userrndstr+"&ref");
    };

    var iconDiv = document.createElement("div");
    iconDiv.className = "button_icon-button-icon";
    var icon = document.createElement("i");
    icon.className = "layui-icon";
    icon.innerHTML = "";
    iconDiv.appendChild(icon);

    var textDiv = document.createElement("div");
    textDiv.className = "button_icon-button-text";
    textDiv.innerHTML = "慢读细品评 v2 (Beta 测试)";
    newButton.appendChild(iconDiv);
    newButton.appendChild(textDiv);

    newDiv.appendChild(newButton);

    var originalDiv = document.querySelector(".home_sidebar-header-bar");
    originalDiv.parentNode.insertBefore(newDiv, originalDiv.nextSibling);
}

</script>
<!-- ks1cck end -->