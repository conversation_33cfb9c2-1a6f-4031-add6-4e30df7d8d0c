<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
if (isset($_REQUEST["ftp"])) {
    $conn->update('main',['ftp'=>urlencode($_REQUEST["user"]) . ":" . urlencode($_REQUEST["pass"]) . "@" . urlencode($_REQUEST["ftp"]) . "/" . urlencode($_REQUEST["dir"])],['id'=>1]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('设置成功！');</script>";
    exit;
}
$ftp = "";
$user = "";
$pass = "";
$dir = "";
$row = $conn->get('main','*',['id'=>1]);
$ftp = $row["ftp"];
if (isset($ftp)) {
    $user = urldecode(explode(":", explode("@", $ftp)[0])[0]);
    $pass = urldecode(explode(":", explode("@", $ftp)[0])[1]);
    $dir = urldecode(explode("/", $ftp)[1]);
    $ftp = urldecode(explode("/", explode("@", $ftp)[1])[0]);
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>数据库备份与导出</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-print"></i> 系统管理</li>
                <li class="active">数据库备份与导出</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px">
            <div style="margin:10px;;font-size:16px;">
                <b>数据库备份设置</b><br><br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1. 请确保登陆的FTP账号有建立目录和上传权限。某些FTP服务器与本程序不兼容，因此请先连接测试成功后再保存配置。<br><br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 如果想在服务器本地备份数据库，可以每天访问一次/admin/sql_backup.php，该文件将在/admin/backup目录中自动备份数据库文件。<br><br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3. 如果想将备份的数据库保存到下面配置的FTP服务器上，可以每天访问一次/admin/ftp_backup.php，浏览器访问或在linux shell用php执行都可以。<br><br>
                <div class="page-content-area" style="margin:10px;width:450px;height:350px;background:#F5F5F5;border:1px solid silver;border-radius:10px;text-align:center;font-size:16px;padding:10px;">
                    <div style="margin:10px;"><b>FTP服务器设置</b></div>
                    <div class="row">
                        <div class="space-6"></div>
                        <div class="col-xs-12">
                            <form class="form-horizontal" target=temp>

                                <div class="form-group" style="margin:13px 0;">
                                    <label class="col-lg-4 control-label">IP地址：</label>

                                    <div class="col-lg-6">
                                        <input type="text" id="ftp" name="ftp" value="<?php echo $ftp; ?>" class="bg-focus form-control" title="填写格式：***********:21（冒号后面是端口，可不填）">
                                    </div>
                                </div>
                                <div class="form-group" style="margin:13px 0;">
                                    <label class="col-lg-4 control-label">登陆用户名：</label>

                                    <div class="col-lg-6">
                                        <input type="text" id="user" name="user" value="<?php echo $user; ?>" class="bg-focus form-control" title="需要有上传权限，匿名用户填anonymous">
                                    </div>
                                </div>
                                <div class="form-group" style="margin:13px 0;">
                                    <label class="col-lg-4 control-label">登陆密码：</label>

                                    <div class="col-lg-6">
                                        <input type="text" id="pass" name="pass" value="<?php echo $pass; ?>" class="bg-focus form-control" title="需要有上传权限，匿名用户密码可为空">
                                    </div>
                                </div>
                                <div class="form-group" style="margin:13px 0;">
                                    <label class="col-lg-4 control-label">保存目录：</label>

                                    <div class="col-lg-6">
                                        <input type="text" id="dir" name="dir" value="<?php echo $dir; ?>" class="bg-focus form-control" title="填写格式：/backup/，基于登陆用户的FTP根目录，目录必须已经存在">
                                    </div>
                                </div>
                                <div class="form-group" align="center" style="padding:10px;">
                                    <button type="button" class="btn btn-primary" onclick="$('#onthego').show();$('#temp').attr('src','ftp_test.php?ftp='+$('#ftp').val()+'&user='+$('#user').val()+'&pass='+$('#pass').val()+'&dir='+$('#dir').val());">连接测试</button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<button type="submit" class="btn btn-primary">确认设置</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin:50px 10px;font-size:16px;">
                <b>数据库导出</b><br><br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;请 <a href="sql_export.php" target="temp">点击这里</a> 导出数据库
            </div>
            <div style="margin:50px 10px;font-size:16px;">
                <b>错误日志导出</b><br><br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;请 <a href="sql_export_table.php?table=errorlog" target="temp">点击这里</a> 导出日志
            </div>
        </div>
    </div>
    <iframe style="display:none;" id=temp name=temp></iframe>
</body>

</html>