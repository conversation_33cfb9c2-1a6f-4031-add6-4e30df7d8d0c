<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>群组列表</title>
    <link rel="stylesheet" href="res/ks1table.css">    
</head>
<body>
<?php
require '_ks1.php';
@session_start();

if (!isset($_SESSION[SESSION_KEY]['user']['id']) || !in_array($_SESSION[SESSION_KEY]['user']['role'], ['user', 'admin'])) {
    header('Location: login.php');
    exit;
}

$sql = "SELECT * FROM cck1_groups";
$groups = get_data($sql);
?>

<link rel="stylesheet" href="<?php echo $css_file; ?>">
<div style="text-align: center;">
    <h1>群组列表</h1>
    <div>
        <?php echo $nav_html; ?>
    </div>
    <a href="group_form.php">新增群组</a>
    <table border="1" style="margin: 0 auto;">
        <tr>
            <th>群组ID</th>
            <th>群组名称</th>
            <th>群组口令</th>
            <th>群组介绍</th>
            <th>群主用户ID</th>
            <th>操作</th>
        </tr>
        <?php foreach ($groups as $group): ?>
            <tr>
                <td><?php echo $group['group_id']; ?></td>
                <td><?php echo $group['group_name']; ?></td>
                <td><?php echo $group['password']; ?></td>
                <td><?php echo $group['description']; ?></td>
                <td><?php echo $group['owner_id']; ?></td>
                <td>
                    <a href="group_form.php?group_id=<?php echo $group['group_id']; ?>">编辑</a> |
                    <a href="group_delete.php?group_id=<?php echo $group['group_id']; ?>">删除</a>
                </td>
            </tr>
        <?php endforeach; ?>
    </table>
    <div>
        <?php echo $nav_html; ?>
    </div>
</div>
</body>
</html>