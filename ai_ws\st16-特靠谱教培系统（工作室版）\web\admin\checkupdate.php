<?php
require_once('check_admin.php');
require_once('mysqlconn.php');

$row = $conn->get('main', '*', ['id' => 1]);
$websitename = $row["websitename"];
$companyname = $row["companyname"];
$freetry = $row["freetry"];
$headlogo = $row["headlogo"];
$contentlogo = $row["contentlogo"];
$weixinaddress = $row["weixinaddress"];
$weixinredirecturl = $row["weixinredirecturl"];
$updateurl = $row["updateurl"];
$license = $row["license"];
$version = $row["version"];
if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
    $updateurl = str_replace('http://', 'https://', $updateurl);
}
$downloadurl = str_replace('checkupdate.php', 'download.php', $updateurl) . "?license=" . $license;
if ((empty($license)) && (isset($_REQUEST["license"]))) {
    $conn->update('main', ['license' => $_REQUEST["license"]], ['id' => 1]);
    header("Refresh:0");
    exit;
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>检查更新</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />


    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>
    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">检查更新</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px 0;">
            <div class="page-content-area">
                <div class="row">
                    <div class="space-6"></div>
                    <div class="col-xs-12">
                        <?php
                        if (empty($license)) {
                        ?>
                            <form class="form-horizontal">

                                <div class="form-group">
                                    <label class="col-lg-4 control-label">请先设置License（仅一次机会，请勿乱写）：</label>

                                    <div class="col-lg-4">
                                        <input type="text" style="text-align:left;" id="license" name="license" class="bg-focus form-control" autoComplete="off">
                                    </div>
                                </div>
                                <div class="form-group" style="margin-right:150px;margin-top:15px;text-align:center;">
                                    <div class="col-lg-6 col-lg-offset-3">
                                        <button type="submit" class="btn btn-primary">确认设置</submit>
                                    </div>
                                </div>
                            </form>
                        <?php
                        } else {
                        ?>
                            <div style="padding:10px 50px;font-size:20px;">
                                <p style="padding:20px;">License状态：<span id="licensestatus"></span></p>
                                <p style="padding:20px;">升级有效期：<span id="expireday"></span></p>
                                <p style="padding:20px;">当前版本：<span style="color:darkblue"><?php echo $version; ?></span></p>
                                <p style="padding:20px;">云端最新版本：<span id="version" style="color:darkgreen"></span></p>
                                <p style="padding:20px;">检测更新时间：<span id="checktime"></span></p>
                                <div id="updatebutton">
                                    <p style="padding:20px;"><button style="padding:5px 10px;" onclick="checkupdate();">检测更新</button><button style="margin-left:60px;padding:5px 10px;" onclick="downloadall();">下载全量包</button></p>
                                    <p style="padding:0 20px;font-size:20px;color:red;">请勿转卖本项目，安装包和源文件都有特征码，一旦发现转卖永久封禁。<br>买源码的用户可能会被删库，到时候售后成本要远高于赚到的小钱。</p>
                                </div>
                                <div style="display:none;" id="downloadbutton">
                                    <p style="padding:20px;"><button style="padding:5px 10px;" onclick="downloadupdate();">下载更新包</button></p>
                                    <p style="padding:0 20px;font-size:20px;color:red;">更新包和全量包都有用户特征，请勿外泄，否则将被永久封禁，并且不保证网站数据的安全。</p>
                                </div>
                                <iframe style="display:none;" id="temp"></iframe>
                            </div>




                        <?php
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        lastRunTime = 0;

        function checkupdate() {
            now = Date.now();
            if (now - lastRunTime < 60000) {
                alert('请不要频繁检测更新，一分钟后再试。');
                return;
            }
            $.ajax({
                url: '<?php echo $updateurl; ?>',
                type: 'post',
                dataType: "json",
                data: {
                    "license": "<?php echo $license; ?>",
                    "version": "<?php echo $version; ?>",
                    "action": "check"
                },
                success: function(response) {
                    if (response.success) {
                        $("#licensestatus").html(response.licensestatus);
                        $("#expireday").html(response.expireday);
                        $("#version").html(response.version);
                        $("#checktime").text(getCurrentTime());
                        if (response.version !== "<?php echo $version; ?>") {
                            $("#updatebutton").hide();
                            $("#downloadbutton").show();
                        }
                    } else {
                        alert(response.message);
                    }
                }
            });
            lastRunTime = now;
        }

        function downloadupdate() {
            location.href = "downloadupdatefile.php";
        }

        function downloadall() {
            location.href = "<?php echo $downloadurl; ?>";
        }

        function getCurrentTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = now.getMonth() + 1;
            const day = now.getDate();
            const hour = now.getHours();
            const minute = now.getMinutes();
            const second = now.getSeconds();
            return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;
        }
        <?php
        if (!empty($license)) {
        ?>
            $(document).ready(function() {
                checkupdate();
            });
        <?php
        }
        ?>
    </script>
</body>

</html>