<?php
require_once('check_admin.php');
require_once('mysqlconn.php');

use Medoo\Medoo;
// 最近两周天的日期
$dates = array();
for ($i = 0; $i < 14; $i++) {
    $dates[] = date('Y-m-d', strtotime("-$i day"));
}

// 近两周用户数变化
if (isset($_REQUEST['echarts']) && $_REQUEST['echarts'] == 'echarts_newuser-line') {
    $result = array();
    foreach ($dates as $key => $val) {
        // 当前日期总数
        $sql = "SELECT COUNT(*) AS count FROM user WHERE to_days(registertime) = to_days('$val')";
        $total = $conn->query($sql)->fetchAll();
        $data[] = $total[0][0];
    }

    $result['dates'] = $dates;
    $result['data'] = $data;
    die(json_encode($result));
}

// 近两周充值订单数变化
if (isset($_REQUEST['echarts']) && $_REQUEST['echarts'] == 'echarts_recharge-line') {
    $result = array();
    foreach ($dates as $key => $val) {
        // 当前日期总数
        $sql = "SELECT COUNT(*) AS count FROM rechargelog WHERE (to_days(rechargetime) = to_days('$val')) and (NOT (rechargecardid is NULL and operatorid is NULL and wxpaylistid is NULL and alipaylistid is NULL))";
        $total = $conn->query($sql)->fetchAll();
        $data[] = $total[0]["count"];
    }

    $result['dates'] = $dates;
    $result['data'] = $data;
    die(json_encode($result));
}

// 近两周全部提问数变化
if (isset($_REQUEST['echarts']) && $_REQUEST['echarts'] == 'echarts_data-line') {
    $result = array();
    foreach ($dates as $key => $val) {
        // 当前日期总数
        $sql = "SELECT COUNT(*) AS count FROM chathistory WHERE to_days(realtime) = to_days('$val')";
        $total = $conn->query($sql)->fetchAll();
        $data[] = $total[0]["count"];
    }

    $result['dates'] = $dates;
    $result['data'] = $data;
    die(json_encode($result));
}

// 近两周人员提问数变化
if (isset($_REQUEST['echarts']) && ($_REQUEST['echarts'] == 'echarts_user-line_area' || $_REQUEST['echarts'] == 'echarts_user-shadow_area')) {
    $result = array();
    $user_data = array();
    foreach ($dates as $key => $val) {
        $sql = "select CONCAT('UID:', u.userid) AS uname,count(*) as utotal from chathistory as h left join user as u on h.userid=u.id  where to_days(h.realtime) = to_days('$val') GROUP BY h.userid";
        $u_total = $conn->query($sql)->fetchAll();
        foreach ($u_total as $k => $v) {
            $user_data[$v['uname']][$val] = $v['utotal'];
        }
    }
    asort($user_data);

    // 内容补全
    foreach ($user_data as &$subArray) {
        foreach ($dates as $date) {
            if (!isset($subArray[$date])) {
                if ($_REQUEST['echarts'] == 'echarts_user-line_area') {
                    $subArray[$date] = 0;
                } else {
                    $subArray[$date] = '';
                }
            }
        }
        krsort($subArray);
    }

    // 格式化
    $user_num = array();
    foreach ($user_data as $key => $val) {
        $user_num[$key] = array_values($val);
    }

    $result['dates'] = $dates;
    $result['user_name'] = array_keys($user_data);
    $result['user_data'] = $user_num;
    die(json_encode($result));
}

// 全员数据
if (isset($_REQUEST['echarts']) && $_REQUEST['echarts'] == 'echarts_user_all-shadow') {
    $result = array();
    $sql = "select CONCAT('UID:', u.userid) AS uname,count(*) as utotal from chathistory as h left join user as u on h.userid=u.id WHERE h.realtime >= DATE_SUB(CURDATE(), INTERVAL 14 DAY) GROUP BY h.userid";
    $all_total = $conn->query($sql)->fetchAll();

    $utotal_col = array_column($all_total, 'utotal');
    array_multisort($utotal_col, SORT_DESC, $all_total);

    $result['xAxis'] = array_column($all_total, 'uname');
    $result['series'] = array_column($all_total, 'utotal');
    die(json_encode($result));
}

// 全员数据(环形)
if (isset($_REQUEST['echarts']) && $_REQUEST['echarts'] == 'echarts_user_all-pic') {
    $result = array();
    $sql = "select CONCAT('UID:', u.userid) AS uname,count(*) as utotal from chathistory as h left join user as u on h.userid=u.id WHERE h.realtime >= DATE_SUB(CURDATE(), INTERVAL 14 DAY) GROUP BY h.userid";
    $data = $conn->query($sql)->fetchAll();

    // $utotal_col = array_column($data, 'utotal');
    // array_multisort($utotal_col, SORT_DESC, $data);
    $all_total = array_sum(array_column($data, 'utotal'));
    $result['data'] = $data;
    $result['all_total'] = $all_total;
    die(json_encode($result));
}

$user = $conn->count("user", ["registertime[>=]" => date("Y-m-d 00:00:00")]);
$recharge = $conn->count("rechargelog", ["AND" => ["OR" => ["rechargecardid[!]" => null, "operatorid[!]" => null, "wxpaylistid[!]" => null, "alipaylistid[!]" => null], "rechargetime[>=]" => date("Y-m-d 00:00:00")]]);
$chat = $conn->count("chathistory", ["realtime[>=]" => date("Y-m-d 00:00:00")]);
?>
<!DOCTYPE html>
<html>

<head>
    <title>Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="bootstrap/bootstrap.min.css">
    <link rel="stylesheet" href="bootstrap/all.min.css">
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <link href="img/admin.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <style>
        .container {
            margin-top: 30px;
            width: 100% !important;
        }

        .card {
            border: none;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .card-header {
            background-color: #f8f9fa;
            padding: 10px;
            margin-bottom: 20px;
        }

        .card-header i {
            font-size: 24px;
            margin-right: 10px;
        }

        .card-header h5 {
            display: inline-block;
            font-size: 18px;
            margin: 0;
        }

        .card-header .badge {
            font-size: 14px;
            padding: 5px 10px;
            margin-left: 10px;
            background-color: #007bff;
        }

        .breadcrumb {
            list-style: none;
        }

        .breadcrumbs {
            height: 41px;
        }

        .breadcrumb>li {
            display: inline-block;
        }

        .check {
            text-decoration: underline;
            text-decoration-color: red;
        }

        span {
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-bar-chart-o"></i> 统计日志</li>
                <li class="active">对话日志</li>
            </ul>
        </div>
        <div class="main-content">
            <div class="container">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-users"></i>
                                <h5>今日新增用户数</h5>
                            </div>
                            <div class="card-body">
                                <h1><?php echo $user; ?></h1>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-wallet"></i>
                                <h5>今日新增充值订单数</h5>
                            </div>
                            <div class="card-body">
                                <h1><?php echo $recharge; ?></h1>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-question"></i>
                                <h5>今日新增问答数</h5>
                            </div>
                            <div class="card-body">
                                <h1><?php echo $chat; ?></h1>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="main-content">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card" style="padding: 0;">
                            <div class="card-header" style="margin-bottom: 0;">
                                <i class="fas fa-users"></i>
                                <h5>新增用户数近两周变化</h5>
                            </div>
                            <div class="card-body">
                                <div id="echarts_newuser" style="height: 300px"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="main-content">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card" style="padding: 0;">
                            <div class="card-header" style="margin-bottom: 0;">
                                <i class="fas fa-wallet"></i>
                                <h5>新增充值订单数近两周变化</h5>
                            </div>
                            <div class="card-body">
                                <div id="echarts_recharge" style="height: 300px"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--
        <div class="main-content">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card" style="padding: 0;">
                            <div class="card-header" style="margin-bottom: 0;">
                                <i class="fas fa-chart-line"></i>
                                <h5>新增提问数近两周变化</h5>
                            </div>
                            <div class="card-body">
                                <div id="echarts_data" style="height: 300px"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        -->
        <div class="main-content">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card" style="padding: 0;">
                            <div class="card-header" style="margin-bottom: 0;">
                                <i class="fas fa-chart-area"></i>
                                <h5>近两周用户提问变化</h5>
                                &nbsp;&nbsp;&nbsp;<span class="check" onclick="$(this).siblings().removeClass('check');$(this).addClass('check');echarts_push('echarts_user','line_area')">堆叠面积图</span>
                                &nbsp;&nbsp;&nbsp;<span onclick="$(this).siblings().removeClass('check');$(this).addClass('check');echarts_push('echarts_user','shadow_area')">堆叠条形图</span>
                            </div>
                            <div class="card-body">
                                <div id="echarts_user" style="height: 600px"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="main-content">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card" style="padding: 0;margin-bottom: 0;">
                            <div class="card-header" style="margin-bottom: 0;">
                                <i class="fas  fa-chart-bar"></i>
                                <h5>近两周用户提问排序</h5>
                                &nbsp;&nbsp;&nbsp;<span class="check" onclick="$(this).siblings().removeClass('check');$(this).addClass('check');echarts_push('echarts_user_all','shadow')">柱状图</span>
                                &nbsp;&nbsp;&nbsp;<span onclick="$(this).siblings().removeClass('check');$(this).addClass('check');echarts_push('echarts_user_all','pic')">环形图</span>
                            </div>
                            <div class="card-body">
                                <div id="echarts_user_all" style="height: 600px"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    </div>
    <script src="bootstrap/jquery.min.js"></script>
    <script src="js/propper.min.js"></script>
    <script src="bootstrap/bootstrap.min.js"></script>
    <script src="js/highcharts.js"></script>
    <script src="js/echarts.min.js"></script>
    <script>
        var options = {
            chart: {
                type: 'line'
            },
            title: {
                text: ''
            },
            xAxis: {
                categories: ['<?php echo date("m-d"); ?>', '<?php echo date("m-d", strtotime('-1 day')); ?>', '<?php echo date("m-d", strtotime('-2 day')); ?>', '<?php echo date("m-d", strtotime('-3 day')); ?>', '<?php echo date("m-d", strtotime('-4 day')); ?>', '<?php echo date("m-d", strtotime('-5 day')); ?>', '<?php echo date("m-d", strtotime('-6 day')); ?>', '<?php echo date("m-d", strtotime('-7 day')); ?>', '<?php echo date("m-d", strtotime('-8 day')); ?>', '<?php echo date("m-d", strtotime('-9 day')); ?>', '<?php echo date("m-d", strtotime('-10 day')); ?>', '<?php echo date("m-d", strtotime('-11 day')); ?>', '<?php echo date("m-d", strtotime('-12 day')); ?>', '<?php echo date("m-d", strtotime('-13 day')); ?>']
            },
            yAxis: {
                title: {
                    text: ''
                }
            },
            accessibility: {
                enabled: false
            },
            series: [{
                name: '新增用户数',
                data: [<?php echo $user['today_new']; ?>, <?php echo $user['day_1_new']; ?>, <?php echo $user['day_2_new']; ?>, <?php echo $user['day_3_new']; ?>, <?php echo $user['day_4_new']; ?>, <?php echo $user['day_5_new']; ?>, <?php echo $user['day_6_new']; ?>, <?php echo $user['day_7_new']; ?>, <?php echo $user['day_8_new']; ?>, <?php echo $user['day_9_new']; ?>, <?php echo $user['day_10_new']; ?>, <?php echo $user['day_11_new']; ?>, <?php echo $user['day_12_new']; ?>, <?php echo $user['day_13_new']; ?>]
            }]
        };
        $('.highcharts-container1').each(function() {
            Highcharts.chart(this, options);
        });
        var options = {
            chart: {
                type: 'line'
            },
            title: {
                text: ''
            },
            xAxis: {
                categories: ['<?php echo date("m-d"); ?>', '<?php echo date("m-d", strtotime('-1 day')); ?>', '<?php echo date("m-d", strtotime('-2 day')); ?>', '<?php echo date("m-d", strtotime('-3 day')); ?>', '<?php echo date("m-d", strtotime('-4 day')); ?>', '<?php echo date("m-d", strtotime('-5 day')); ?>', '<?php echo date("m-d", strtotime('-6 day')); ?>', '<?php echo date("m-d", strtotime('-7 day')); ?>', '<?php echo date("m-d", strtotime('-8 day')); ?>', '<?php echo date("m-d", strtotime('-9 day')); ?>', '<?php echo date("m-d", strtotime('-10 day')); ?>', '<?php echo date("m-d", strtotime('-11 day')); ?>', '<?php echo date("m-d", strtotime('-12 day')); ?>', '<?php echo date("m-d", strtotime('-13 day')); ?>']
            },
            yAxis: {
                title: {
                    text: ''
                }
            },
            accessibility: {
                enabled: false
            },
            series: [{
                name: '新增充值订单数',
                data: [<?php echo $recharge['today_new']; ?>, <?php echo $recharge['day_1_new']; ?>, <?php echo $recharge['day_2_new']; ?>, <?php echo $recharge['day_3_new']; ?>, <?php echo $recharge['day_4_new']; ?>, <?php echo $recharge['day_5_new']; ?>, <?php echo $recharge['day_6_new']; ?>, <?php echo $recharge['day_7_new']; ?>, <?php echo $recharge['day_8_new']; ?>, <?php echo $recharge['day_9_new']; ?>, <?php echo $recharge['day_10_new']; ?>, <?php echo $recharge['day_11_new']; ?>, <?php echo $recharge['day_12_new']; ?>, <?php echo $recharge['day_13_new']; ?>]
            }]
        };
        $('.highcharts-container2').each(function() {
            Highcharts.chart(this, options);
        });
        var options = {
            chart: {
                type: 'line'
            },
            title: {
                text: ''
            },
            xAxis: {
                categories: ['<?php echo date("m-d"); ?>', '<?php echo date("m-d", strtotime('-1 day')); ?>', '<?php echo date("m-d", strtotime('-2 day')); ?>', '<?php echo date("m-d", strtotime('-3 day')); ?>', '<?php echo date("m-d", strtotime('-4 day')); ?>', '<?php echo date("m-d", strtotime('-5 day')); ?>', '<?php echo date("m-d", strtotime('-6 day')); ?>', '<?php echo date("m-d", strtotime('-7 day')); ?>', '<?php echo date("m-d", strtotime('-8 day')); ?>', '<?php echo date("m-d", strtotime('-9 day')); ?>', '<?php echo date("m-d", strtotime('-10 day')); ?>', '<?php echo date("m-d", strtotime('-11 day')); ?>', '<?php echo date("m-d", strtotime('-12 day')); ?>', '<?php echo date("m-d", strtotime('-13 day')); ?>']
            },
            yAxis: {
                title: {
                    text: ''
                }
            },
            accessibility: {
                enabled: false
            },
            series: [{
                name: '新增问答数',
                data: [<?php echo $chat['today_new']; ?>, <?php echo $chat['day_1_new']; ?>, <?php echo $chat['day_2_new']; ?>, <?php echo $chat['day_3_new']; ?>, <?php echo $chat['day_4_new']; ?>, <?php echo $chat['day_5_new']; ?>, <?php echo $chat['day_6_new']; ?>, <?php echo $chat['day_7_new']; ?>, <?php echo $chat['day_8_new']; ?>, <?php echo $chat['day_9_new']; ?>, <?php echo $chat['day_10_new']; ?>, <?php echo $chat['day_11_new']; ?>, <?php echo $chat['day_12_new']; ?>, <?php echo $chat['day_13_new']; ?>]
            }]
        };
        $('.highcharts-container3').each(function() {
            Highcharts.chart(this, options);
        });
        // echarts
        function echarts_push(div_dom = 'echarts_data', style = 'line') {
            var myChart = echarts.init(document.getElementById(div_dom));
            var option;
            myChart.showLoading();

            $.get('./statistics.php?echarts=' + div_dom + '-' + style, function(data) {
                myChart.hideLoading();

                option = {
                    toolbox: {
                        feature: {
                            saveAsImage: {}
                        }
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        bottom: '1%',
                        containLabel: true
                    },
                    yAxis: [{
                        type: 'value'
                    }],
                };

                if (div_dom == 'echarts_newuser') {
                    option.tooltip = {
                        trigger: 'axis'
                    };
                    option.xAxis = {
                        type: 'category',
                        data: data.dates
                    };
                    option.series = [{
                        name: '新增用户数',
                        label: {
                            show: true
                        },
                        data: data.data,
                        type: 'line'
                    }];
                } else if (div_dom == 'echarts_recharge') {
                    option.tooltip = {
                        trigger: 'axis'
                    };
                    option.xAxis = {
                        type: 'category',
                        data: data.dates
                    };
                    option.series = [{
                        name: '新增充值订单数',
                        label: {
                            show: true
                        },
                        data: data.data,
                        type: 'line'
                    }];
                } else if (div_dom == 'echarts_data') {
                    option.tooltip = {
                        trigger: 'axis'
                    };
                    option.xAxis = {
                        type: 'category',
                        data: data.dates
                    };
                    option.series = [{
                        name: '新增问答数',
                        label: {
                            show: true
                        },
                        data: data.data,
                        type: 'line'
                    }];
                } else if (style == 'line_area') {
                    option.tooltip = {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            label: {
                                backgroundColor: '#6a7985'
                            }
                        }
                    };
                    option.legend = {
                        data: data.user_name
                    };
                    option.xAxis = [{
                        type: 'category',
                        boundaryGap: false,
                        data: data.dates
                    }];
                    option.series = [];
                    $.each(data.user_data, function(index, value) {
                        option.series.push({
                            name: index,
                            type: 'line',
                            stack: 'Total',
                            areaStyle: {},
                            emphasis: {
                                focus: 'series'
                            },
                            data: value
                        });
                    });
                } else if (style == 'shadow_area') {
                    option.tooltip = {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    };
                    option.legend = {};
                    option.xAxis = {
                        type: 'value'
                    };
                    option.yAxis = {
                        type: 'category',
                        data: data.dates
                    };
                    option.series = [];
                    $.each(data.user_data, function(index, value) {
                        option.series.push({
                            name: index,
                            type: 'bar',
                            stack: 'total',
                            label: {
                                show: true
                            },
                            emphasis: {
                                focus: 'series'
                            },
                            data: value
                        });
                    });
                } else if (style == 'shadow') {
                    option.grid.top = '2%';
                    option.tooltip = {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: ''
                    };
                    option.xAxis = [{
                        type: 'category',
                        data: data.xAxis,
                        axisTick: {
                            alignWithLabel: true
                        },
                        axisLabel: {
                            interval: 0,
                            rotate: 40
                        },
                        show: true
                    }];
                    option.series = [{
                        name: '提问数量',
                        label: {
                            show: true
                        },
                        type: 'bar',
                        barWidth: '60%',
                        data: data.series
                    }];
                } else if (style == 'pic') {
                    option.grid.top = '1%';
                    option.tooltip = {
                        trigger: 'item',
                        formatter: function(p) {
                            return p.name + '：<br/>' + p.value + '(' + p.percent * 2 + ')%';
                        }
                    };
                    option.xAxis = {
                        show: false
                    };
                    option.series = {
                        type: 'pie',
                        radius: ['170%', '200%'],
                        center: ['50%', '105%'],
                        minAngle: 1,
                        maxAngle: 5,
                        startAngle: 180,
                        label: {
                            formatter(param) {
                                return param.name + ' (' + param.percent * 2 + '%)';
                            }
                        },
                        data: data.data.map(function(p) {
                            return {
                                name: p.uname,
                                value: p.utotal
                            };
                        })
                    };
                    // 总数形成半圆
                    option.series.data.push({
                        value: data.all_total,
                        itemStyle: {
                            color: 'none',
                            decal: {
                                symbol: 'none'
                            }
                        },
                        label: {
                            show: false
                        }
                    })
                }

                if (option && typeof option === 'object') {
                    myChart.setOption(option);
                }
                window.addEventListener('resize', myChart.resize);
            }, 'json')


        }
        echarts_push('echarts_newuser', 'line');
        echarts_push('echarts_recharge', 'line');
        //echarts_push('echarts_data', 'line');
        echarts_push('echarts_user', 'line_area');
        echarts_push('echarts_user_all', 'shadow');
    </script>
</body>
<?php

?>

</html>