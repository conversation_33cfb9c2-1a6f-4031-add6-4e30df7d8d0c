<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>cck2仪表盘</title>
    
</head>
<body>
<?php
require '_ks1.php';
@session_start();

if (!isset($_SESSION[SESSION_KEY]['user']['id']) || !in_array($_SESSION[SESSION_KEY]['user']['role'], ['user', 'admin'])) {
    header('Location: login.php?from=index_miss_s_u_id');
    exit;
}

$user_id = null;
$role = null;
$username = null;
$user_group_id = null; // 新增变量，用于存储用户所属群组ID

if (isset($_SESSION[SESSION_KEY]['user']['id'])) {
    $user_id = $_SESSION[SESSION_KEY]['user']['id'];
    $role = $_SESSION[SESSION_KEY]['user']['role'] ?? null;
    $username = $_SESSION[SESSION_KEY]['user']['username'] ?? null;
    $user_group_id = $_SESSION[SESSION_KEY]['user']['group_id'] ?? null; // 获取用户所属群组ID
}

if (intval($user_group_id)<=1) {
    $user_group_id = null;
}

$total_links_group = get_cck_links_count_mode(false);
$total_checks = get_cck_link_views_count();
$total_users = get_cck_users_count();

// 获取最热群组数据
$hot_groups = get_hot_groups();

$css_file='res/wx_style.css';
?>
<link rel="stylesheet" href="<?php echo $css_file; ?>"> <!-- 动态引用CSS -->
<div style="text-align: center;">
    <?php if (isset($_SESSION[SESSION_KEY]['user']['id'])): ?>
        <h1>欢迎，<?php echo $username;
echo ' group_id='.$user_group_id;
         ?></h1>
        <div>
            <?php echo $nav_html; ?>
        </div>
        <?php if ($role == 'admin'): ?>
            <a href="admin.php">查看检查记录</a>
        <?php endif; ?>
    <?php endif; ?>
    <div id='mode_div' style="display: flex; justify-content: center; align-items: center;">
        <div style="flex: 1;">
            <h2>群组模式</h2>
            <p>文章总数：<?php echo $total_links_group ?? 0; ?> 个</p>
            <div>
                <form action="dashboard.php" method="post" style="margin-top: 10px;">
                    <input type="hidden" name="group_id" value="0">
                    <button type="submit">进入大厅（非群组）</button>
                </form>
            </div>
            <?php if ($user_group_id): ?>
                <!-- 如果用户已加入群组，显示进入群组的按钮 -->
                <form action="enter_group.php?group_id=<?php echo $user_group_id; ?>" method="post" style="margin-top: 10px;">
                    <input type="hidden" name="group_id" value="<?php echo $user_group_id; ?>">
                    <button type="submit">进入我的群组 #<?php echo $user_group_id; ?></button>
                </form>
            <?php else: ?>
                <!-- 如果用户未加入群组，显示加入群组的表单 -->
                <form action="join_group.php" method="post" style="margin-top: 10px;">
                    <input type="text" id="group_code" name="group_code" required placeholder="群组代号。自由大厅代号new，其他群组代号见介绍或社群通知。">
                    <input type="text" id="group_pawd" name="group_pawd" required placeholder="群组口令。自由大厅口令new，其他群组口令见介绍或社群通知。">
                    <button type="submit">加入群组</button>
                </form>
            <?php endif; ?>
            <h3>热门群组</h3>
            <ul>
                <?php foreach ($hot_groups as $group): ?>
                    <li><?php echo htmlspecialchars($group['group_name']); ?> (热度：<?php echo $group['hot_score']; ?>)<br>
                        <span><?php echo htmlspecialchars($group['description']);?></span>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
</div>
</body>
</html>