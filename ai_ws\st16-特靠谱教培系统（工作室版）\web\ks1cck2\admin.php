<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员仪表盘</title>    
    <link rel="stylesheet" href="res/ks1table.css">    
</head>
<?php
require '_ks1.php';
@session_start();
if (is_admin_in()===false) {
    header('Location: login.php');
    exit;
}


$checks = get_all_checks();


?>
<body>
    <link rel="stylesheet" href="<?php echo $css_file; ?>"> <!-- 动态引用CSS -->
    <h1>管理员仪表盘</h1>
    <div>
        <?php echo $nav_html; ?>
    </div>
    <h2>检查记录</h2>
    <table border="1">
        <tr>
            <th>检查时间</th>
            <th>检查者</th>
            <th>文章链接</th>
        </tr>
        <?php
        if ($checks) {
            foreach ($checks as $check) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($check['checked_at']) . '</td>';
                echo '<td>' . htmlspecialchars($check['checker']) . '</td>';
                echo '<td><a href="' . htmlspecialchars($check['link']) . '">' . htmlspecialchars($check['link']) . '</a></td>';
                echo '</tr>';
            }
        } else {
            echo '<tr><td colspan="3">暂无检查记录。</td></tr>';
        }
        ?>
    </table>
    <div>
        <?php echo $nav_html; ?>
    </div>
</body>
</html>