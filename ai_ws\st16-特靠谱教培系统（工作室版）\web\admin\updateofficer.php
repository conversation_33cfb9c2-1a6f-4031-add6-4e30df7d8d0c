<?php
require_once('check_admin.php');
if (isset($_REQUEST["id"])) {
    require_once('mysqlconn.php');
    if ($conn->has('officer',['id'=>$_REQUEST["id"]])) {
        $conn->update('officer',['realname'=>$_REQUEST['realname'],'tel'=>$_REQUEST['tel'],'email'=>$_REQUEST['email'],'memo'=>$_REQUEST['memo'],'realtime'=>date('Y-m-d H:i:s')],['id'=>$_REQUEST["id"]]);
        echo "<html><head><meta charset=utf-8></head><body><script>parent.document.getElementById('onthego').style.display='none';</script></body></html>";
    } else {
        echo '<html><head><meta charset=utf-8></head><body><script>alert("用户不存在！");parent.location.href="manageuser.php";</script></body></html>';
    }
    exit(0);
}
