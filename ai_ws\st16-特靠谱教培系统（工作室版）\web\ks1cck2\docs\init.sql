-- 第 1 套系统表
CREATE TABLE cck1_users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    group_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

DROP TABLE IF EXISTS `cck1_groups`;
CREATE TABLE `cck1_groups`  (
  `group_id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `password` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '群组口令',
  `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '群组介绍',
  `owner_id` int(11) NOT NULL DEFAULT 0 COMMENT '群主用户 ID',
  `config` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '群组个性化配置',
  PRIMARY KEY (`group_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of cck1_groups
-- ----------------------------
INSERT INTO `cck1_groups` VALUES (1, 'L0，自由大厅', '', '', '无密码', 2, NULL);
INSERT INTO `cck1_groups` VALUES (2, 'L1，定位探索期（0-100粉）', '', 'password1', '这是定位探索期的群组，口令password1', 1, NULL);
INSERT INTO `cck1_groups` VALUES (3, 'L2，冷启动期（100-300粉）', '', 'password2', '这是冷启动期的群组，口令password2', 1, NULL);
INSERT INTO `cck1_groups` VALUES (4, 'L3，冲刺开通期（300-500粉）', '', 'password3', '这是冲刺开通期的群组，口令password3', 1, NULL);
INSERT INTO `cck1_groups` VALUES (5, 'L4，流量池攻坚期（500-800粉）', '', 'password4', '这是流量池攻坚期的群组，口令password4', 1, NULL);
INSERT INTO `cck1_groups` VALUES (6, 'L5，商业跃升期（800-1000以上）', '', 'password5', '这是商业跃升期的群组，口令password5', 1, NULL);

CREATE TABLE cck1_links (
    link_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    url VARCHAR(500) NOT NULL,
    title VARCHAR(200) NOT NULL,
    mode ENUM('free', 'group') NOT NULL,
    group_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES cck1_users(user_id),
    FOREIGN KEY (group_id) REFERENCES cck1_groups(group_id)
);

CREATE TABLE cck1_link_views (
    view_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    link_id INT NOT NULL,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES cck1_users(user_id),
    FOREIGN KEY (link_id) REFERENCES cck1_links(link_id),
    UNIQUE KEY unique_view (user_id, link_id)
);

-- 第 2 套系统表
CREATE TABLE cck2_users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    group_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE cck2_groups (
    group_id INT PRIMARY KEY AUTO_INCREMENT,
    group_name VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE cck2_links (
    link_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    url VARCHAR(500) NOT NULL,
    title VARCHAR(200) NOT NULL,
    mode ENUM('free', 'group') NOT NULL,
    group_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES cck2_users(user_id),
    FOREIGN KEY (group_id) REFERENCES cck2_groups(group_id)
);

CREATE TABLE cck2_link_views (
    view_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    link_id INT NOT NULL,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES cck2_users(user_id),
    FOREIGN KEY (link_id) REFERENCES cck2_links(link_id),
    UNIQUE KEY unique_view (user_id, link_id)
);

-- 第 3 套系统表
CREATE TABLE cck3_users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    group_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE cck3_groups (
    group_id INT PRIMARY KEY AUTO_INCREMENT,
    group_name VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE cck3_links (
    link_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    url VARCHAR(500) NOT NULL,
    title VARCHAR(200) NOT NULL,
    mode ENUM('free', 'group') NOT NULL,
    group_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES cck3_users(user_id),
    FOREIGN KEY (group_id) REFERENCES cck3_groups(group_id)
);

CREATE TABLE cck3_link_views (
    view_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    link_id INT NOT NULL,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES cck3_users(user_id),
    FOREIGN KEY (link_id) REFERENCES cck3_links(link_id),
    UNIQUE KEY unique_view (user_id, link_id)
);

-- 修改 cck1_link_views 表的 viewed_at 字段类型为 VARCHAR(32)
ALTER TABLE `cck1_link_views` MODIFY COLUMN `viewed_at` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '';

-- 在 cck1_link_views 表中新增 read_count 字段
ALTER TABLE `cck1_link_views` ADD COLUMN `read_count` INT(11) NOT NULL DEFAULT 0;




-- 修改 cck1_groups 表，增加 password、description 和 owner_id 字段
ALTER TABLE `cck1_groups`
ADD COLUMN `password` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '群组口令',
ADD COLUMN `description` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '群组介绍',
ADD COLUMN `owner_id` INT NOT NULL DEFAULT 0 COMMENT '群主用户 ID';

ALTER TABLE `cck1_groups` MODIFY COLUMN `created_at` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '';


-- 修改 cck1_groups 表，增加 config 字段
ALTER TABLE `cck1_groups`
ADD COLUMN `config` TEXT DEFAULT NULL  COMMENT '群组个性化配置';