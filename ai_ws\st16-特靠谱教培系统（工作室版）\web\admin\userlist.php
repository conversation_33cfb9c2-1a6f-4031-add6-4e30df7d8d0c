<?php
set_time_limit(0);
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);
require_once('check_admin.php');
require_once('mysqlconn.php');
if ((isset($_POST["action"])) && ($_POST["action"] == "update")) {
    $conn->update('user', ['email' => $_POST['email'], 'tel' => $_POST['tel'], 'isforbidden' => $_POST['isforbidden'], 'memo' => $_POST['memo']], ['id' => $_POST['id']]);
    echo "<html><head><meta charset=utf-8></head><body><script>parent.document.getElementById('onthego').style.display='none';</script></body></html>";
    exit;
}
if ((isset($_GET["action"])) && ($_GET["action"] == "changepass")) {
    $conn->update('user', ['password' => md5(md5($_GET["password"]) . "chatgpt@2023")], ['id' => $_GET['id']]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('密码修改成功');</script></body></html>";
    exit;
}

$ipagecurrent = isset($_REQUEST["page"]) ? $_REQUEST["page"] : 1;
$searchitem = isset($_REQUEST["searchitem"]) ? $_REQUEST["searchitem"] : '';
$keyword = isset($_REQUEST["keyword"]) ? $_REQUEST["keyword"] : '';
$order = isset($_REQUEST["order"]) ? $_REQUEST["order"] : '';
$orderdesc = isset($_REQUEST["orderdesc"]) ? $_REQUEST["orderdesc"] : '';
$pagenumber = 15; //每页显示数据条数
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>用户列表</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <link rel="stylesheet" href="../css/layui.css">
    <script src="../js/layui.js" type="application/javascript"></script>
    <style>
        form {
            margin: 0px;
            display: inline
        }

        select {
            vertical-align: bottom;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑';overflow-x: auto">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-users"></i> 用户管理</li>
                <li class="active">用户列表</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px;">
            <div class="row">
                <div class="space-6"></div>
                <div class="col-xs-12">

                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <td colspan="18">
                                    <form target=_self method=get style="margin:0px;display: inline">
                                        搜索：<select name=searchitem style='width:120px;margin-right:10px;' onchange='changecontent(this.value);'>
                                            <option value="">请选择</option>
                                            <option value=userid <?php if ($searchitem == "userid") echo "selected"; ?>>用户UID</option>
                                            <option value=tel <?php if ($searchitem == "tel") echo "selected"; ?>>手机号</option>
                                            <option value=email <?php if ($searchitem == "email") echo "selected"; ?>>邮箱</option>
                                            <option value=openid <?php if ($searchitem == "openid") echo "selected"; ?>>openid1</option>
                                            <option value=newopenid <?php if ($searchitem == "newopenid") echo "selected"; ?>>openid2</option>
                                            <option value=appletopenid <?php if ($searchitem == "appletopenid") echo "selected"; ?>>微信小程序</option>
                                            <option value=rndstr <?php if ($searchitem == "rndstr") echo "selected"; ?>>cookie</option>
                                            <option value=isforbidden <?php if ($searchitem == "isforbidden") echo "selected"; ?>>是否被禁</option>
                                            <option value=quota <?php if ($searchitem == "quota") echo "selected"; ?>>额度范围</option>
                                            <option value=credits <?php if ($searchitem == "credits") echo "selected"; ?>>积分范围</option>
                                            <option value=questioncount <?php if ($searchitem == "questioncount") echo "selected"; ?>>提问次数范围</option>
                                            <option value=expiretime <?php if ($searchitem == "expiretime") echo "selected"; ?>>过期时间范围</option>
                                            <option value=registertime <?php if ($searchitem == "registertime") echo "selected"; ?>>注册时间范围</option>
                                            <option value=memo <?php if ($searchitem == "memo") echo "selected"; ?>>备注</option>

                                        </select>
                                        <span id=keywordpanel style="display:<?php if (($searchitem == "expiretime") || ($searchitem == "registertime") || ($searchitem == "userid") || ($searchitem == "quota") || ($searchitem == "credits") || ($searchitem == "questioncount")) echo "none"; ?>;">关键字：<input name=keyword value="<?php echo $keyword; ?>" id='keyword' style='width:140px;height:30px;font-size:14px;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;padding-left:8px;'></span>
                                        <span id=selecttime style="display:<?php if (($searchitem != "expiretime") && ($searchitem != "registertime")) echo "none"; ?>;">时间范围：<input size=12 name=time1 style="height:30px;font-size:14px;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;padding-left:5px;" value='<?php echo isset($_REQUEST["time1"]) ? $_REQUEST["time1"] : date("Y-m-d"); ?>'> 至 <input style="padding-left:5px;height:30px;font-size:14px;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;" size=12 name=time2 value='<?php echo isset($_REQUEST["time2"]) ? $_REQUEST["time2"] : date("Y-m-d"); ?>'></span>
                                        <span id=selectnum style="display:<?php if (($searchitem != "userid") && ($searchitem != "quota") && ($searchitem != "credits") && ($searchitem != "questioncount")) echo "none"; ?>;">数值范围：<input size=12 name=num1 style="height:30px;font-size:14px;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;padding-left:5px;" value='<?php echo isset($_REQUEST["num1"]) ? $_REQUEST["num1"] : ""; ?>'> 至 <input style="padding-left:5px;height:30px;font-size:14px;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;" size=12 name=num2 value='<?php echo isset($_REQUEST["num2"]) ? $_REQUEST["num2"] : ""; ?>'></span>
                                        &nbsp;&nbsp;&nbsp;按照
                                        <select name=order style="width:120px;">
                                            <option value="">请选择</option>
                                            <option value=userid <?php if ($order == "userid") echo "selected"; ?>>用户UID</option>
                                            <option value=tel <?php if ($order == "tel") echo "selected"; ?>>手机号</option>
                                            <option value=email <?php if ($order == "email") echo "selected"; ?>>邮箱</option>
                                            <option value=quota <?php if ($order == "quota") echo "selected"; ?>>额度</option>
                                            <option value=credits <?php if ($order == "credits") echo "selected"; ?>>积分</option>
                                            <option value=questioncount <?php if ($order == "questioncount") echo "selected"; ?>>历史提问</option>
                                            <option value=isforbidden <?php if ($order == "isforbidden") echo "selected"; ?>>是否被禁</option>
                                            <option value=memo <?php if ($order == "memo") echo "selected"; ?>>备注</option>
                                        </select>&nbsp;&nbsp;&nbsp;
                                        <select name=orderdesc style="width:100px;">
                                            <option value="desc" <?php if ($orderdesc == 'desc') echo "selected"; ?>>降序排列</option>
                                            <option value="">升序排列</option>
                                        </select>
                                        &nbsp;&nbsp;&nbsp;
                                        <button class="btn btn-sm btn-info" style="padding:2px 10px;" type=submit>查询</button>
                                        <!--
                                        <button class="btn btn-sm btn-info" style="padding:2px 10px;" onclick="document.getElementById('temp').src='saveexcel_userlist.php';return false;">导出所有用户</button>
                                        -->
                                    </form>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-center" style="width:60px;">编号</th>
                                <th class="text-center" style="width:100px;">UID</th>
                                <th class="text-center" style="min-width:100px;">手机</th>
                                <th class="text-center" style="min-width:100px;">邮箱</th>
                                <th class="text-center" style="width:100px;">openid1</th>
                                <th class="text-center" style="width:100px;">openid2</th>
                                <th class="text-center" style="width:100px;">微信小程序</th>
                                <th class="text-center" style="width:100px;">cookie</th>
                                <th class="text-center" style="width:60px;">额度</th>
                                <th class="text-center" style="width:60px;">积分</th>
                                <th class="text-center" style="width:60px;">提问</th>
                                <th class="text-center" style="width:160px;">过期时间</th>
                                <th class="text-center" style="width:160px;">注册时间</th>
                                <th class="text-center" style="width:160px;">最后登录时间</th>
                                <th class="text-center" style="width:100px;">登录次数</th>
                                <th class="text-center" style="width:100px;">禁用</th>
                                <th class="text-center" style="width:100px;">密码</th>
                                <th class="text-center" style="width:60px;">备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $sql = "select * from user ";
                            if ($searchitem != "") {
                                if (($searchitem == "userid") || ($searchitem == "quota") || ($searchitem == "credits") || ($searchitem == "questioncount")) {
                                    $sql .= " where " . addslashes($searchitem) . " between '" . addslashes($_REQUEST["num1"]) . "' and '" . addslashes($_REQUEST["num2"]) . "' ";
                                } else if (($searchitem == "expiretime") || ($searchitem == "registertime")) {
                                    $sql .= " where " . addslashes($searchitem) . " between '" . addslashes($_REQUEST["time1"]) . "' and '" . addslashes(date("Y-m-d", strtotime("+1 day", strtotime($_REQUEST["time2"])))) . "' ";
                                } else {
                                    $sql .= " where " . addslashes($searchitem) . " like '%" . addslashes($keyword) . "%' ";
                                }
                            }
                            if ($order !== "") {
                                $sql .= " order by " . addslashes($order) . " " . addslashes($orderdesc);
                            } else {
                                $sql .= " order by id desc";
                            }
                            $sqlcount = "select count(t.id) from (" . $sql . ") t";
                            $result = $conn->query($sqlcount);
                            $row = $result->fetch();
                            $totalnumber = $row[0];
                            $ipagecount = ceil($totalnumber / $pagenumber);
                            if ($totalnumber == 0) {
                                echo "<tr><td colspan=17>未找到匹配的结果。</td></tr>";
                            } else {
                                if ($ipagecurrent > $ipagecount) $ipagecurrent = $ipagecount;
                                if ($ipagecurrent < 1) $ipagecurrent = 1;
                                $count = 0;
                                $startcount = ($ipagecurrent - 1) * $pagenumber;
                                $sql .= " limit " . $startcount . "," . $pagenumber;
                                $result = $conn->query($sql);
                                while ($row = $result->fetch()) {
                                    $count++;
                                    $logintimes = explode(";", $row["logintime"]);
                                    $loginips = explode(";", $row["loginip"]);
                                    $toalert = "";
                                    for ($x = 0; $x < sizeof($logintimes); $x++) {
                                        $toalert = $toalert . '登录时间：' . $logintimes[$x] . ' 登录IP：' . $loginips[$x] . '\n';
                                    }
                            ?>
                                    <tr>
                                        <form id="form<?php echo $row["id"] ?>" method=post target="temp" action="userlist.php" onsubmit="checkandsubmit('<?php echo $row["id"] ?>');">
                                            <input type=hidden name=id value="<?php echo $row["id"] ?>"><input type=hidden name=action value=update>
                                            <td class="text-center"><?php echo $count ?></td>
                                            <td class="text-center"><?php echo $row["userid"] ?></td>
                                            <td class="text-center"><input style="width:100%;" name=tel value="<?php echo $row["tel"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><input style="width:100%;" name=email value="<?php echo $row["email"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td title="单击复制" class="text-center copy" style="cursor:pointer;min-width:100px;max-width:100px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"><?php echo $row["openid"] ?></td>
                                            <td title="单击复制" class="text-center copy" style="cursor:pointer;min-width:100px;max-width:100px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"><?php echo $row["newopenid"] ?></td>
                                            <td title="单击复制" class="text-center copy" style="cursor:pointer;min-width:100px;max-width:100px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"><?php echo $row["appletopenid"] ?></td>
                                            <td title="单击复制" class="text-center copy" style="cursor:pointer;min-width:100px;max-width:100px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"><?php echo $row["rndstr"] ?></td>
                                            <td class="text-center"><?php echo $row["quota"] ?></td>
                                            <td class="text-center"><?php echo $row["credits"] ?></td>
                                            <td class="text-center"><?php echo $row["questioncount"] ?></td>
                                            <td class="text-center"><?php echo $row["expiretime"] ?></td>
                                            <td class="text-center"><?php echo $row["registertime"] ?></td>
                                            <td class="text-center"><?php if (sizeof($logintimes) > 0) echo $logintimes[sizeof($logintimes) - 1] ?></td>
                                            <td class="text-center" style="cursor:pointer;" title="点击查看详情" onclick="alert('<?php echo $toalert ?>');"><?php echo sizeof($logintimes) ?></td>
                                            <td class="text-center"><select name=isforbidden style="padding:0;height:25px;" onchange="checkandsubmit('<?php echo $row["id"] ?>');">
                                                    <option value=1 <?php echo ($row["isforbidden"]) ? "selected" : "" ?>>是</option>
                                                    <option value=0 <?php echo ($row["isforbidden"]) ? "" : "selected" ?>>否</option>
                                                </select></td>
                                            <td class="text-center"><input type=button onclick="changepasswd('<?php echo $row["id"] ?>');" value="修改"></td>
                                            <td class="text-center"><input style="width:100%;" name=memo value="<?php echo $row["memo"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                        </form>
                                    </tr>
                            <?php
                                }
                            }

                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php
    echo "<table width='100%' border='0' cellspacing='0' cellpadding='5'><tr><td height='20' align=center style='border-width:0pt'>每页显示 " . $pagenumber . " 个记录　共有 " . $ipagecount . " 页　当前为第 " . $ipagecurrent . " 页 ";
    if ($ipagecurrent == 1) {
        echo "　　首页　 | ";
    } else {
        echo "<form method=post onclick='this.submit();' style='cursor:pointer;'><input type=hidden name=page value='1'>　　首页　</form> | ";
    }
    if ($ipagecurrent == 1) {
        echo "　上一页　 | ";
    } else {
        echo "<form method=post onclick='this.submit();' style='cursor:pointer;'><input type=hidden name=page value='" . ($ipagecurrent - 1) . "'>　上一页　</form> | ";
    }
    if ($ipagecount > $ipagecurrent) {
        echo "<form method=post onclick='this.submit();' style='cursor:pointer;'><input type=hidden name=page value='" . ($ipagecurrent + 1) . "'>　下一页　</form> ";
    } else {
        echo "　下一页　";
    }
    if ($ipagecount > $ipagecurrent) {
        echo "| <form method=post onclick='this.submit();' style='cursor:pointer;'><input type=hidden name=page value='" . $ipagecount . "'>　末页　</form> ";
    } else {
        echo "| 　末页　 ";
    }
    echo "</td></tr></table>";
    ?>
    <br><br>
    <script>
        function changecontent(cnt) {
            if ((cnt == "userid") || (cnt == "quota") || (cnt == "credits") || (cnt == "questioncount")) {
                document.getElementById("selectnum").style.display = "";
                document.getElementById("selecttime").style.display = "none";
                document.getElementById("keywordpanel").style.display = "none";
                document.getElementById("keywordpanel").value = "";
            } else if ((cnt == "expiretime") || (cnt == "registertime")) {
                document.getElementById("selectnum").style.display = "none";
                document.getElementById("selecttime").style.display = "";
                document.getElementById("keywordpanel").style.display = "none";
                document.getElementById("keywordpanel").value = "";
            } else {
                document.getElementById("selectnum").style.display = "none";
                document.getElementById("selecttime").style.display = "none";
                document.getElementById("keywordpanel").style.display = "";
                document.getElementById("keywordpanel").value = "";
            }
        }
        var cells = document.querySelectorAll('.copy');

        cells.forEach(function(cell) {
            cell.addEventListener('click', function() {
                if (cell.innerText != "") {
                    var range = document.createRange();
                    range.selectNodeContents(cell);
                    var selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);
                    document.execCommand('copy');
                    selection.removeAllRanges();
                    layer.msg('复制完成');
                }
            });
        });

        function checkandsubmit(boxid) {
            var newid = "form" + boxid;
            document.getElementById('onthego').style.display = 'block';
            document.getElementById(newid).submit();
        }

        function changepasswd(id) {
            newpass = prompt("请输入新密码");
            if ((newpass != "") && (newpass !== null)) {
                document.getElementById("temp").src = "userlist.php?action=changepass&id=" + id + "&password=" + md5(md5(newpass) + 'Libra');
            }
        }
    </script>
    <iframe name=temp id=temp style='display:none'></iframe>
</body>

</html>