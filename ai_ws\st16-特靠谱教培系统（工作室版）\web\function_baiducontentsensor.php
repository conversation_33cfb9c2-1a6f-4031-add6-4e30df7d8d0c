<?php
function baiducontentsensorpassed($text)
{
    require_once('admin/mysqlconn.php');
    $row = $conn->get('main','*',['id'=>1]);
    $API_KEY = $row["baiduapikey"];
    $SECRET_KEY = $row["baidusecretkey"];
    
    $curl = curl_init();
    $postData = array(
        'grant_type' => 'client_credentials',
        'client_id' => $API_KEY,
        'client_secret' => $SECRET_KEY
    );
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://aip.baidubce.com/oauth/2.0/token',
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POSTFIELDS => http_build_query($postData)
    ));
    $response = curl_exec($curl);
    curl_close($curl);
    $rtn = json_decode($response);
    if (isset($rtn->error)) {
        return true;
        exit;
    }
    $accesstoken = $rtn->access_token;
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => "https://aip.baidubce.com/rest/2.0/solution/v1/text_censor/v2/user_defined?access_token=" . $accesstoken,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => 'text=' . $text,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ),
    ));
    $response = curl_exec($curl);
    curl_close($curl);
    $result = json_decode($response);

    if (isset($result->conclusionType)) {
        if ($result->conclusionType == 1) {
            return true;
        } else {
            return false;
        }
    }
}
