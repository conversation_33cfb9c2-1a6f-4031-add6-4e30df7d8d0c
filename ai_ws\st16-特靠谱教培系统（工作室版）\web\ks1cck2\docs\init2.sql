-- 用户表（已存在，假设为cck_users）
CREATE TABLE IF NOT EXISTS cck1_users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    group_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 选题表
CREATE TABLE IF NOT EXISTS cck1_topics (
    topic_id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL, -- 创建选题的用户ID
    FOREIGN KEY (created_by) REFERENCES cck_users(id) ON DELETE CASCADE
);

-- 资料表
CREATE TABLE IF NOT EXISTS cck1_materials (
    material_id INT AUTO_INCREMENT PRIMARY KEY,
    topic_id INT NOT NULL, -- 对应的选题ID
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL, -- 创建资料的用户ID
    FOREIGN KEY (topic_id) REFERENCES cck_topics(topic_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES cck_users(id) ON DELETE CASCADE
);

-- 用户选用资料记录表
CREATE TABLE IF NOT EXISTS cck1_material_assignments (
    assignment_id INT AUTO_INCREMENT PRIMARY KEY,
    material_id INT NOT NULL,
    user_id INT NOT NULL,
    checked_out_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    viewed_at TIMESTAMP NULL,
    FOREIGN KEY (material_id) REFERENCES cck_materials(material_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES cck_users(id) ON DELETE CASCADE,
    UNIQUE (material_id) -- 确保一个资料只能被一个用户选用
);

-- ## 11:09 2025/3/13

-- 禁用外键检查（MyISAM引擎下实际无效，仅为语法兼容）
SET FOREIGN_KEY_CHECKS = 0;

-- 用户表升级
ALTER TABLE cck1_users 
  MODIFY  COLUMN id INT UNSIGNED NOT NULL AUTO_INCREMENT,
  ADD COLUMN user_id INT UNSIGNED NOT NULL ,
  ADD COLUMN status TINYINT(1) NOT NULL DEFAULT 1 AFTER group_id,
  ADD COLUMN last_login DATETIME AFTER status,
  ADD COLUMN meta TEXT COMMENT '扩展字段' AFTER last_login,
  ENGINE=MyISAM, 
  CONVERT TO CHARACTER SET utf8;

-- 重建选题表（旧数据迁移）
CREATE TABLE cck1_topics_new LIKE cck1_topics;
INSERT INTO cck1_topics_new (topic_id, title, description, created_by, created_at)
SELECT 
  topic_id,
  title,
  description,
  created_by,
  DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s')
FROM cck1_topics;

RENAME TABLE cck1_topics TO cck1_topics_old, cck1_topics_new TO cck1_topics;

-- 创建扩展表
CREATE TABLE IF NOT EXISTS cck1_topic_meta (
    topic_id INT UNSIGNED NOT NULL,
    views INT UNSIGNED NOT NULL DEFAULT 0,
    favorites INT UNSIGNED NOT NULL DEFAULT 0,
    platforms VARCHAR(255) NOT NULL DEFAULT '' COMMENT '适用平台，逗号分隔',
    keywords VARCHAR(255) NOT NULL DEFAULT '',
    trend_data TEXT COMMENT 'JSON格式的时序数据',
    PRIMARY KEY (topic_id),
    INDEX idx_keywords (keywords(50))
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- 迁移热度数据（示例）
INSERT INTO cck1_topic_meta (topic_id, views, favorites)
SELECT 
  t.topic_id,
  IFNULL(SUM(a.access_count), 0) AS views,
  IFNULL(COUNT(f.fav_id), 0) AS favorites
FROM cck1_topics t
LEFT JOIN cck1_material_assignments a ON t.topic_id = a.topic_id
LEFT JOIN cck1_favorites f ON t.topic_id = f.topic_id
GROUP BY t.topic_id;

-- 资料表升级（保留历史版本）
ALTER TABLE cck1_materials
  ADD COLUMN file_type VARCHAR(50) NOT NULL AFTER file_path,
  ADD COLUMN version VARCHAR(20) NOT NULL DEFAULT '1.0',
  ADD COLUMN is_latest TINYINT(1) NOT NULL DEFAULT 1,
  ENGINE=MyISAM,
  CONVERT TO CHARACTER SET utf8;

-- 用户-资料关系表升级
ALTER TABLE cck1_material_assignments
  ADD COLUMN download_count INT UNSIGNED NOT NULL DEFAULT 0,
  ADD COLUMN access_count INT UNSIGNED NOT NULL DEFAULT 0,
  MODIFY COLUMN viewed_at DATETIME NULL,
  ENGINE=MyISAM,
  CONVERT TO CHARACTER SET utf8;

-- 新增审计日志表（扩展需求）
CREATE TABLE IF NOT EXISTS cck_audit_log (
    log_id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    user_id INT UNSIGNED NOT NULL,
    action_type VARCHAR(50) NOT NULL,
    target_type VARCHAR(50) NOT NULL,
    target_id INT UNSIGNED NOT NULL,
    old_value TEXT,
    new_value TEXT,
    logged_at DATETIME NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    PRIMARY KEY (log_id),
    INDEX idx_action (action_type),
    INDEX idx_target (target_type, target_id)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;


-- 标签系统
CREATE TABLE cck1_tags (
    tag_id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tag_name VARCHAR(50) NOT NULL UNIQUE
);

-- 选题-标签关联表
CREATE TABLE cck1_topic_tags (
    topic_id INT UNSIGNED NOT NULL,
    tag_id INT UNSIGNED NOT NULL,
    PRIMARY KEY (topic_id, tag_id)
);




-- 留言链接表
CREATE TABLE cck1_message_links (
    link_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    url VARCHAR(500) NOT NULL,
    title VARCHAR(200) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES cck1_users(user_id)
);

-- 预设留言表
CREATE TABLE cck1_message_previews (
    preview_id INT PRIMARY KEY AUTO_INCREMENT,
    link_id INT NOT NULL,
    message TEXT NOT NULL,
    uuid CHAR(32) NOT NULL, -- 增加 uuid 字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (link_id) REFERENCES cck1_message_links(link_id),
    UNIQUE KEY unique_message (link_id, uuid) -- 修改唯一性约束
);



INSERT INTO `cck1_groups` (`group_name`, `password`, `description`, `owner_id`) VALUES
('L1，定位探索期（0-100粉）', 'password1', '这是定位探索期的群组', 1),
('L2，冷启动期（100-300粉）', 'password2', '这是冷启动期的群组', 1),
('L3，冲刺开通期（300-500粉）', 'password3', '这是冲刺开通期的群组', 1),
('L4，流量池攻坚期（500-800粉）', 'password4', '这是流量池攻坚期的群组', 1),
('L5，商业跃升期（800-1000以上）', 'password5', '这是商业跃升期的群组', 1);

-- 20:15 2025/3/29

ALTER TABLE `cck1_message_previews`
ADD COLUMN `max_times` INT(11) NOT NULL DEFAULT 1 COMMENT '最大次数',
ADD COLUMN `current_times` INT(11) NOT NULL DEFAULT 0 COMMENT '当前次数';



ALTER TABLE cck1_link_views
ADD COLUMN duration INT NOT NULL DEFAULT 0 COMMENT '查看时长（单位：秒）';


ALTER TABLE `cck1_link_views`
ADD COLUMN `remark` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '备注信息';

ALTER TABLE `cck1_link_views`
ADD COLUMN `version` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '版本号';
