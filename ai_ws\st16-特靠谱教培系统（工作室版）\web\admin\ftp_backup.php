<?php
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_WARNING);
ignore_user_abort(true);
set_time_limit(0);
ini_set("max_execution_time", "180"); //避免数据量过大，导出不全的情况出现。
if ($_SERVER['HTTP_HOST']) {
    $server =  $_SERVER['HTTP_HOST'];
    $newline = "<br>";
} else {
    $server = "127.0.0.1";
    $newline = "\n";
}
$host = file_get_contents("http://" . $server . "/admin/sql_backup.php");
require_once('mysqlconn.php');

$host = "";
$user = "";
$pwd = "";
$dir = "";

$ftp = $conn->get('main','ftp',['id'=>1]);
if (!empty($ftp)) {
    $user = urldecode(explode(":", explode("@", $ftp)[0])[0]);
    $pwd = urldecode(explode(":", explode("@", $ftp)[0])[1]);
    $dir = urldecode(explode("/", $ftp)[1]);
    $host = urldecode(explode("/", explode("@", $ftp)[1])[0]);
}


if (empty($host)) {
    echo "No FTP information, program terminated.";
    exit(1);
}

if (strpos($host, ":") !== false) {
    $port = explode(":", $host)[1];
    $host = explode(":", $host)[0];
}

// 进行ftp连接，根据port是否设置，传递的参数会不同
if (empty($port)) {
    $f_conn = ftp_connect($host);
} else {
    $f_conn = ftp_connect($host, $port);
}
if (!$f_conn) {
    echo "FTP connection failed.";
    exit(1);
}
echo "***FTP BACKUP***" . $newline . "FTP connection succeeded." . $newline;

// 进行ftp登录，使用给定的ftp登录用户名和密码进行login
$f_login = ftp_login($f_conn, $user, $pwd);
if (!$f_login) {
    echo "User login failed." . $newline;
    exit(1);
}
echo "User login succeeded." . $newline;

// 获取当前所在ftp目录下包含的目录与文件
$exist_dir = ftp_nlist($f_conn, $dir);
if (!is_array($exist_dir)) {
    echo "Enter passive mode" . $newline;
    ftp_pasv($f_conn, true);
    $exist_dir = ftp_nlist($f_conn, $dir);
}

// 要求是按照日期在ftp目录下创建文件夹作为文件上传存放目录

$path = date("Ym");
$dir_name = $dir . $path;
// 检查ftp目录下是否已存在当前日期的文件夹，如不存在则进行创建
if ((!in_array($dir_name, $exist_dir)) && (!in_array($path, $exist_dir))) {
    if (!ftp_mkdir($f_conn, $dir_name)) {
        echo "Directory creation failed." . $newline;
        exit(1);
    } else {
        echo "Directory created." . $newline;
    }
} else {
    echo "Directory existed." . $newline;
}
// 切换目录
if (!ftp_chdir($f_conn, $dir_name)) {
    echo "Failed to change directory." . $newline;
    exit(1);
} else {
    echo "Directory changed." . $newline;
}

$filename = date("Ymd") . "_chatgpt.sql";
// 进行文件上传
$result = ftp_put($f_conn, $filename, __DIR__ . "/backup/" . $filename, FTP_BINARY);
if (!$result) {
    echo "SQL file upload failed." . $newline;
    exit(1);
} else {
    echo "SQL file '" . $filename . "' uploaded successfully." . $newline;
}
