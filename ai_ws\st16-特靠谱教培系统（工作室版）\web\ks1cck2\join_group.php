<?php
require '_ks1.php';
@session_start(); // 启动会话

// 检查用户是否登录
if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
    header('Location: login.php');
    exit;
}

// 获取用户ID
$user_id = $_SESSION[SESSION_KEY]['user']['id'];

// 获取用户输入的群组代号和群组口令
$group_code = isset($_POST['group_code']) ? trim($_POST['group_code']) : null;
$group_pawd = isset($_POST['group_pawd']) ? trim($_POST['group_pawd']) : null;

// 检查输入是否为空
if (empty($group_code) || empty($group_pawd)) {
    $_SESSION['error_message'] = '群组代号和群组口令不能为空！';
    header('Location: index.php');
    exit;
}

if($group_code=='new' && $group_pawd=='new'){
    $group_code='new';
    $group_pawd='';
}
// 验证群组代号和口令
$group = validate_group($group_code, $group_pawd);

if ($group) {
    // 更新用户所属群组
    if (assign_user_to_group($user_id, $group['group_id'])) {
        $_SESSION['success_message'] = '成功加入群组：' . htmlspecialchars($group['group_name']);
        $_SESSION[SESSION_KEY]['user']['group_id'] = $group['group_id'];

    } else {
        $_SESSION['error_message'] = '加入群组失败，请稍后再试！';
    }
} else {
    $_SESSION['error_message'] = '群组代号或口令错误！';
}

header('Location: index.php');
exit;

/**
 * 验证群组代号和口令
 * @param string $group_code 群组代号
 * @param string $group_pawd 群组口令
 * @return array|null 返回群组信息或null
 */
function validate_group($group_code, $group_pawd) {
    $table_name=get_table_name('groups');
    $sql = "
        SELECT group_id, group_name
        FROM $table_name
        WHERE code = ?s AND password = ?s
        LIMIT 1
    ";
    $sql = prepare($sql, array($group_code, $group_pawd));
    //if(DEBUG_MODE)  echo __METHOD__.'() sql='.$sql.'<br>';die();
    return get_line($sql);
}

/**
 * 将用户分配到指定群组
 * @param int $user_id 用户ID
 * @param int $group_id 群组ID
 * @return bool 是否成功
 */
function assign_user_to_group($user_id, $group_id) {
    $table_name=get_table_name('users');
    $sql = "
        UPDATE $table_name
        SET group_id = ?i
        WHERE id = ?i
    ";
    $sql = prepare($sql, array($group_id, $user_id));
    return run_sql($sql);
}