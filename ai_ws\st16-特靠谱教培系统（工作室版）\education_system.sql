-- 教培系统数据库结构

-- 教师表
CREATE TABLE `teacher` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '关联用户表的ID',
  `name` varchar(50) NOT NULL COMMENT '教师姓名',
  `subject` varchar(100) DEFAULT NULL COMMENT '教授科目',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮箱',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-停用',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='教师信息表';

-- 学生表
CREATE TABLE `student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '关联用户表的ID',
  `name` varchar(50) NOT NULL COMMENT '学生姓名',
  `gender` tinyint(1) DEFAULT NULL COMMENT '性别：1-男，2-女',
  `birthday` date DEFAULT NULL COMMENT '出生日期',
  `grade` varchar(20) DEFAULT NULL COMMENT '年级',
  `parent_name` varchar(50) DEFAULT NULL COMMENT '家长姓名',
  `parent_phone` varchar(20) DEFAULT NULL COMMENT '家长电话',
  `address` varchar(200) DEFAULT NULL COMMENT '家庭住址',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-停用',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='学生信息表';

-- 教室表
CREATE TABLE `classroom` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '教室名称',
  `capacity` int(11) DEFAULT NULL COMMENT '容纳人数',
  `location` varchar(100) DEFAULT NULL COMMENT '位置',
  `equipment` varchar(255) DEFAULT NULL COMMENT '设备描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-可用，0-维护中',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='教室信息表';

-- 课程表
CREATE TABLE `course` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '课程名称',
  `description` text COMMENT '课程描述',
  `teacher_id` int(11) NOT NULL COMMENT '教师ID',
  `max_students` int(11) DEFAULT '20' COMMENT '最大学生数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-停课',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_teacher_id` (`teacher_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='课程信息表';

-- 课程安排表
CREATE TABLE `course_schedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) NOT NULL COMMENT '课程ID',
  `classroom_id` int(11) NOT NULL COMMENT '教室ID',
  `day_of_week` tinyint(1) NOT NULL COMMENT '星期几：1-7代表周一至周日',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-取消',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_classroom_id` (`classroom_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='课程安排表';

-- 学生选课表
CREATE TABLE `student_course` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL COMMENT '学生ID',
  `course_id` int(11) NOT NULL COMMENT '课程ID',
  `join_date` date NOT NULL COMMENT '加入日期',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-退课',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_student_course` (`student_id`,`course_id`),
  KEY `idx_course_id` (`course_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='学生选课表';

-- 请假表
CREATE TABLE `leave_request` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL COMMENT '学生ID',
  `course_schedule_id` int(11) NOT NULL COMMENT '课程安排ID',
  `leave_date` date NOT NULL COMMENT '请假日期',
  `leave_type` tinyint(1) NOT NULL COMMENT '请假类型：1-病假，2-事假，3-其他',
  `reason` varchar(255) NOT NULL COMMENT '请假原因',
  `attachment` varchar(255) DEFAULT NULL COMMENT '附件路径',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-待审批，1-已批准，2-已拒绝',
  `approve_by` int(11) DEFAULT NULL COMMENT '审批人ID',
  `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approve_remark` varchar(255) DEFAULT NULL COMMENT '审批备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_course_schedule_id` (`course_schedule_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='请假申请表';

-- 考勤记录表
CREATE TABLE `attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_schedule_id` int(11) NOT NULL COMMENT '课程安排ID',
  `student_id` int(11) NOT NULL COMMENT '学生ID',
  `attendance_date` date NOT NULL COMMENT '考勤日期',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-缺席，1-出席，2-请假',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_schedule_student_date` (`course_schedule_id`,`student_id`,`attendance_date`),
  KEY `idx_student_id` (`student_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='考勤记录表';

-- 用户角色表
CREATE TABLE `user_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role_type` tinyint(1) NOT NULL COMMENT '角色类型：1-管理员，2-教师，3-学生',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`,`role_type`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='用户角色表';