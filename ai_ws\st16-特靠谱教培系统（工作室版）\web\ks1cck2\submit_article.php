<?php
require '_ks1.php';
@session_start();
if (!isset($_SESSION[SESSION_KEY]['user']['id']) ) {
    header('Location: login.php');
    exit;
}


$user_id = $_SESSION[SESSION_KEY]['user']['id'];
$role = $_SESSION[SESSION_KEY]['user']['role'];
$username = $_SESSION[SESSION_KEY]['user']['username'];


$jump_url='dashboard.php';
$jump_delay='2';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $link = $_POST['link'];
    if (submit_article($user_id, $link)) {
        echo "Article submitted successfully.";
    } else {
        echo "Failed to submit article.";
        $jump_delay='60';
    }
}



?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>提交新文章链接</title>
</head>
<body>
<p>请稍候，<span id="totalSecond" style="color:red"><?php echo $jump_delay;?></span>秒后自动跳转…</p>

<p><a href="<?php echo $jump_url;?>" target="_self">立即跳转</p>

<script language="javascript" type="text/javascript">
    var second = document.getElementById('totalSecond').textContent;

    if (navigator.appName.indexOf("Explorer") > -1) {
        second = document.getElementById('totalSecond').innerText;
    } else {
        second = document.getElementById('totalSecond').textContent;
    }

    setInterval("redirect()", 1000);

    function redirect() {
        if (second < 0) {
            location.href = '<?php echo $jump_url;?>';
        } else {
            if (navigator.appName.indexOf("Explorer") > -1) {
                document.getElementById('totalSecond').innerText = second--;
            } else {
                document.getElementById('totalSecond').textContent = second--;
            }
        }
    }
</script>