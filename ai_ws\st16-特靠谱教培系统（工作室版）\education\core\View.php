<?php
namespace Core;

/**
 * 视图类
 */
class View
{
    private $layout = 'default';
    
    /**
     * 构造函数
     */
    public function __construct()
    {
    }
    
    /**
     * 设置布局
     * 
     * @param string $layout 布局名称
     */
    public function setLayout($layout)
    {
        $this->layout = $layout;
    }
    
    /**
     * 渲染视图
     * 
     * @param string $view 视图名称
     * @param array $data 数据
     * @return string 渲染结果
     */
    public function render($view, $data = [])
    {
        // 提取数据到变量
        extract($data);
        
        // 获取视图内容
        ob_start();
        $viewFile = APP_PATH . '/views/' . $view . '.php';
        if (file_exists($viewFile)) {
            require $viewFile;
        } else {
            throw new \Exception('View file not found: ' . $viewFile);
        }
        $content = ob_get_clean();
        
        // 使用布局
        if ($this->layout) {
            ob_start();
            $layoutFile = APP_PATH . '/views/layouts/' . $this->layout . '.php';
            if (file_exists($layoutFile)) {
                require $layoutFile;
            } else {
                throw new \Exception('Layout file not found: ' . $layoutFile);
            }
            return ob_get_clean();
        }
        
        return $content;
    }
    
    /**
     * 部分视图
     * 
     * @param string $view 视图名称
     * @param array $data 数据
     * @return string 渲染结果
     */
    public function partial($view, $data = [])
    {
        // 提取数据到变量
        extract($data);
        
        // 获取视图内容
        ob_start();
        $viewFile = APP_PATH . '/views/partials/' . $view . '.php';
        if (file_exists($viewFile)) {
            require $viewFile;
        } else {
            throw new \Exception('Partial view file not found: ' . $viewFile);
        }
        return ob_get_clean();
    }
}