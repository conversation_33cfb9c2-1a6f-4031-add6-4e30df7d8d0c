<?php
header("Access-Control-Allow-Origin: *");
$daysToKeep = 7; // 保留文件天数
$token = "NewBee"; // 后台配置好的token
$uploadDir = 'u/'; // 上传目录，注意建立好这个目录并打开写权限


if ((!isset($_POST["action"])) && (!isset($_GET["action"]))) {
    header('HTTP/1.1 404 Not Found');
    exit;
}
if (((isset($_POST["action"])) && ($_POST["action"] == "getnonce")) || ((isset($_GET["action"])) && ($_GET["action"] == "getnonce"))) {
    echo time();
    exit;
}
if ((isset($_POST["action"])) && ($_POST["action"] == "upload")) {
    $nonce = $_POST['nonce'];
    $sign = $_POST['sign'];
}
if (((time() - $nonce) > 60) || ((time() - $nonce) < 0)) {
    http_response_code(403); // 返回403错误，表示禁止访问
    echo "Paramter nonce is out of date";
    exit;
}
if ($sign != md5(md5($nonce) . $token)) {
    http_response_code(403);
    echo "Paramter sign is invalid";
    
    exit;
}

// 删除超过保留天数的文件
$files = glob($uploadDir . '*'); // 获取所有文件
foreach ($files as $file) {
    if (is_file($file)) {
        $fileCreationTime = filemtime($file);
        $fileAge = time() - $fileCreationTime; // 文件年龄，以秒为单位
        if ($fileAge > ($daysToKeep * 24 * 60 * 60)) { // 如果文件年龄超过保留天数
            unlink($file); // 删除文件
        }
    }
}

// 处理上传的图片
if ($_FILES['image']['error'] === UPLOAD_ERR_OK) {
    $filename = $_FILES['image']['name'];
    $extension = pathinfo($filename, PATHINFO_EXTENSION);
    $uploadFile = $uploadDir . time() . '.' . $extension; // 以时间戳为文件名

    // 如果文件已存在，则加上后缀_1、_2等后缀
    $i = 1;
    while (file_exists($uploadFile)) {
        $uploadFile = $uploadDir . time() . '_' . $i . '.' . $extension;
        $i++;
    }

    // 判断文件类型是否为图片
    $imageInfo = getimagesize($_FILES['image']['tmp_name']);
    if ($imageInfo === false) {
        http_response_code(400); // 返回400错误，表示客户端请求错误
        echo "Invalid file type";
        exit;
    }

    // 允许的图片类型
    $allowedTypes = array(IMAGETYPE_JPEG, IMAGETYPE_PNG, IMAGETYPE_GIF);
    if (!in_array($imageInfo[2], $allowedTypes)) {
        http_response_code(400); // 返回400错误，表示客户端请求错误
        echo "Unsupported file type";
        exit;
    }

    if (move_uploaded_file($_FILES['image']['tmp_name'], $uploadFile)) {
        $httpprotocol = "http://";
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
            $httpprotocol = "https://";
        }
        $imageUrl = $httpprotocol . $_SERVER['HTTP_HOST'] . "/" . $uploadFile; // 图片链接
        echo $imageUrl;
    } else {
        http_response_code(500); // 返回500错误，表示服务器内部错误
        echo "Failed to move the uploaded file";
    }
} else {
    http_response_code(400); // 返回400错误，表示客户端请求错误
    echo "Failed to upload the file";
}
