<?php
set_time_limit(0);
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);
require_once('check_admin.php');
require_once('mysqlconn.php');
if ((isset($_POST["action"])) && ($_POST["action"] == "update")) {
    if ($conn->has('wxpaylist',['id'=>$_POST["id"]])) {
        $conn->update('wxpaylist',['memo'=>$_POST['memo']],['id'=>$_POST["id"]]);
        echo "<html><head><meta charset=utf-8></head><body><script>parent.document.getElementById('onthego').style.display='none';</script></body></html>";
    } else {
        echo '<html><head><meta charset=utf-8></head><body><script>alert("充值记录不存在！");parent.location.reload();</script>';
    }
    exit;
}

$result = $conn->select('cardtype','*',['ORDER'=>'id']);
$cardtypearray = array();
foreach ($result as $row) {
    $temparray = array($row["cardname"], $row["price"], $row["quota"], $row["extenddays"]);
    $cardtypearray[$row["id"]] = $temparray;
}
$ipagecurrent = isset($_REQUEST["page"]) ? $_REQUEST["page"] : 1;
$searchitem = isset($_REQUEST["searchitem"]) ? $_REQUEST["searchitem"] : '';
$keyword = isset($_REQUEST["keyword"]) ? $_REQUEST["keyword"] : '';
$order = isset($_REQUEST["order"]) ? $_REQUEST["order"] : '';
$orderdesc = isset($_REQUEST["orderdesc"]) ? $_REQUEST["orderdesc"] : 'true';
$pagenumber = 20; //每页显示数据条数

?>


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>微信支付订单</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <link rel="stylesheet" href="../css/layui.css">
    <script src="../js/layui.js" type="application/javascript"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        form {
            margin: 0px;
            display: inline
        }

        select {
            vertical-align: bottom;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑';overflow-x: auto">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-money"></i> 充值管理</li>
                <li class="active">微信支付订单</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px;">
            <div class="row">
                <div class="space-6"></div>
                <div class="col-xs-12">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <td colspan="15">
                                    <form target=_self method=get style="margin:0px;display: inline">
                                        搜索：<select name=searchitem style='width:120px;margin-right:10px;' onchange='changecontent(this.value);'>
                                            <option value="">请选择</option>
                                            <option value=userid <?php if ($searchitem == "userid") echo "selected"; ?>>用户UID</option>
                                            <option value=cardtype <?php if ($searchitem == "cardtype") echo "selected"; ?>>卡种名称</option>
                                            <option value=ordertime <?php if ($searchitem == "ordertime") echo "selected"; ?>>下单时间</option>
                                            <option value=confirmtime <?php if ($searchitem == "confirmtime") echo "selected"; ?>>支付时间</option>
                                            <option value=attachid <?php if ($searchitem == "attachid") echo "selected"; ?>>内部单号</option>
                                            <option value=out_trade_no <?php if ($searchitem == "out_trade_no") echo "selected"; ?>>外部单号</option>
                                            <option value=transaction_id <?php if ($searchitem == "transaction_id") echo "selected"; ?>>交易单号</option>
                                            <option value=payopenid <?php if ($searchitem == "payopenid") echo "selected"; ?>>支付openid</option>
                                            <option value=isconfirmed <?php if ($searchitem == "isconfirmed") echo "selected"; ?>>已付</option>
                                            <option value=memo <?php if ($searchitem == "memo") echo "selected"; ?>>备注</option>

                                        </select>
                                        <span id=keywordpanel style="display:<?php if (($searchitem == "ordertime") || ($searchitem == "confirmtime") || ($searchitem == "cardtype")) echo "none"; ?>;">关键字：<input name=keyword value="<?php echo $keyword; ?>" id='keyword' style='width:140px;height:30px;font-size:14px;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;padding-left:8px;'></span>
                                        <span id=selecttime style="display:<?php if (($searchitem != "ordertime") && ($searchitem != "confirmtime")) echo "none"; ?>;">时间范围：<input size=12 name=time1 style="height:30px;font-size:14px;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;padding-left:5px;" value='<?php echo isset($_REQUEST["time1"]) ? $_REQUEST["time1"] : date("Y-m-d"); ?>'> 至 <input style="padding-left:5px;height:30px;font-size:14px;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;" size=12 name=time2 value='<?php echo isset($_REQUEST["time2"]) ? $_REQUEST["time2"] : date("Y-m-d"); ?>'></span>
                                        <span id=selectcardtype style="display:<?php if ($searchitem != "cardtype") echo "none"; ?>;">
                                            <select id="cardtype" name="cardtype">
                                                <option value="">请选择</option>
                                                <?php
                                                $result = $conn->select("cardtype","*",["ishidden[!]" => 1,"ORDER"=>"id"]);
                                                foreach ($result as $row) {
                                                    echo '<option value="' . $row["id"] . '" ' . (($_REQUEST["cardtype"] == $row["id"]) ? "selected" : "") . '>' . $row["cardname"] . '</option>';
                                                }
                                                ?>
                                            </select>
                                        </span>
                                        &nbsp;&nbsp;&nbsp;按照
                                        <select name=order style="width:120px;">
                                            <option value="">请选择</option>
                                            <option value=userid <?php if ($order == "userid") echo "selected"; ?>>用户UID</option>
                                            <option value=cardtype <?php if ($order == "cardtype") echo "selected"; ?>>卡种名称</option>
                                            <option value=ordertime <?php if ($order == "ordertime") echo "selected"; ?>>下单时间</option>
                                            <option value=confirmtime <?php if ($order == "confirmtime") echo "selected"; ?>>支付时间</option>
                                            <option value=isconfirmed <?php if ($order == "isconfirmed") echo "selected"; ?>>已付</option>
                                            <option value=memo <?php if ($order == "memo") echo "selected"; ?>>备注</option>
                                        </select>&nbsp;&nbsp;&nbsp;
                                        <select name=orderdesc style="width:100px;">
                                            <option value="desc" <?php if ($orderdesc == 'desc') echo "selected"; ?>>降序排列</option>
                                            <option value="">升序排列</option>
                                        </select>
                                        &nbsp;&nbsp;&nbsp;
                                        <button class="btn btn-sm btn-info" style="padding:2px 10px;" type=submit>查询</button>
                                        <!--
                                        <button class="btn btn-sm btn-info" style="padding:2px 10px;" onclick="document.getElementById('temp').src='saveexcel_wxpay.php';return false;">导出所有订单</button>
                                        -->
                                    </form>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-center">编号</th>
                                <th class="text-center">用户</th>
                                <th class="text-center">卡种名称</th>
                                <th class="text-center">价格</th>
                                <th class="text-center">额度</th>
                                <th class="text-center">天数</th>
                                <th class="text-center">下单时间</th>
                                <th class="text-center">支付时间</th>
                                <th class="text-center">用户IP</th>
                                <th class="text-center" style="min-width:100px;">内部单号</th>
                                <th class="text-center" style="min-width:100px;">外部单号</th>
                                <th class="text-center" style="min-width:100px;">交易单号</th>
                                <th class="text-center" style="min-width:100px;">支付openid</th>
                                <th class="text-center">已付</th>
                                <th class="text-center">备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $sql = "select * from wxpaylist ";

                            if ($searchitem != "") {
                                if ($searchitem == "cardtype") {
                                    if ($_REQUEST["cardtype"]) {
                                        $sql .= " where " . addslashes($searchitem) . " = " . addslashes($_REQUEST["cardtype"]) . " ";
                                    }
                                } else if (($searchitem == "ordertime") || ($searchitem == "confirmtime")) {
                                    $sql .= " where " . addslashes($searchitem) . " between '" . addslashes($_REQUEST["time1"]) . "' and '" . addslashes(date("Y-m-d", strtotime("+1 day", strtotime($_REQUEST["time2"])))) . "' ";
                                } else {
                                    $sql .= " where " . addslashes($searchitem) . " like '%" . addslashes($keyword) . "%' ";
                                }
                            }
                            if ($order !== "") {
                                $sql .= " order by " . addslashes($order) . " " . addslashes($orderdesc);
                            } else {
                                $sql .= " order by id desc";
                            }

                            $sqlcount = "select count(t.id) from (" . $sql . ") t";
                            $result = $conn->query($sqlcount);
                            $row = $result->fetch();
                            $totalnumber = $row[0];
                            $ipagecount = ceil($totalnumber / $pagenumber);
                            if ($totalnumber == 0) {
                                echo "<tr><td colspan=15>暂无微信支付订单</td></tr>";
                            } else {
                                if ($ipagecurrent > $ipagecount) $ipagecurrent = $ipagecount;
                                if ($ipagecurrent < 1) $ipagecurrent = 1;
                                $count = 0;
                                $startcount = ($ipagecurrent - 1) * $pagenumber;
                                $sql .= " limit " . $startcount . "," . $pagenumber;
                                $result = $conn->query($sql);
                                while ($row = $result->fetch()) {
                                    $count++;
                            ?>
                                    <tr>
                                        <form id="form<?php echo $row["id"] ?>" method=post target="temp" action="wxpaylist.php" onsubmit="checkandsubmit('<?php echo $row["id"] ?>');">
                                            <input type=hidden name=id value="<?php echo $row["id"] ?>"><input type=hidden name=action value=update>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;width:50px;min-width:50px;max-width:50px;"><?php echo $count ?></td>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;width:60px;min-width:60px;max-width:60px;"><?php echo $row["userid"] ?></td>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;width:100px;min-width:100px;max-width:100px;"><?php echo $cardtypearray[$row["cardtype"]][0] ?></td>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;width:60px;in-width:60px;max-width:60px;"><?php echo $cardtypearray[$row["cardtype"]][1] ?> 元</td>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;width:80px;min-width:80px;max-width:8px;"><?php echo $cardtypearray[$row["cardtype"]][2] ?> 次</td>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;width:8px;min-width:80px;max-width:80px;"><?php echo $cardtypearray[$row["cardtype"]][3] ?> 天</td>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;width:160px;min-width:160px;max-width:160px;"><?php echo $row["ordertime"] ?></td>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;width:160px;min-width:160px;max-width:160px;"><?php echo $row["confirmtime"] ?></td>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;width:120px;min-width:120px;max-width:120px;"><?php echo $row["clientip"] ?></td>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;min-width:100px;max-width:100px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"><?php echo $row["attachid"] ?></td>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;min-width:100px;max-width:100px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"><?php echo $row["out_trade_no"] ?></td>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;min-width:100px;max-width:100px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"><?php echo $row["transaction_id"] ?></td>
                                            <td title="双击复制" class="text-center" style="cursor:pointer;min-width:100px;max-width:100px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"><?php echo $row["payopenid"] ?></td>
                                            <td class="text-center" style="width:50px;min-width:50px;max-width:50px;"><?php echo ($row["isconfirmed"]) ? "是" : "否" ?></td>
                                            <td class="text-center"><input name=memo style="width:100%;" value="<?php echo $row["memo"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                        </form>
                                    </tr>
                            <?php
                                }
                            }
                            
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php
        echo "<table width='100%' border='0' cellspacing='0' cellpadding='5'><tr><td height='20' align=center style='border-width:0pt'>每页显示 " . $pagenumber . " 个记录　共有 " . $ipagecount . " 页　当前为第 " . $ipagecurrent . " 页 ";
        if ($ipagecurrent == 1) {
            echo "　　首页　 | ";
        } else {
            echo "<form method=post onclick='this.submit();' style='cursor:pointer;'><input type=hidden name=page value='1'>　　首页　</form> | ";
        }
        if ($ipagecurrent == 1) {
            echo "　上一页　 | ";
        } else {
            echo "<form method=post onclick='this.submit();' style='cursor:pointer;'><input type=hidden name=page value='" . ($ipagecurrent - 1) . "'>　上一页　</form> | ";
        }
        if ($ipagecount > $ipagecurrent) {
            echo "<form method=post onclick='this.submit();' style='cursor:pointer;'><input type=hidden name=page value='" . ($ipagecurrent + 1) . "'>　下一页　</form> ";
        } else {
            echo "　下一页　";
        }
        if ($ipagecount > $ipagecurrent) {
            echo "| <form method=post onclick='this.submit();' style='cursor:pointer;'><input type=hidden name=page value='" . $ipagecount . "'>　末页　</form> ";
        } else {
            echo "| 　末页　 ";
        }
        echo "</td></tr></table>";
        ?>
    </div>
    <script>
        function checkandsubmit(boxid) {
            var newid = "form" + boxid;
            document.getElementById('onthego').style.display = 'block';
            document.getElementById(newid).submit();
        }

        function changecontent(cnt) {
            if (cnt == "cardtype") {
                document.getElementById("selectcardtype").style.display = "";
                document.getElementById("selecttime").style.display = "none";
                document.getElementById("keywordpanel").style.display = "none";
                document.getElementById("keywordpanel").value = "";
            } else if ((cnt == "ordertime") || (cnt == "confirmtime")) {
                document.getElementById("selectcardtype").style.display = "none";
                document.getElementById("selecttime").style.display = "";
                document.getElementById("keywordpanel").style.display = "none";
                document.getElementById("keywordpanel").value = "";
            } else {
                document.getElementById("selectcardtype").style.display = "none";
                document.getElementById("selecttime").style.display = "none";
                document.getElementById("keywordpanel").style.display = "";
                document.getElementById("keywordpanel").value = "";
            }
        }
        var cells = document.querySelectorAll('.text-center');

        cells.forEach(function(cell) {
            cell.addEventListener('dblclick', function() {
                if (cell.innerText != "") {
                    var range = document.createRange();
                    range.selectNodeContents(cell);
                    var selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);
                    document.execCommand('copy');
                    selection.removeAllRanges();
                    layer.msg('复制完成');
                }
            });
        });
    </script>
    <iframe style="display:none;" id=result name=temp></iframe>
</body>

</html>