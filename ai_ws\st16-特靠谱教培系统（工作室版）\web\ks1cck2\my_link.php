<?php
require '_ks1.php';
@session_start();
if (!isset($_SESSION[SESSION_KEY]['user']['id']) || !in_array($_SESSION[SESSION_KEY]['user']['role'], ['user', 'admin'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION[SESSION_KEY]['user']['id'];
$role = $_SESSION[SESSION_KEY]['user']['role'] ?? null;
$username = $_SESSION[SESSION_KEY]['user']['username'] ?? null;

$pub_name = get_public_account_name($user_id);

// 获取用户提交的所有链接
$today_links = get_all_submitted_links($user_id, true);
$history_links = get_all_submitted_links($user_id, false);

?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><?php echo $appName; ?>仪表盘</title>
    <link rel="stylesheet" href="res/wx_style.css">    
    <link rel="stylesheet" href="res/ks1table.css">
    <style>
        p{
            text-align: center;
            margin: 10px 0;
        }

        .read-count-input {
            margin-left: 10px;
        }


    </style>
</head>
<body>
    <div>
        <center>
            <h2>欢迎，<?php echo $pub_name; ?></h2>
            <span><?php 
if ($username=='default_user'){
    echo '请先到[profile]中修改昵称!';
}else{
    echo $username;
}        
if (!empty($$email)) echo ' '.$email;
if (!empty($user_group_id)) echo ' 组id='.$user_group_id;

echo ' (#' . $user_id; ?>)</span>
        </center>
        <?php echo $nav_html; ?>
    </div>

    <h2>今天提交的链接</h2>
    <?php if ($today_links): ?>
        <ul>
            <?php foreach ($today_links as $link): ?>
                <li>
                    <a href="<?php echo htmlspecialchars($link['url']); ?>" target="_blank">
                        <?php
                        if (isset($link['title']) && !empty($link['title'])) {
                            echo htmlspecialchars($link['title']); // 显示标题
                        } else {
                            $tmp3=fn_mini_link($link['url']);
                            echo htmlspecialchars($tmp3);
                        }
                        ?>
                    </a>
                    <br><small>提交时间：<?php echo htmlspecialchars(hide_today_ymd($link['created_at'])); ?></small>
                    <small>查看次数：<?php echo $link['view_count']; ?></small>
                    <?php if ($link['view_count']>0): ?>
                    <br>
                    <a href="javascript:void(0);" onclick="showViewDetails(<?php echo $link['link_id']; ?>)">显示详情</a>
                    <?php endif; ?>
                </li>
            <?php endforeach; ?>
        </ul>
    <?php else: ?>
        <p>今天还没有提交任何链接。</p>
    <?php endif; ?>

    <h2>历史提交的链接</h2>
    <?php if ($history_links): ?>
        <ul>
            <?php foreach ($history_links as $link): ?>
                <li>
                    <a href="<?php echo htmlspecialchars($link['url']); ?>" target="_blank">
                        <?php
                        if (isset($link['title']) && !empty($link['title'])) {
                            echo htmlspecialchars($link['title']); // 显示标题
                        } else {
                            $tmp3=fn_mini_link($link['url']);
                            echo htmlspecialchars($tmp3);
                        }
                        ?>
                    </a>
                    <br><small>提交时间：<?php echo htmlspecialchars(hide_today_ymd($link['created_at'])); ?></small>
                    <br><small>查看次数：<?php echo $link['view_count']; ?></small>
                    <?php if ($link['view_count']>0): ?>
                    <br>
                    <a href="javascript:void(0);" onclick="showViewDetails(<?php echo $link['link_id']; ?>)">显示详情</a>
                    <?php endif; ?>
                </li>
            <?php endforeach; ?>
        </ul>
    <?php else: ?>
        <p>还没有提交任何历史链接。</p>
    <?php endif; ?>
    
    <div class="view-details-layer" id="viewDetailsLayer">
        <div class="view-details-content" id="viewDetailsContent">
            <span class="view-details-close" onclick="closeViewDetails()">×</span>
            <div id="viewDetailsTable"></div>
        </div>
    </div>

    <div>
        <?php echo $nav_html; ?>
    </div>

<script>
    function showViewDetails(linkId) {
        var xhr = new XMLHttpRequest();
        xhr.open('GET', 'get_link_views_details.php?link_id=' + linkId, true);
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4 && xhr.status === 200) {
                var response = JSON.parse(xhr.responseText);
                var tableHtml = '<table><thead><tr><th>时间</th><th>用户</th><th>报阅读数</th><th>阅读时长(秒)</th></tr></thead><tbody>';
                if (response.length > 0) {
                    for (var i = 0; i < response.length; i++) {
                        tableHtml += '<tr><td>' + response[i].viewed_at + '</td><td>' + response[i].username + '</td><td>' + response[i].read_count + '</td><td>' + response[i].duration + '</td></tr>';
                    }
                } else {
                    tableHtml += '<tr><td colspan="4">暂无查看记录。</td></tr>';
                }
                tableHtml += '</tbody></table>';
                document.getElementById('viewDetailsTable').innerHTML = tableHtml;
                document.getElementById('viewDetailsLayer').style.display = 'block';
            }
        };
        xhr.send();
    }

    function closeViewDetails() {
        document.getElementById('viewDetailsLayer').style.display = 'none';
    }
</script>
</body>
</html>

<?php


// 获取用户提交的所有链接及其查看次数
function get_all_submitted_links($user_id, $is_today = true) {
    $table_name_links = get_table_name('links');
    $table_name_views = get_table_name('link_views');
    $where_clause = '';
    if ($is_today) {
        $today = date('Y-m-d');
        $where_clause = " AND DATE(l.created_at) = ?s";
    }else{
        $today = date('Y-m-d');
        $where_clause = " AND DATE(l.created_at) != ?s";
    }
    $sql = "
        SELECT 
            l.link_id, l.url, l.title, l.created_at, 
            COUNT(v.view_id) AS view_count
        FROM 
            $table_name_links l
        LEFT JOIN 
            $table_name_views v ON l.link_id = v.link_id
        WHERE 
            l.user_id = ?i $where_clause
        GROUP BY 
            l.link_id
        ORDER BY 
            l.created_at DESC
    ";
    //echo $sql .'<hr>';
    $sql = prepare($sql, array($user_id, $today ));
    //echo $sql .'<hr>';
    return get_data($sql);
}


?>