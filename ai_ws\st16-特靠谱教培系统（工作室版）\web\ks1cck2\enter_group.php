<?php
require '_ks1.php';
@session_start();

if (!is_logged_in()) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION[SESSION_KEY]['user']['id'];
$role = $_SESSION[SESSION_KEY]['user']['role'] ?? null;
$username = $_SESSION[SESSION_KEY]['user']['username'] ?? null;
$user_group_id = $_SESSION[SESSION_KEY]['user']['group_id'] ?? null;

if (intval($user_group_id)<=1) {
    $_SESSION['error_message'] = '您尚未加入任何群组，请先加入群组。';
    header('Location: index.php');
    exit;
}

$_SESSION['temp_group_mode']="1";

// 获取用户所属群组中的文章列表
$articles = get_unchecked_articles_by_group($user_id, $user_group_id);

read_time_check($articles);

?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>群组仪表盘</title>
    <!-- 引入 CSS 文件 -->
    <link rel="stylesheet" href="res/wx_style.css">    
    <link rel="stylesheet" href="res/ks1table.css">
</head>
<body>
    <div>
        <center>
            <h2>欢迎，<?php echo $pub_name; ?> 群组模式</h2>
            <span><?php 

if ($username=='default_user'){
    echo '请先到[profile]中修改昵称!';
}else{
    echo $username;
}        
if (!empty($$email)) echo ' '.$email;
echo ' 组id='.$user_group_id;

?></span>
        </center>
        <?php echo $nav_html; ?>
    </div>

    <h2>群组中的未阅读文章</h2>
    <ul>
        <?php
        if ($articles) {
            foreach ($articles as $article) {
                echo '<li>';
                echo '<a href="temporary_page.php?article_id=' . $article['id'] . '&link=' . urlencode($article['link']) . '">' . htmlspecialchars(fn_mini_link($article['link'])) . '</a> 提交人：' . htmlspecialchars($article['username']) . ' ';
                echo '<br><strong>标题：</strong>' . htmlspecialchars($article['title'] ?? '无标题'); // 显示标题
                echo '<form method="post" action="check_article.php" style="display:inline;">';
                echo '<input type="hidden" name="article_id" value="' . $article['id'] . '">';
                echo '<hr><label for="read_count">文章已有阅读数：</label>';
                echo '<input type="number" name="read_count" id="read_count" required placeholder="输入数字（如1.7万需写17000）" class="read-count-input">';
                echo '<button type="submit">提交阅读数</button>';
                echo '</form>';
                echo '</li>';
            }
        } else {
            echo '<li>暂无未阅读文章。</li>';
        }
        ?>
    </ul>
    
    <div>
        <?php echo $nav_html; ?>
    </div>
</body>
</html>