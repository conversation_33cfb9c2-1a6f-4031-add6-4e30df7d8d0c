<html>

<head>
    <style>
        table {
            border-collapse: collapse;
            background-color: #f9f9f9;
            font-family: Arial, sans-serif;
            font-size: 12px;
            table-layout: fixed;
            width: 100%;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            word-break: break-all;
            white-space: normal;
            vertical-align: top;
        }

        .th1 {
            width: 24px;
            min-width: 24px;
        }

        .th2 {
            width: 65px;
            min-width: 65px;
        }

        @media (min-width:600px) {
            .th1 {
                width: 30px;
                min-width: 24px;
            }

            .th2 {
                width: 120px;
                min-width: 60px;
            }
        }

        pre {
            white-space: pre-wrap;
        }

        .center {
            text-align: center;
        }

        th {
            background-color: #f2f2f2;
            text-align: left;
        }

        tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            font-size: 14px;
        }

        .pagination span,
        .pagination a {
            display: inline-block;
            padding: 5px 10px;
            margin: 0 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
            color: #333;
            text-decoration: none;
        }

        .pagination span.current {
            background-color: #007bff;
            color: #fff;
            border-color: #007bff;
        }

        .pagination a:hover {
            background-color: #007bff;
            color: #fff;
            border-color: #007bff;
        }
    </style>
</head>

<body>
    <?php

    require_once('admin/mysqlconn.php');
    $userrndstr = $_GET["userrndstr"];
    $userid = $conn->get('user','id',['rndstr'=>$userrndstr]);
    $totalCount = $conn->count("chathistory", ["userid" => $userid]);
    //获取当前页码
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;

    //每页显示的记录数
    $pageSize = 5;

    //计算总页数
    $totalPage = ceil($totalCount / $pageSize);

    //计算当前页的起始记录
    $start = ($page - 1) * $pageSize;
    $num = $start;

    //获取聊天记录
    $result = $conn->select("chathistory","*",["userid"=>$userid,"ORDER"=>["id"=>"DESC"],"LIMIT"=>[$start,$pageSize]]);
    //输出表格
    echo "<table>
        <tr>
            <th class='th1' class='center'>序号</th>
            <th class='th2' class='center'>时间 / 模型</th>
            <th>问答记录</th>
        </tr>";
    if (!empty($result)) {
        // 输出每行数据
        foreach ($result as $row) {
            $num++;
            if (!empty($row["modelid"])) {
                $row2 = $conn->get('model','*',['id'=>$row["modelid"]]);
            } else {
                $row2 = $conn->get('model','*',['ORDER'=>'sequenceid']);
            }
            echo "<tr>
                <td rowspan='2' class='center'>" . $num . "</td>
                <td class='center'>" . $row["realtime"] . "</td>
                <td><pre>" . htmlspecialchars($row["question"]) . "<pre></td>
            </tr>
            <tr>
                <td class='center'>" . $row2["modelname"] . "</td>
                <td><pre>" . htmlspecialchars($row["answer"]) . "<pre></td>
            </tr>";
        }
    } else {
        echo "<tr><td colspan='3' class='center'>暂无对话记录</td></tr>";
    }
    echo "</table>";

    //输出按钮
    // 假设 $total_pages 是总页数，$page 是当前页数
    $prev_page = max(1, $page - 1); // 上一页
    $next_page = min($totalPage, $page + 1); // 下一页
    $start_page = max(1, $page - 3); // 开始页码
    $end_page = min($totalPage, $page + 3); // 结束页码
    echo '<div class="pagination"><span style="border:0;">总计' . $totalCount . '条记录，每页' . $pageSize . '条，共 ' . $totalPage . ' 页</span></div>';
    echo '<div class="pagination">';

    if ($page <> 1) {
        echo '<a href="?userrndstr=' . $userrndstr . '&page=1">首页</a>';
        echo '<a href="?userrndstr=' . $userrndstr . '&page=' . $prev_page . '">上一页</a>';
    }

    for ($i = $start_page; $i <= $end_page; $i++) {
        if ($i == $page) {
            echo '<span class="current">' . $i . '</span>';
        } else {
            echo '<a href="?userrndstr=' . $userrndstr . '&page=' . $i . '">' . $i . '</a>';
        }
    }

    if ($page <> $totalPage) {
        echo '<a href="?userrndstr=' . $userrndstr . '&page=' . $next_page . '">下一页</a>';
        echo '<a href="?userrndstr=' . $userrndstr . '&page=' . $totalPage . '">末页</a>';
    }
    echo '</div>';
    ?>
</body>
</html>