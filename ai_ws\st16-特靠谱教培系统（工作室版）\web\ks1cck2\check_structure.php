<?php
define('IN_API2', true);
require '_ks1.php'; // 引入公共配置和函数
@session_start();

// 读取之前保存的结构信息
$recordedStructure = json_decode(file_get_contents('database_structure.json'), true);

// 获取当前数据库的所有表名
$currentTables = [];
$result = get_data("SHOW TABLES");
foreach ($result as $row) {
    $tmptbl = implode('', $row);
    $currentTables[] = $tmptbl;
}

// 检查表结构是否一致
$differences = [];
$diffCount = 0; // 用于限制最多显示10个表的差异

foreach ($currentTables as $table) {
    if ($diffCount >= 10) break; // 如果已经记录了10个表的差异，则停止检查

    if (!isset($recordedStructure[$table])) {
        $differences[] = "表 $table 未在记录中找到。";
        $diffCount++;
        continue;
    }

    $currentStructure = [];
    $result = get_data("DESCRIBE $table");
    foreach ($result as $row) {
        $currentStructure[] = [
            'Field' => $row['Field'],
            'Type' => $row['Type'],
            'Null' => $row['Null'],
            'Key' => $row['Key'],
            'Default' => $row['Default'],
            'Extra' => $row['Extra']
        ];
    }

    if ($currentStructure !== $recordedStructure[$table]) {
        $diffDetails = [];
        foreach ($currentStructure as $fieldInfo) {
            $field = $fieldInfo['Field'];
            if (!isset($recordedStructure[$table][$field])) {
                $diffDetails[] = "字段 $field 仅存在于数据库中";
            } else {
                $recordedFieldInfo = $recordedStructure[$table][$field];
                foreach ($fieldInfo as $key => $value) {
                    if ($recordedFieldInfo[$key] !== $value) {
                        $diffDetails[] = "字段 $field 的 $key 不同：数据库值为 $value，JSON值为 {$recordedFieldInfo[$key]}";
                    }
                }
            }
        }
        foreach ($recordedStructure[$table] as $field => $recordedFieldInfo) {
            if (!isset($currentStructure[$field])) {
                $diffDetails[] = "字段 $field 仅存在于 JSON 文件中";
            }
        }
        $differences[] = "表 $table 的结构与记录不一致。具体差异如下：<br>" . implode('<br>', $diffDetails);
        $diffCount++;
    }
}

if (empty($differences)) {
    echo "数据库结构与记录一致。";
} else {
    echo "发现以下差异：<br>";
    foreach ($differences as $diff) {
        echo "- $diff<br>";
    }
}
?>