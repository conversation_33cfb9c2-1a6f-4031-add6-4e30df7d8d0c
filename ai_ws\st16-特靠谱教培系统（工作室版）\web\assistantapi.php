<?php
error_reporting(E_ALL ^ E_WARNING);
header("Access-Control-Allow-Origin: *");
header("Content-Type: text/event-stream");
header("X-Accel-Buffering: no");
set_time_limit(0);
$data = file_get_contents("php://input");
$complete = json_decode($data);
if (isset($complete->model)) {
    $model = $complete->model;
    $msg = end($complete->messages)->content;
} else {
    exit;
}

function savelog($msg, $filename)
{
    error_log(date('Y-m-d H:i:s') . " {$msg}\n", 3, $filename);
}

require_once('admin/mysqlconn.php');
$row = $conn->get('assistant', '*', ['assistantid' => $model]);
if (empty($row)) {
    echo 'data: {"error":{"code":"invalid_model","message":"模型参数值错误"}}' . "\n\n";
    exit;
}


$headers  = [
    'Content-Type: application/json',
    'OpenAI-Beta: assistants=v1',
    'Authorization: Bearer ' . $row["apikey"]
];
$postdata = array(
    "assistant_id" => $model,
    "thread" => [
        "messages" => [
            [
                "role" => "user",
                "content" => $msg
            ]
        ]
    ]
);
$postdata = json_encode($postdata);
savelog("SEND:" . $postdata, "tt.log");

$ch = curl_init();
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
curl_setopt($ch, CURLOPT_URL, $row["apiaddress"] . "/v1/threads/runs");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 300);
curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_AUTOREFERER, true);
$responsedata = curl_exec($ch);
curl_close($ch);

savelog($responsedata, "tt.log");

if (empty($responsedata)) {
    echo 'data: {"error":{"code":"empty_response","message":"OpenAI接口返回值为空"}}' . "\n\n";
    exit;
}

$complete = json_decode($responsedata);
if (isset($complete->error)) {
    $errcode = "";
    $errmsg = "";
    if (isset($complete->error->code)) {
        $errcode = $complete->error->code;
    }
    if (isset($complete->error->message)) {
        $errmsg = $complete->error->message;
    }
    echo 'data: {"error":{"code":"' . $errcode . '","message":"' . $errmsg . '"}}' . "\n\n";
    exit;
} else if (isset($complete->thread_id)) {
    $run_id = $complete->id;
    $thread_id = $complete->thread_id;
    $count = 0;
    $notready = 1;
    while (($count < 30) && ($notready)) {

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_URL, $row["apiaddress"] . "/v1/threads/" . $thread_id . "/messages");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 300);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_AUTOREFERER, true);
        $responsedata = curl_exec($ch);
        curl_close($ch);
        savelog($responsedata, "tt.log");

        $complete = json_decode($responsedata);
        if (isset($complete->data)) {
            $data = $complete->data;
            if ($data[0]->run_id == $run_id) {
                if (count($data[0]->content)) {
                    $notready = 0;
                    $senddata = '{"id":"' . $data[0]->id . '","object":"chat.completion.chunk","created":' . time() . ',"choices":[{"index":0,"delta":{"content":""},"finish_reason":null}]}';
                    $senddata = json_decode($senddata);
                    $senddata->choices[0]->delta->content = $data[0]->content[0]->text->value;
                    echo 'data: ' . json_encode($senddata) . "\n\n";
                    echo 'data: {"id":"' . $data[0]->id . '","object":"chat.completion.chunk","created":' . time() . ',"choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}' . "\n\n" . 'data: [DONE]' . "\n\n";
                    flush();
                    exit;
                }
            }
        }
        $count++;
        sleep(1);
    }
} else {
    echo $responsedata;
    exit;
}
