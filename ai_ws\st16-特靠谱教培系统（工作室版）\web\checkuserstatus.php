<?php

use Medoo\Medoo;

if (isset($_GET["userrndstr"])) {
    require_once('admin/mysqlconn.php');
    $row = $conn->get('main', '*', ['id' => 1]);
    $freetryeveryday = $row["freetryeveryday"];
    $freetryshare = $row["freetryshare"];
    $row = $conn->get("user", "*", ["rndstr" => $_GET["userrndstr"], "isforbidden[!]" => true]);
    if (empty($row)) {
        echo '{"success":false,"uid":"","openid":""}';
    } else {
        echo '{"success":true,"uid":"' . ($row["userid"]) . '","openid":"' . $row["openid"] . '" , "email":"' . $row["email"] . '"}';
        if ($freetryeveryday > 0) {
            $result = $conn->select("rechargelog", "*", ["userid" => $row["userid"], "memo" => "每日登录", "rechargetime[=]" => "CURDATE()"]);
            if (!empty($result)) {
                $conn->insert('rechargelog', ['userid' => $row["userid"], 'quota' => $freetryeveryday, 'extenddays' => 0, 'rechargetime' => date('Y-m-d H:i:s'), 'memo' => '每日登录']);
                if (strtotime($row["expiretime"]) < strtotime(date("Y-m-d H:i:s"))) {
                    $conn->update("user", ["quota[+]" => $freetryeveryday, "expiretime" => Medoo::raw("DATE_ADD(CURDATE(), INTERVAL 1 DAY)")], ["userid" => $row["userid"]]);
                } else {
                    $conn->update("user", ["quota[+]" => $freetryeveryday], ["userid" => $row["userid"]]);
                }
            }
        }
        if (($freetryshare > 0) && isset($_COOKIE['sharefromuserid']) && !empty($_COOKIE['sharefromuserid']) && ($_COOKIE['sharefromuserid'] != $row["userid"])) {
            $result = $conn->select("rechargelog", "*", ["AND" => ["userid" => $row["userid"], "memo" => "分享注册"]]);
            if (empty($result)) {
                $result = $conn->select("user", "*", ["AND" => ["userid" => $row["userid"], "registertime[>=]" => date("Y-m-d 00:00:00"), "registertime[<=]" => date("Y-m-d 23:59:59")]]); //判断是否是当天新注册用户
                if (!empty($result)) {
                    $conn->insert('rechargelog', ['userid' => $row["userid"], 'quota' => $freetryshare, 'extenddays' => 0, 'rechargetime' => date('Y-m-d H:i:s'), 'memo' => '分享注册']); //赠送被邀请的用户
                    $conn->update('user', ['quota[+]' => $freetryshare, 'sharefromuserid' => $_COOKIE['sharefromuserid']], ['userid' => $row["userid"]]);

                    $conn->insert('rechargelog', ['userid' => $_COOKIE['sharefromuserid'], 'quota' => $freetryshare, 'extenddays' => 0, 'rechargetime' => date('Y-m-d H:i:s'), 'memo' => '分享给' . $row["userid"]]); //赠送发邀请的用户
                    $conn->update('user', ['quota[+]' => $freetryshare], ['userid' => $_COOKIE['sharefromuserid']]);
                }
            }
        }
    }
}
