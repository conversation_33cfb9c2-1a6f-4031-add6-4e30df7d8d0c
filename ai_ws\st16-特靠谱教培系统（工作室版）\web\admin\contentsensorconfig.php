<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
if ((isset($_POST["action"])) && ($_POST["action"] == "set")) {
    $conn->update('main',['baiduapikey'=>$_POST["baiduapikey"],'baidusecretkey'=>$_POST["baidusecretkey"],'isquestionsensor'=>$_POST["isquestionsensor"],'isanswersensor'=>$_POST["isanswersensor"]],['id'=>1]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');</script>";
    exit;
}

$row = $conn->get('main','*',['id'=>1]);
$baiduapikey = $row["baiduapikey"];
$baidusecretkey = $row["baidusecretkey"];
$isquestionsensor = $row["isquestionsensor"];
$isanswersensor = $row["isanswersensor"];

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>内容审核配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />


    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>
    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">内容审核配置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px 0;">
            <div class="page-content-area">
                <div class="row">
                    <div class="space-6"></div>
                    <div class="col-xs-12">
                        <form class="form-horizontal" method=post target="temp" style="padding:20px 40px;line-height:30px;margin:40px;">
                            <input type=hidden name=action value=set>
                            <div>百度内容审核功能开启后，违规问题无法提问，违规答案打字效果输出完成后会变成内容违规提示。</div>
                            <div style="margin-bottom:20px;">百度内容审核平台注册指南详见论坛文章，请 <a href="https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=60" target="_blank">点击这里</a> 查看</div>
                            <div class="form-group">
                                <label class="col-lg-4 control-label">百度API Key：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="<?php echo $baiduapikey; ?>" style="text-align:left;" id="baiduapikey" name="baiduapikey" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">百度Secret Key：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="<?php echo $baidusecretkey; ?>" style="text-align:left;" id="baidusecretkey" name="baidusecretkey" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">开启用户提问内容审核</label>

                                <div class="col-lg-4">
                                    <select name=isquestionsensor>
                                        <option value=1 <?php echo ($isquestionsensor) ? "selected" : "" ?>>是</option>
                                        <option value=0 <?php echo ($isquestionsensor) ? "" : "selected" ?>>否</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">开启AI回复内容审核</label>

                                <div class="col-lg-4">
                                    <select name=isanswersensor>
                                        <option value=1 <?php echo ($isanswersensor) ? "selected" : "" ?>>是</option>
                                        <option value=0 <?php echo ($isanswersensor) ? "" : "selected" ?>>否</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group" align="center" style="margin-right:150px;margin-top:15px">
                                <div class="col-lg-6 col-lg-offset-3">
                                    <button type="submit" class="btn btn-primary">确认配置</submit>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <iframe style="display:none;" id=result name=temp></iframe>
</body>

</html>