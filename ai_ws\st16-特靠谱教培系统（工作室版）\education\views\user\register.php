<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-user-plus"></i> 用户注册</h4>
            </div>
            <div class="card-body">
                <form id="registerForm" method="post" action="/register">
                    <div class="mb-3">
                        <label for="name" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">确认密码</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="verifycode" class="form-label">验证码</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="verifycode" name="verifycode" required>
                            <span class="input-group-text p-0">
                                <img src="/verifycode" alt="验证码" id="verifycodeImg" style="height: 38px; cursor: pointer;" onclick="refreshVerifyCode()">
                            </span>
                        </div>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">注册</button>
                    </div>
                </form>
                
                <div class="mt-3 text-center">
                    <p>已有账号？<a href="/login">立即登录</a></p>
                </div>
                
                <div class="mt-4">
                    <div class="text-center mb-3">或使用以下方式登录</div>
                    <div class="d-grid gap-2">
                        <a href="/wxlogin" class="btn btn-success">
                            <i class="fab fa-weixin"></i> 微信一键登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshVerifyCode() {
    document.getElementById('verifycodeImg').src = '/verifycode?' + Math.random();
}

document.getElementById('registerForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (password !== confirmPassword) {
        alert('两次输入的密码不一致');
        return;
    }
    
    const formData = new FormData(this);
    
    fetch('/register', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            window.location.href = data.redirect || '/login';
        } else {
            alert(data.message);
            refreshVerifyCode();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('注册失败，请稍后再试');
        refreshVerifyCode();
    });
});
</script>