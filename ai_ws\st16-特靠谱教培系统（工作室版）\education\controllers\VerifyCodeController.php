<?php
/**
 * 验证码控制器
 */
class VerifyCodeController extends Controller {
    /**
     * 生成验证码图像
     * 
     * @return void
     */
    public function index() {
        // 启动会话（如果尚未启动）
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        // 创建验证码实例
        $verifyCode = new VerifyCode([
            'width' => 120,
            'height' => 40,
            'codeLength' => 4,
            'fontSize' => 20
        ]);
        
        // 生成并输出验证码图像
        $verifyCode->generate();
        exit; // 直接输出图像，不需要渲染视图
    }
    
    /**
     * 验证用户输入的验证码
     * 
     * @return bool 验证结果
     */
    public function verify() {
        // 获取用户输入的验证码
        $userInput = $this->request->getBody('code');
        
        // 验证
        $result = VerifyCode::verify($userInput);
        
        // 验证后清除验证码（防止重复使用）
        VerifyCode::clear();
        
        return $result;
    }
}