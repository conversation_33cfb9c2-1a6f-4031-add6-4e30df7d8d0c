<?php
error_reporting(0);
ini_set('display_errors', 0);
require_once dirname(__FILE__) . '/PHPExcel.php';
$objPHPExcel = new PHPExcel();

$objPHPExcel->getProperties()->setCreator("ChatGPT")
    ->setTitle("recharge")
    ->setSubject("recharge");


$objPHPExcel->setActiveSheetIndex(0);

require_once('mysqlconn.php');
$result = $conn->select('card','*',['batchid'=>$_REQUEST["batchid"],'ORDER'=>'id']);
$arrayData = array(
    array('批次', '卡类型', '价格', '额度', '天数', '卡号', '密码', '创建时间', '失效', '已充', '已充用户', '充值时间', '备注')
);
foreach($result as $row){
    $row2 = $conn->get('cardtype','*',['id'=>$row["cardtype"]]);
    $temparray = array($row["batchid"], $row2["cardname"], $row2["price"], $row2["quota"], $row2["extenddays"], $row["cardid"], $row["cardpass"], $row["createtime"], ($row["isinvalid"]) ? "是" : "否", ($row["isused"]) ? "是" : "否", $row["binduser"], $row["bindtime"], $row["memo"]);

    array_push($arrayData, $temparray);
}
$objPHPExcel->getActiveSheet()->fromArray($arrayData);
$objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(6);
$objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(12);
$objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(6);
$objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(6);
$objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(6);
$objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(10);
$objPHPExcel->getActiveSheet()->getColumnDimension('G')->setWidth(36);
$objPHPExcel->getActiveSheet()->getColumnDimension('H')->setWidth(22);
$objPHPExcel->getActiveSheet()->getColumnDimension('I')->setWidth(6);
$objPHPExcel->getActiveSheet()->getColumnDimension('J')->setWidth(6);
$objPHPExcel->getActiveSheet()->getColumnDimension('K')->setWidth(10);
$objPHPExcel->getActiveSheet()->getColumnDimension('L')->setWidth(22);
$objPHPExcel->getActiveSheet()->getColumnDimension('M')->setWidth(30);
$objPHPExcel->getActiveSheet()->getStyle('A1:M1')->getFont()->setBold(true);



header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="第' . $_REQUEST["batchid"] . '批充值卡_' . date("YmdHis") . '.xls"');
header('Cache-Control: max-age=0');
header('Cache-Control: max-age=1');

header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
header('Pragma: public'); // HTTP/1.0

$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
$objWriter->save('php://output');
exit;
