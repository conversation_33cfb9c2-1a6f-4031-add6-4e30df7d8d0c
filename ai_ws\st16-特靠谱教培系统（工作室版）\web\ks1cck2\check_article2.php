<?php
define('IN_API2', true);
require '_ks1.php';
if (!isset($_SESSION[SESSION_KEY]['user']['id']) || !in_array($_SESSION[SESSION_KEY]['user']['role'], ['user', 'admin'])) {
    //header('Location: login.php');
    echo 'need login!';
    exit;
}


$user_id = $_SESSION[SESSION_KEY]['user']['id'];
$role = $_SESSION[SESSION_KEY]['user']['role']??null;
$username = $_SESSION[SESSION_KEY]['user']['username']??null;

$pub_name=get_public_account_name($user_id);

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $article_id = $_POST['article_id'];
    $read_count = $_POST['read_count'];
    $remark = $_POST['remark'] ?? ''; // 接收新增的remark参数

    echo "你因故采用了【不记时】模式".PHP_EOL;

    if (record_check2($user_id, $article_id, $read_count,0,2,$remark)) {
        echo "Check recorded successfully.";
    } else {
        echo "Failed to record check.";
    }
}

