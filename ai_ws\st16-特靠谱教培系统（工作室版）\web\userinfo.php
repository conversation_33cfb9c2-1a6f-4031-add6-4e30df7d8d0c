<?php

require_once('admin/mysqlconn.php');
if ((isset($_POST["action"])) && ($_POST["action"] == "update")) {
    session_start();
    $email = $_POST['email'];
    $password = $_POST['password'];
    $userrndstr = $_POST['userrndstr'];
    if (empty($email)) {
        echo '{"success":false,"message":"请输入电子邮箱"}';
    } else {
        $row2 = $conn->get('user','*',['rndstr'=>$userrndstr]);
        if (empty($row2)) {
            echo '{"success":false,"message":"用户登录状态异常，请刷新网页再试"}';
        } else {
            $openid = $row2["openid"];
            $appletopenid = $row2["appletopenid"];
            if ((!empty($row2["email"])) || (!empty($openid))) {
                if ($conn->has("user",["AND" => ["email" => $email,"rndstr[!]" => $userrndstr]])) {
                    echo '{"success":false,"message":"您输入的邮箱已被其他人绑定"}';
                    exit(0);
                }
            } else {
                $row = $conn->get("user","*",["AND"=> ["email"=> $email,"password"=> md5(md5($password)."chatgpt@2023"),"rndstr[!]"=> $userrndstr]]);
                if (!empty($row)) {
                    $conn->delete('user',['rndstr'=>$userrndstr]);
                    $conn->update('user',['rndstr'=>$userrndstr,'appletopenid'=>$appletopenid,'ismobile'=>0,'loginip'=>$row['loginip'] . ";" . $_SERVER["REMOTE_ADDR"],'logintime'=>$row['logintime'] . ";" . date('Y-m-d H:i:s')],['id'=>$row['id']]);
                    echo '{"success":true,"message":"已和原账户建立关联"}';
                    exit(0);
                }
                if ($conn->has("user","*",["AND"=> ["email"=> $email,"password[!]"=> md5(md5($password)."chatgpt@2023"),"rndstr[!]"=> $userrndstr]])) {
                    echo '{"success":false,"message":"邮箱存在，密码错误，请重试"}';
                    exit(0);
                }
            }
            $conn->update('user',['email'=>$email,'password'=>md5(md5($password) . "chatgpt@2023")],['rndstr'=>$userrndstr]);
            echo '{"success":true}';
        }
    }
    exit(0);
}

$adminweixinid = $conn->get('main','adminweixinid',['id'=>1]);
$row = $conn->get('user','*',['rndstr'=>$_GET["userrndstr"]]);
if (empty($row)) {
    exit(0);
}
$uid = $row["userid"];
$email = $row["email"];
?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>用户登录</title>
    <style>
        body {
            color: rgba(0, 0, 0, .85);
        }

        .login-box,
        .register-box {
            width: 280px;
        }

        .login-page,
        .register-page {
            -ms-flex-align: center;
            align-items: center;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-direction: column;
            flex-direction: column;
            height: 100vh;
            -ms-flex-pack: center;
            justify-content: center;
        }

        .card {
            margin-bottom: 1rem;
            position: relative;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-direction: column;
            flex-direction: column;
            min-width: 0;
            word-wrap: break-word;
            background-color: #fff;
            background-clip: border-box;
            border: 0 solid rgba(0, 0, 0, .125);
            border-radius: .25rem;
        }

        .login-card-body,
        .register-card-body {
            background-color: #fff;
            border-top: 0;
            color: #666;
            padding: 20px;
        }

        .card-body {
            -ms-flex: 1 1 auto;
            flex: 1 1 auto;
            min-height: 1px;
        }

        .login-box-msg,
        .register-box-msg {
            margin: 0;
            padding: 0 10px 10px;
            text-align: center;
        }

        .input-group {
            position: relative;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-wrap: wrap;
            flex-wrap: wrap;
            -ms-flex-align: stretch;
            align-items: stretch;
            width: 100%;
        }

        .mb-3,
        .my-3 {
            margin-bottom: 1rem !important;
        }

        .input-group:not(.has-validation)>.custom-file:not(:last-child) .custom-file-label,
        .input-group:not(.has-validation)>.custom-file:not(:last-child) .custom-file-label::after,
        .input-group:not(.has-validation)>.custom-select:not(:last-child),
        .input-group:not(.has-validation)>.form-control:not(:last-child) {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .login-card-body .input-group .form-control,
        .register-card-body .input-group .form-control {
            border-right: 0;
        }

        .input-group>.custom-file,
        .input-group>.custom-select,
        .input-group>.form-control,
        .input-group>.form-control-plaintext {
            position: relative;
            -ms-flex: 1 1 auto;
            flex: 1 1 auto;
            width: 1%;
            min-width: 0;
            margin-bottom: 0;
        }

        .form-control {
            display: block;
            width: 100%;
            height: 24px;
            padding: .375rem .75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: .25rem;
            box-shadow: inset 0 0 0 transparent;
            transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        }

        .input-group-append {
            margin-left: -1px;
            display: -ms-flexbox;
            display: flex;
        }

        .login-card-body .input-group .input-group-text,
        .register-card-body .input-group .input-group-text {
            background-color: transparent;
            border-bottom-right-radius: .25rem;
            border-left: 0;
            border-top-right-radius: .25rem;
            color: #777;
            transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        }

        .input-group>.input-group-append>.btn,
        .input-group>.input-group-append>.input-group-text,
        .input-group>.input-group-prepend:first-child>.btn:not(:first-child),
        .input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child),
        .input-group>.input-group-prepend:not(:first-child)>.btn,
        .input-group>.input-group-prepend:not(:first-child)>.input-group-text {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .input-group-text {
            display: -ms-flexbox;
            display: flex;
            -ms-flex-align: center;
            align-items: center;
            padding: .375rem .75rem;
            margin-bottom: 0;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            text-align: center;
            white-space: nowrap;
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: .25rem;
        }

        .justify-content-center {
            -ms-flex-pack: center !important;
            justify-content: center !important;
        }

        .row {
            display: -ms-flexbox;
            display: flex;
            -ms-flex-wrap: wrap;
            flex-wrap: wrap;
            margin-right: -7.5px;
            margin-left: -7.5px;
        }

        a {
            color: #007bff;
            text-decoration: none;
            background-color: transparent;
            cursor: pointer;
        }

        .text-center {
            text-align: center !important;
        }

        .btn:not(:disabled):not(.disabled) {
            cursor: pointer;
        }

        .btn-block {
            display: block;
            width: 100%;
        }

        .btn {
            display: inline-block;
            font-weight: 400;
            color: #212529;
            text-align: center;
            vertical-align: middle;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-color: transparent;
            border: 1px solid transparent;
            padding: .375rem .75rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: .25rem;
            transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        }

        .btn-primary {
            color: #fff;
            background-color: #007bff;
            border-color: #007bff;
            box-shadow: none;
            padding: 3px 20px;
        }
    </style>
    <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
</head>

<body class="login-page">
    <div class="login-box">
        <div class="card">
            <div class="card-body login-card-body">
                <span style="font-size:16px;font-weight:bold;line-height:40px;padding-bottom:20px;">用户ID：<?php echo $uid; ?></span>
                <form>
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" id="email" value="<?php echo $email; ?>" placeholder="电子邮箱" autocomplete="on">
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-envelope"></span>
                            </div>
                        </div>
                    </div>
                    <div class="input-group mb-3">
                        <input type="password" class="form-control" id="password" placeholder="密码" autocomplete="off">
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-lock"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row justify-content-center">
                        <div class="col-4">
                            <button type="submit" class="btn btn-primary btn-block">更新</button>
                        </div>
                    </div>
                </form>
                <div style='border-top:1px solid silver;font-size:14px;margin-top:8px;padding-top:8px;'>
                    请留下您的邮箱以防失联，APP登陆用户通过邮箱和密码关联原账号
                    <?php
                    if (!empty($adminweixinid)) {
                        echo '<p>站长微信：' . $adminweixinid . '</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    <script src="js/jquery.min.js"></script>
    <script src="js/md5.js"></script>
    <script>
        $('form').submit(function(event) {
            event.preventDefault(); // 阻止表单默认提交行为 
            if (/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test($("#email").val()) === false) {
                parent.layer.msg("请填写正确的电子邮箱地址后再提交");
                return;
            }
            $.post("userinfo.php", {
                userrndstr: '<?php echo $_GET["userrndstr"] ?>',
                action: 'update',
                email: $("#email").val(),
                password: md5(md5($("#password").val()) + 'Libra'),
            }, function(response) {
                if (response.success) {
                    if (response.message) {
                        parent.layer.msg(response.message);
                        setTimeout("parent.location.reload();", 1000);
                    } else {
                        parent.layer.msg('您的信息已更新');
                        parent.layer.close(parent.layeruserinfo);
                    }
                } else {
                    parent.layer.msg(response.message);
                }
            }, "json");
        });
    </script>
</body>

</html>