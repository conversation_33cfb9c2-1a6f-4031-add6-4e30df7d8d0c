<?php
/**
 * 路由配置文件
 */

// 获取路由器实例
$router = \Core\Application::getInstance()->getRouter();

// 首页路由
$router->addRoute('GET', '/', ['HomeController', 'index']);
$router->addRoute('GET', '/home', ['HomeController', 'index']);

// 用户路由
$router->addRoute('GET', '/login', ['UserController', 'login']);
$router->addRoute('POST', '/login', ['UserController', 'doLogin']);
$router->addRoute('GET', '/logout', ['UserController', 'logout']);
$router->addRoute('GET', '/register', ['UserController', 'register']);
$router->addRoute('POST', '/register', ['UserController', 'doRegister']);
$router->addRoute('GET', '/wxlogin', ['UserController', 'wxLogin']);

// 教师路由
$router->addRoute('GET', '/teacher', ['TeacherController', 'index']);
$router->addRoute('GET', '/teacher/list', ['TeacherController', 'list']);
$router->addRoute('GET', '/teacher/add', ['TeacherController', 'add']);
$router->addRoute('POST', '/teacher/add', ['TeacherController', 'doAdd']);
$router->addRoute('GET', '/teacher/edit/{id}', ['TeacherController', 'edit']);
$router->addRoute('POST', '/teacher/edit/{id}', ['TeacherController', 'doEdit']);
$router->addRoute('GET', '/teacher/delete/{id}', ['TeacherController', 'delete']);

// 学生路由
$router->addRoute('GET', '/student', ['StudentController', 'index']);
$router->addRoute('GET', '/student/list', ['StudentController', 'list']);
$router->addRoute('GET', '/student/add', ['StudentController', 'add']);
$router->addRoute('POST', '/student/add', ['StudentController', 'doAdd']);
$router->addRoute('GET', '/student/edit/{id}', ['StudentController', 'edit']);
$router->addRoute('POST', '/student/edit/{id}', ['StudentController', 'doEdit']);
$router->addRoute('GET', '/student/delete/{id}', ['StudentController', 'delete']);

// 课程路由
$router->addRoute('GET', '/course', ['CourseController', 'index']);
$router->addRoute('GET', '/course/list', ['CourseController', 'list']);
$router->addRoute('GET', '/course/add', ['CourseController', 'add']);
$router->addRoute('POST', '/course/add', ['CourseController', 'doAdd']);
$router->addRoute('GET', '/course/edit/{id}', ['CourseController', 'edit']);
$router->addRoute('POST', '/course/edit/{id}', ['CourseController', 'doEdit']);
$router->addRoute('GET', '/course/delete/{id}', ['CourseController', 'delete']);

// 课表路由
$router->addRoute('GET', '/schedule', ['ScheduleController', 'index']);
$router->addRoute('GET', '/schedule/week', ['ScheduleController', 'week']);
$router->addRoute('GET', '/schedule/day', ['ScheduleController', 'day']);
$router->addRoute('GET', '/schedule/add', ['ScheduleController', 'add']);
$router->addRoute('POST', '/schedule/add', ['ScheduleController', 'doAdd']);
$router->addRoute('GET', '/schedule/edit/{id}', ['ScheduleController', 'edit']);
$router->addRoute('POST', '/schedule/edit/{id}', ['ScheduleController', 'doEdit']);
$router->addRoute('GET', '/schedule/delete/{id}', ['ScheduleController', 'delete']);

// 教室路由
$router->addRoute('GET', '/classroom', ['ClassroomController', 'index']);
$router->addRoute('GET', '/classroom/list', ['ClassroomController', 'list']);
$router->addRoute('GET', '/classroom/add', ['ClassroomController', 'add']);
$router->addRoute('POST', '/classroom/add', ['ClassroomController', 'doAdd']);
$router->addRoute('GET', '/classroom/edit/{id}', ['ClassroomController', 'edit']);
$router->addRoute('POST', '/classroom/edit/{id}', ['ClassroomController', 'doEdit']);
$router->addRoute('GET', '/classroom/delete/{id}', ['ClassroomController', 'delete']);

// 请假路由
$router->addRoute('GET', '/leave', ['LeaveController', 'index']);
$router->addRoute('GET', '/leave/list', ['LeaveController', 'list']);
$router->addRoute('GET', '/leave/add', ['LeaveController', 'add']);
$router->addRoute('POST', '/leave/add', ['LeaveController', 'doAdd']);
$router->addRoute('GET', '/leave/view/{id}', ['LeaveController', 'view']);
$router->addRoute('GET', '/leave/approve/{id}', ['LeaveController', 'approve']);
$router->addRoute('POST', '/leave/approve/{id}', ['LeaveController', 'doApprove']);
$router->addRoute('GET', '/leave/reject/{id}', ['LeaveController', 'reject']);
$router->addRoute('POST', '/leave/reject/{id}', ['LeaveController', 'doReject']);

// API路由
$router->addRoute('GET', '/api/schedule', ['ApiController', 'schedule']);
$router->addRoute('GET', '/api/leave', ['ApiController', 'leave']);