<?php

try {
    // 可能会抛出异常的代码块
    $conn = new mysqli($config['host'], $config['user'], $config['pass'], $config['dbname']);
    // 检查连接
	if ($conn->connect_error) {
	    //die("连接失败: " . $conn->connect_error);
	    show503(1);
	}
	$conn->query("set names 'utf8'");
} catch (Exception $e) {
    // 捕获异常并输出错误信息到日志文件中
    error_log('Caught exception: ' . $e->getMessage());
    show503(2);
}

if(!$conn){
	show503(3);
}

function show503($mode=0){
  //header('HTTP/1.1 503 Service Unavailable');
  //header('Retry-After: 36000');
echo '<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>维护中</title>
</head>
<body>
  <h1>系统正在维护中，请稍后再试</h1>
  <p>对不起，系统正在维护中，请稍后再试。 #'.$mode.'</p>
</body>
</html>';

die("系统正在维护中，请稍后再试。");
}