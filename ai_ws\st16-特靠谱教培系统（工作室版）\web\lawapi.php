<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: text/event-stream");
header("X-Accel-Buffering: no");
set_time_limit(0);
$role = $_REQUEST['role'];
require_once('admin/mysqlconn.php');

$tongyinowtext = "";
$proxyaddress = $conn->get('main','proxyaddress',['id'=>1]);
$isstart = true;
$firsterror = true;
$row = $conn->get('user','*',['rndstr'=>$_GET["user"]]);
if (empty($row)) {
    echo 'data: {"error":{"code":"invalid_user","message":""}}' . "\n\n";
    exit;
}
if (empty($row["lastquestion"])) {
    echo 'data: {"error":{"code":"repeated_query","message":""}}' . "\n\n";
    exit;
}
$userid = $row["id"];
$quota = $row["quota"];
$conversationid = explode(",", $row["lastquestion"])[0];
$postdata = substr($row["lastquestion"], strlen($conversationid) + 1);
$lastmodelid = $row["lastmodelid"];
$row = $conn->get('model','*',['id'=>$lastmodelid]);
$modelprice = $row["modelprice"];
$modelvalue = $row["modelvalue"];
$modeltype = $row["modeltype"];

if ($quota < $modelprice) {
    echo 'data: {"error":{"code":"out_of_money","message":""}}' . "\n\n";
    exit;
}
$responsedata = "";
$OPENAI_API_KEY = "";

$row = $conn->get("apikey","*",["AND"=>["isvalid"=>true,"keytype"=>$lastmodelid],"ORDER"=>["lasttime","id"]]);
if (empty($row)) {
    echo 'data: {"error":{"code":"no_valid_apikey","message":""}}' . "\n\n";
    exit;
}
$OPENAI_API_KEY = $row["apikey"];
$apiaddress = $row["apiaddress"];
$apikeyid = $row["id"];
$conn->update('apikey',['lasttime'=>date('Y-m-d H:i:s')],['id'=>$apikeyid]);

$iserror = 0;

$jsonmessages = json_decode($postdata, true)["messages"];
$prompt = end($jsonmessages)["content"];
$originalprompt = $prompt;
error_log(date('Y-m-d H:i:s') . " Start: {$prompt}\n", 3, "you.log");

$url = 'http://c.ipfei.com:777/api/chatOpinion?query=' . $prompt;
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate');
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
$prompt2 = curl_exec($ch);
curl_close($ch);

$prompt = "案情描述：" . $prompt . "\n\n请根据以上案情，依据中华人民共和国法律，请协助法官提出建议的裁判结果，重点在量刑部分。量刑和判决必须结合案情，不能虚构事实，如果案情中没有可以减轻刑罚的描述不要刻意增加，请参考后面的案例考虑量刑数字。\n以下是5个相似案件的案情和裁判结果，作为相关法规和书写格式参考。\n\n" . $prompt2;

error_log(date('Y-m-d H:i:s') . " Process: {$prompt}\n", 3, "you.log");
$context = json_decode($_REQUEST['context'] ?: "[]") ?: [];
$postdata = [
    "model" => $modelvalue,
    "temperature" => 0,
    "stream" => true,
    "messages" => [],
];

if (!empty($role)) {
    $postdata['messages'][] = ['role' => 'system', 'content' => $role];
}

if (!empty($context)) {
    $context = array_slice($context, -5);
    foreach ($context as $message) {
        $postdata['messages'][] = ['role' => 'user', 'content' => urldecode($message[0])];
        $postdata['messages'][] = ['role' => 'assistant', 'content' => urldecode($message[1])];
    }
}
$postdata['messages'][] = ['role' => 'user', 'content' => $prompt];
$postdata = json_encode($postdata);


function getbaiduaccesscode()
{
    global $conn, $apiaddress, $OPENAI_API_KEY, $apikeyid;
    $apikey = explode(",", $OPENAI_API_KEY);
    $newtoken = json_decode(file_get_contents($apiaddress . "/oauth/2.0/token?grant_type=client_credentials&client_id=" . $apikey[0] . "&client_secret=" . $apikey[1]))->access_token;
    $conn->update('apikey',['apikey'=>$apikey[0].",".$apikey[1].",".$newtoken],['id'=>$apikeyid]);
    return $newtoken;
}
function jwt_encode($payload, $secret, $algorithm, $headers)
{
    $header = base64_encode(json_encode($headers));
    $payload = base64_encode(json_encode($payload));
    $signature = hash_hmac($algorithm, "$header.$payload", $secret, true);
    $signature = base64_encode($signature);
    return "$header.$payload.$signature";
}
function generate_token($apikey, $exp_seconds)
{
    try {
        list($id, $secret) = explode(".", $apikey);
    } catch (Exception $e) {
        throw new Exception("invalid apikey", $e);
    }

    $payload = array(
        "api_key" => $id,
        "exp" => round(microtime(true) * 1000) + $exp_seconds * 1000,
        "timestamp" => round(microtime(true) * 1000),
    );

    return jwt_encode(
        $payload,
        $secret,
        "sha256",
        array("alg" => "HS256", "sign_type" => "SIGN")
    );
}
if ($modeltype == "文心千帆") {
    $headers  = [
        'Accept: application/json',
        'Content-Type: application/json'
    ];

    $apikeyarray = explode(",", $OPENAI_API_KEY);
    if (count($apikeyarray) == 2) {
        $accesstoken = getbaiduaccesscode();
    } else {
        $accesstoken = $apikeyarray[2];
    }
    $postdatajson = json_decode($postdata, true);
    unset($postdatajson['temperature']);
    //下面这段代码是为了把角色去掉，国内模型都不支持
    $jsonmessages = $postdatajson["messages"];
    for ($i = 0; $i < count($jsonmessages); ++$i) {
        if ($jsonmessages[$i]["role"] !== "system") {
            $newjson[] = $jsonmessages[$i];
        }
    }
    $postdatajson["messages"] = $newjson;
    $postdata = json_encode($postdatajson);
} else if ($modeltype == "清华智谱") {
    $token = generate_token($OPENAI_API_KEY, 3600);
    $headers  = [
        'Accept: text/event-stream',
        'Content-Type: application/json',
        'Authorization: ' . $token
    ];

    $postdatajson = json_decode($postdata, true);
    unset($postdatajson['model']);
    unset($postdatajson['temperature']);
    unset($postdatajson['stream']);
    $jsonmessages = $postdatajson["messages"];
    for ($i = 0; $i < count($jsonmessages); ++$i) {
        if ($jsonmessages[$i]["role"] !== "system") {
            $newjson[] = $jsonmessages[$i];
        }
    }
    $postdatajson["messages"] = $newjson;
    $postdatajson['prompt'] = $postdatajson['messages'];
    unset($postdatajson['messages']);
    $postdata = json_encode($postdatajson);
} else if ($modeltype == "通义千问") {
    $headers  = [
        'Accept: text/event-stream',
        'Content-Type: application/json',
        'X-DashScope-SSE: enable',
        'Authorization: Bearer ' . $OPENAI_API_KEY
    ];
    $jsonmessages = json_decode($postdata, true)["messages"];
    $postdata = [
        "model" => "qwen-v1",
        "input" => ["prompt" => "", "history" => []],
        "parameters" => (object) []
    ];
    $postdata["input"]["prompt"] = end($jsonmessages)["content"];
    for ($i = 0; $i < (count($jsonmessages) - 1); ++$i) {
        if ($jsonmessages[$i]["role"] == "user") {
            $postdata["input"]["history"][] = ['user' => $jsonmessages[$i]["content"], 'bot' => $jsonmessages[$i + 1]["content"]];
            $i++;
        }
    }
    $postdata = json_encode($postdata);
} else {
    $headers  = [
        'Accept: application/json',
        'Content-Type: application/json',
        'Authorization: Bearer ' . $OPENAI_API_KEY
    ];
}
$callback = function ($ch, $data) {
    global $tongyinowtext, $modeltype, $responsedata, $iserror, $isstart, $conn, $userid, $modelprice, $errcode, $errmsg, $accesstoken, $firsterror, $prompt2;
    $complete = json_decode($data);
    if (isset($complete->error)) {
        $errcode = $complete->error->code;
        $errmsg = $complete->error->message;
        if (strpos($errmsg, "Rate limit reached") === 0) { //访问频率超限错误返回的code为空，特殊处理一下
            $errcode = "rate_limit_reached";
        }
        if (strpos($errmsg, "Your access was terminated") === 0) { //违规使用，被封禁，特殊处理一下
            $errcode = "access_terminated";
        }
        if (strpos($errmsg, "You didn't provide an API key") === 0) { //未提供API-KEY
            $errcode = "no_api_key";
        }
        if (strpos($errmsg, "You exceeded your current quota") === 0) { //API-KEY余额不足
            $errcode = "insufficient_quota";
        }
        if (strpos($errmsg, "That model is currently overloaded") === 0) { //OpenAI模型超负荷
            $errcode = "model_overloaded";
        }
        if (strpos($errmsg, "The server had an error") === 0) { //OpenAI服务器超负荷
            $errcode = "server_overloaded";
        }
        if (strpos($errmsg, "Your account is not active") === 0) { //账户被封禁
            $errcode = "account_deactivated";
        }
        $responsedata = $data;
        echo 'data: {"error":{"code":"' . $errcode . '","message":"' . $errmsg . '"}}' . "\n\n";
        $iserror = 1;
    } else if (isset($complete->error_code)) {
        $errcode = $complete->error_code;
        $errmsg = $complete->error_msg;
        if ((($errcode == 110) || ($errcode == 111)) && ($firsterror)) {
            $accesstoken = getbaiduaccesscode();
            $firsterror = false;
            go();
            return;
        } else {
            $responsedata = $data;
            echo 'data: {"error":{"code":"' . $errcode . '","message":"' . $errmsg . '"}}' . "\n\n";
            $iserror = 1;
        }
    } else if (substr($data, 0, 3) === "id:") { //处理通义千问的流式数据
        if ($isstart) {
            $conn->update("user",["quota[-]"=>$modelprice,"credits[+]"=>$modelprice,"questioncount[+]"=>1],["id"=>$userid]);
            $isstart = false;
        }
        $msg = preg_replace('/id:[\s\S]*?data:/', '', $data);
        $contentarr = json_decode(trim($msg));
        if ((isset($contentarr->output)) && (isset($contentarr->output->text))) {
            preg_match('/id:(\d+)/', $data, $matches);
            $senddata = '{"id":"' . $matches[1] . '","object":"chat.completion.chunk","created":' . time() . ',"choices":[{"index":0,"delta":{"content":""},"finish_reason":null}]}';
            $senddata = json_decode($senddata);
            $senddata->choices[0]->delta->content = str_replace($tongyinowtext, "", $contentarr->output->text);
            $tongyinowtext = $contentarr->output->text;
            $senddata = 'data: ' . json_encode($senddata) . "\n\n";
            $responsedata .= $senddata;
            echo $senddata;
        }
        if ((isset($contentarr->output)) && (isset($contentarr->output->finish_reason)) && ($contentarr->output->finish_reason === "stop")) {
            preg_match('/id:(\d+)/', $data, $matches);
            $senddata = 'data: {"id":"' . ($matches[1] + 1) . '","object":"chat.completion.chunk","created":' . time() . ',"choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}' . "\n\n" . 'data: [DONE]' . "\n\n";
            $responsedata .= $senddata;
            echo $senddata;
        }
    } else if (substr($data, 0, 6) === "event:") { //处理清华智谱的流式数据
        if ($isstart) {
            $conn->update("user",["quota[-]"=>$modelprice,"credits[+]"=>$modelprice,"questioncount[+]"=>1],["id"=>$userid]);
            $isstart = false;
        }
        preg_match_all('/data:(.*)/', $data, $matches);
        $msg = implode('', $matches[1]);
        preg_match('/id:(\d+)/', $data, $matches);
        $senddata = '{"id":"' . $matches[1] . '","object":"chat.completion.chunk","created":' . time() . ',"choices":[{"index":0,"delta":{"content":""},"finish_reason":null}]}';
        $senddata = json_decode($senddata);
        $senddata->choices[0]->delta->content = $msg;
        $senddata = 'data: ' . json_encode($senddata) . "\n\n";
        $responsedata .= $senddata;
        echo $senddata;
        if (strpos($data, "event:finish\n") === 0) {
            preg_match('/id:(\d+)/', $data, $matches);
            $senddata = 'data: {"id":"' . ($matches[1]) . '","object":"chat.completion.chunk","created":' . time() . ',"choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}' . "\n\n" . 'data: [DONE]' . "\n\n";
            $responsedata .= $senddata;
            echo $senddata;
        }
    } else if (substr($data, 0, 5) === "data:") {
        if ($isstart) {
            $conn->update("user",["quota[-]"=>$modelprice,"credits[+]"=>$modelprice,"questioncount[+]"=>1],["id"=>$userid]);
            $isstart = false;
        }
        if ($modeltype == "文心千帆") {
            foreach (explode("\n\ndata: ", "\n\n" . $data . "data: ") as $msg) {
                if (strlen($msg)) {
                    $contentarr = json_decode(trim($msg));
                    if (isset($contentarr->result)) {
                        $senddata = '{"id":"' . $contentarr->id . '","object":"chat.completion.chunk","created":' . $contentarr->created . ',"choices":[{"index":0,"delta":{"content":""},"finish_reason":null}]}';
                        $senddata = json_decode($senddata);
                        $senddata->choices[0]->delta->content = $contentarr->result;
                        $senddata = 'data: ' . json_encode($senddata) . "\n\n";
                        $responsedata .= $senddata;
                        echo $senddata;
                    }
                    if ((isset($contentarr->is_end)) && ($contentarr->is_end === true)) {
                        $senddata = 'data: {"id":"' . ($contentarr->id + 1) . '","object":"chat.completion.chunk","created":' . $contentarr->created . ',"choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}' . "\n\n" . 'data: [DONE]' . "\n\n";
                        $responsedata .= $senddata;
                        echo $senddata;
                    }
                }
            }
        } else {
            if (strpos($data, '"finish_reason":"stop"') !== false) {

                error_log(date('Y-m-d H:i:s') . " End: {$data}\n", 3, "you.log");
                $tempdata = json_decode(trim(explode("data:", $data)[1]));
                $tempdata->choices[0]->delta->content = "\n\n以下是5个相似案例：\n\n" . str_replace("\n", "\n\n", $prompt2);
                $tempdata->choices[0]->finish_reason = null;
                $tempdata = 'data: ' . json_encode($tempdata, JSON_UNESCAPED_UNICODE) . "\n\n";
                echo $tempdata;
                $responsedata .= $tempdata;
            }
            $responsedata .= $data;
            echo $data;
        }
    } else {
        if ($isstart) {
            $responsedata = $data;
            echo 'data: {"error":{"code":"unknown","message":"' . rawurlencode($data) . '"}}' . "\n\n";
            $isstart = false;
            $iserror = 1;
        } else {
            $responsedata .= $data;
            echo $data;
        }
    }
    flush();
    return strlen($data);
};
function go()
{
    global $modeltype, $modelvalue, $apiaddress, $headers, $postdata, $proxyaddress, $accesstoken, $callback;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

    if ($modeltype == "文心千帆") {
        if ($modelvalue == "ERNIE-Bot") {
            curl_setopt($ch, CURLOPT_URL, $apiaddress . '/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions?access_token=' . $accesstoken);
        } else if ($modelvalue == "ERNIE-Bot-turbo") {
            curl_setopt($ch, CURLOPT_URL, $apiaddress . '/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant?access_token=' . $accesstoken);
        }
    } else if ($modeltype == "清华智谱") {
        curl_setopt($ch, CURLOPT_URL, $apiaddress . '/api/paas/v3/model-api/chatglm_pro/sse-invoke');
    } else if ($modeltype == "通义千问") {
        curl_setopt($ch, CURLOPT_URL, $apiaddress . '/api/v1/services/aigc/text-generation/generation');
    } else {
        curl_setopt($ch, CURLOPT_URL, $apiaddress . '/v1/chat/completions');
        if (!empty($proxyaddress)) {
            curl_setopt($ch, CURLOPT_PROXY, $proxyaddress);
        }
    }
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
    curl_setopt($ch, CURLOPT_WRITEFUNCTION, $callback);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 300); // 设置连接超时时间为300秒
    curl_setopt($ch, CURLOPT_MAXREDIRS, 3); // 设置最大重定向次数为3次
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允许自动重定向
    curl_setopt($ch, CURLOPT_AUTOREFERER, true); // 自动设置Referer
    curl_exec($ch);
    curl_close($ch);
}
go();


$questionarr = json_decode($postdata, true);
if ($modeltype == "通义千问") {
    $goodquestion = addslashes($questionarr['input']['prompt']);
} else if ($modeltype == "清华智谱") {
    $goodquestion = addslashes(end($questionarr['prompt'])['content']);
} else {
    $goodquestion = addslashes($originalprompt);
}
if (!$iserror) {
    $answer = "";
    if (substr(trim($responsedata), -6) == "[DONE]") {
        $responsedata = substr(trim($responsedata), 0, -6) . "{";
    }
    $responsearr = explode("}\n\ndata: {", "}\n\n" . $responsedata);

    foreach ($responsearr as $msg) {
        $contentarr = json_decode("{" . trim($msg) . "}", true);
        if (isset($contentarr['choices'][0]['delta']['content'])) {
            $answer .= $contentarr['choices'][0]['delta']['content'];
        }
    }
    $goodanswer = addslashes(trim($answer));
    $conn->insert('chathistory',['question'=>$goodquestion,'answer'=>$goodanswer,'conversationid'=>$conversationid,'modelid'=>$lastmodelid,'realtime'=>date('Y-m-d H:i:s'),'userid'=>$userid,'iserror'=>$iserror]);
} else {
    if (($errcode == "access_terminated") || ($errcode == "insufficient_quota") || ($errcode == "invalid_api_key") || ($errcode == "account_deactivated")) {
        $conn->update('apikey',['isvalid'=>0,'lasttime'=>date('Y-m-d H:i:s'),'errmsg'=>addslashes($errcode . "|" . $errmsg)],['id'=>$apikeyid]);
    }
    $goodanswer = addslashes(trim($responsedata));
    $conn->insert('errorlog',['question'=>$goodquestion,'errmsg'=>$goodanswer,'conversationid'=>$conversationid,'modelid'=>$lastmodelid,'realtime'=>date('Y-m-d H:i:s'),'userid'=>$userid,'apikey'=>$OPENAI_API_KEY]);
}

