<?php
require '_ks1.php';
@session_start();

if (!isset($_SESSION[SESSION_KEY]['user']['id']) || $_SESSION[SESSION_KEY]['user']['role'] != 'admin') {
    header('Location: login.php');
    exit;
}

// 获取所有选题
function get_all_topics() {
    $table = get_table_name('topics');
    $sql = "SELECT t.*, tm.views, tm.favorites, tm.platforms, tm.keywords 
            FROM $table t 
            LEFT JOIN cck1_topic_meta tm ON t.topic_id = tm.topic_id 
            ORDER BY t.created_at DESC";
    return get_data($sql);
}

// 获取所有标签
function get_all_tags() {
    $table = get_table_name('tags');
    $sql = "SELECT * FROM $table ORDER BY tag_name";
    return get_data($sql);
}

// 创建选题
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_topic'])) {
    $title = $_POST['title'];
    $description = $_POST['description'];
    $keywords = $_POST['keywords'];
    $platforms = $_POST['platforms'];
    $tags = $_POST['tags'] ?? [];
    $new_tags = $_POST['new_tags'] ?? '';

    // 插入选题
    $table = get_table_name('topics');
    $sql = "INSERT INTO $table (title, description, created_by) VALUES (?s, ?s, ?i)";
    $sql = prepare($sql, array($title, $description, $_SESSION[SESSION_KEY]['user']['id']));
    run_sql($sql);

    // 获取新插入的选题ID
    $topic_id = get_last_insert_id();

    // 插入选题元数据
    $meta_table = get_table_name('topic_meta');
    $sql = "INSERT INTO $meta_table (topic_id, views, favorites, platforms, keywords) 
            VALUES (?i, 0, 0, ?s, ?s)";
    $sql = prepare($sql, array($topic_id, $platforms, $keywords));
    run_sql($sql);

    // 插入选题-标签关联
    if ($tags) {
        foreach ($tags as $tag_id) {
            $sql = "INSERT INTO cck1_topic_tags (topic_id, tag_id) VALUES (?i, ?i)";
            $sql = prepare($sql, array($topic_id, $tag_id));
            run_sql($sql);
        }
    }

    // 如果有新标签，插入新标签并关联
    if ($new_tags) {
        $new_tags = explode(',', $new_tags);
        foreach ($new_tags as $new_tag) {
            $new_tag = trim($new_tag);
            if ($new_tag) {
                $sql = "INSERT INTO cck1_tags (tag_name) VALUES (?s)";
                $sql = prepare($sql, array($new_tag));
                run_sql($sql);

                $tag_id = get_last_insert_id();
                $sql = "INSERT INTO cck1_topic_tags (topic_id, tag_id) VALUES (?i, ?i)";
                $sql = prepare($sql, array($topic_id, $tag_id));
                run_sql($sql);
            }
        }
    }

    $_SESSION['success_message'] = '选题创建成功！';
    header('Location: topic_admin.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员仪表盘</title>
    <link rel="stylesheet" href="res/ks1table.css">
</head>
<body>
    <link rel="stylesheet" href="<?php echo $css_file; ?>">
    <h1>管理员仪表盘</h1>
    <div><?php echo $nav_html; ?></div>

    <h2>创建选题</h2>
    <form action="" method="post">
        <label for="title">选题标题：</label>
        <input type="text" name="title" id="title" required><br>

        <label for="description">选题描述：</label>
        <textarea name="description" id="description"></textarea><br>

        <label for="keywords">关键词（逗号分隔）：</label>
        <input type="text" name="keywords" id="keywords"><br>

        <label for="platforms">适用平台（逗号分隔）：</label>
        <input type="text" name="platforms" id="platforms"><br>

        <label>选择标签：</label>
        <select name="tags[]" multiple>
            <?php
            $tags = get_all_tags();
            foreach ($tags as $tag) {
                echo '<option value="' . $tag['tag_id'] . '">' . htmlspecialchars($tag['tag_name']) . '</option>';
            }
            ?>
        </select><br>

        <label for="new_tags">新增标签（逗号分隔）：</label>
        <input type="text" name="new_tags" id="new_tags"><br>

        <button type="submit" name="create_topic">创建选题</button>
    </form>

    <h2>选题列表</h2>
    <table border="1">
        <tr>
            <th>选题标题</th>
            <th>描述</th>
            <th>关键词</th>
            <th>适用平台</th>
            <th>创建时间</th>
            <th>操作</th>
        </tr>
        <?php
        $topics = get_all_topics();
        if ($topics) {
            foreach ($topics as $topic) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($topic['title']) . '</td>';
                echo '<td>' . htmlspecialchars($topic['description']) . '</td>';
                echo '<td>' . htmlspecialchars($topic['keywords']) . '</td>';
                echo '<td>' . htmlspecialchars($topic['platforms']) . '</td>';
                echo '<td>' . htmlspecialchars($topic['created_at']) . '</td>';
                echo '<td>';
                echo '<a href="topic_user_materials.php?topic_id=' . $topic['topic_id'] . '">上传资料</a>';
                echo '</td>';
                echo '</tr>';
            }
        } else {
            echo '<tr><td colspan="6">暂无选题。</td></tr>';
        }
        ?>
    </table>
    <div><?php echo $nav_html; ?></div>
</body>
</html>