<?php
define('IN_API2', true);
require '_ks1.php';
@session_start();
if (!isset($_SESSION[SESSION_KEY]['user']['id']) || !in_array($_SESSION[SESSION_KEY]['user']['role'], ['user', 'admin'])) {
    header('HTTP/1.1 403 Forbidden');
    exit;
}

$link_id = isset($_GET['link_id']) ? intval($_GET['link_id']) : 0;
if ($link_id <= 0) {
    header('HTTP/1.1 400 Bad Request');
    exit;
}

$view_details = get_link_views_details($link_id, 50, 0);
echo json_encode($view_details);
?>