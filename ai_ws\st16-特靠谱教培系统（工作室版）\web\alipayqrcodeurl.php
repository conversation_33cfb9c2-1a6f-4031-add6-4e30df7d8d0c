<?php
if ((isset($_GET["s"])) && (isset($_GET["p"]))) {
    require_once('admin/mysqlconn.php');
    $row = $conn->get('main','*',['id'=>1]);
    $alipayappid = $row["alipayappid"];
    $alipayprivatekey = $row["alipayprivatekey"];
    $row = $conn->get('cardtype','*',['id'=>$_GET["p"]]);
    $cardname = $row["cardname"];
    $price = $row["price"];
    $userid = $conn->get('user','userid',['rndstr'=>$_GET["s"]]);

    $appid = $alipayappid;
    $outTradeNo = $_SERVER['HTTP_HOST'] . "_" . $userid . "_" . time();
    $notifyUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/alipaycallback.php';
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
        $notifyUrl = str_replace('http://', 'https://', $notifyUrl);
    }
    $payAmount = $price;
    $orderName = '购买' . $cardname;
    $rsaPrivateKey = $alipayprivatekey;
    $signType = 'RSA2'; //签名算法类型，支持RSA2和RSA，推荐使用RSA2
    $conn->insert('alipaylist',['userid'=>$userid,'cardtype'=>$_GET["p"],'ordertime'=>date('Y-m-d H:i:s'),'clientip'=>$_SERVER["REMOTE_ADDR"],'out_trade_no'=>$outTradeNo]);

    $aliPay = new AlipayService();
    $aliPay->setAppid($appid);
    $aliPay->setNotifyUrl($notifyUrl);
    $aliPay->setRsaPrivateKey($rsaPrivateKey);
    $aliPay->setTotalFee($payAmount);
    $aliPay->setOutTradeNo($outTradeNo);
    $aliPay->setOrderName($orderName);
    $result = $aliPay->doPay();
    $result = $result['alipay_trade_precreate_response'];
    if ($result['code'] && $result['code'] == '10000') {
        echo '{"error":false,"alipayqrcodeurl":"' . $result['qr_code'] . '"}';
    } else {
        echo '{"error":true,"errmsg":"' . $result['msg'] . ' : ' . $result['sub_msg'] . '"}';
    }

    
}
class AlipayService
{
    protected $appId;
    protected $notifyUrl;
    protected $charset;
    //私钥值
    protected $rsaPrivateKey;
    protected $totalFee;
    protected $outTradeNo;
    protected $orderName;

    public function __construct()
    {
        $this->charset = 'utf-8';
    }

    public function setAppid($appid)
    {
        $this->appId = $appid;
    }

    public function setNotifyUrl($notifyUrl)
    {
        $this->notifyUrl = $notifyUrl;
    }

    public function setRsaPrivateKey($saPrivateKey)
    {
        $this->rsaPrivateKey = $saPrivateKey;
    }

    public function setTotalFee($payAmount)
    {
        $this->totalFee = $payAmount;
    }

    public function setOutTradeNo($outTradeNo)
    {
        $this->outTradeNo = $outTradeNo;
    }

    public function setOrderName($orderName)
    {
        $this->orderName = $orderName;
    }

    /**
     * 发起订单
     * @return array
     */
    public function doPay()
    {
        //请求参数
        $requestConfigs = array(
            'out_trade_no' => $this->outTradeNo,
            'total_amount' => $this->totalFee, //单位 元
            'subject' => $this->orderName,  //订单标题
            'timeout_express' => '2h'       //该笔订单允许的最晚付款时间，逾期将关闭交易。取值范围：1m～15d。m-分钟，h-小时，d-天，1c-当天（1c-当天的情况下，无论交易何时创建，都在0点关闭）。 该参数数值不接受小数点， 如 1.5h，可转换为 90m。
        );
        $commonConfigs = array(
            //公共参数
            'app_id' => $this->appId,
            'method' => 'alipay.trade.precreate',             //接口名称
            'format' => 'JSON',
            'charset' => $this->charset,
            'sign_type' => 'RSA2',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'notify_url' => $this->notifyUrl,
            'biz_content' => json_encode($requestConfigs),
        );
        $commonConfigs["sign"] = $this->generateSign($commonConfigs, $commonConfigs['sign_type']);
        $result = $this->curlPost('https://openapi.alipay.com/gateway.do?charset=' . $this->charset, $commonConfigs);
        return json_decode($result, true);
    }
    public function generateSign($params, $signType = "RSA")
    {
        return $this->sign($this->getSignContent($params), $signType);
    }
    protected function sign($data, $signType = "RSA")
    {
        $priKey = $this->rsaPrivateKey;
        $res = "-----BEGIN RSA PRIVATE KEY-----\n" .
            wordwrap($priKey, 64, "\n", true) .
            "\n-----END RSA PRIVATE KEY-----";
        ($res) or die('您使用的私钥格式错误，请检查RSA私钥配置');
        if ("RSA2" == $signType) {
            openssl_sign($data, $sign, $res, version_compare(PHP_VERSION, '5.4.0', '<') ? SHA256 : OPENSSL_ALGO_SHA256); //OPENSSL_ALGO_SHA256是php5.4.8以上版本才支持
        } else {
            openssl_sign($data, $sign, $res);
        }
        $sign = base64_encode($sign);
        return $sign;
    }
    /**
     * 校验$value是否非空
     *  if not set ,return true;
     *    if is null , return true;
     **/
    protected function checkEmpty($value)
    {
        if (!isset($value))
            return true;
        if ($value === null)
            return true;
        if (trim($value) === "")
            return true;
        return false;
    }
    public function getSignContent($params)
    {
        ksort($params);
        $stringToBeSigned = "";
        $i = 0;
        foreach ($params as $k => $v) {
            if (false === $this->checkEmpty($v) && "@" != substr($v, 0, 1)) {
                // 转换成目标字符集
                $v = $this->characet($v, $this->charset);
                if ($i == 0) {
                    $stringToBeSigned .= "$k" . "=" . "$v";
                } else {
                    $stringToBeSigned .= "&" . "$k" . "=" . "$v";
                }
                $i++;
            }
        }
        unset($k, $v);
        return $stringToBeSigned;
    }
    /**
     * 转换字符集编码
     * @param $data
     * @param $targetCharset
     * @return string
     */
    function characet($data, $targetCharset)
    {
        if (!empty($data)) {
            $fileType = $this->charset;
            if (strcasecmp($fileType, $targetCharset) != 0) {
                $data = mb_convert_encoding($data, $targetCharset, $fileType);
                //$data = iconv($fileType, $targetCharset.'//IGNORE', $data);
            }
        }
        return $data;
    }
    public function curlPost($url = '', $postData = '', $options = array())
    {
        if (is_array($postData)) {
            $postData = http_build_query($postData);
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); //设置cURL允许执行的最长秒数
        if (!empty($options)) {
            curl_setopt_array($ch, $options);
        }
        //https请求 不验证证书和host
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }
}
