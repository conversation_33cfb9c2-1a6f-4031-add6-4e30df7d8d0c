html {
    line-height:1.15;
    -webkit-text-size-adjust:100%;
}
body {
    margin:0;
    height:100%;
}
main {
    display:block;
}
h1 {
    font-size:2em;
    margin:0.67em 0;
}
hr {
    box-sizing:content-box;
    height:0;
    overflow:visible;
}
pre {
    font-family:monospace, monospace;
    font-size:1em;
}
img {
    height:auto;
    width:auto;
    max-width:500px;
}
a {
    background-color:transparent;
}
abbr[title] {
    border-bottom:none;
    text-decoration:underline;
    text-decoration:underline dotted;
}
b, strong {
    font-weight:bolder;
}
code, kbd, samp {
    font-family:monospace, monospace;
    font-size:1em;
    line-height:1.5em;
}
small {
    font-size:80%;
}
sub, sup {
    font-size:75%;
    line-height:0;
    position:relative;
    vertical-align:baseline;
}
sub {
    bottom:-0.25em;
}
sup {
    top:-0.5em;
}
ul, li {
    list-style:none;
}
img {
    border-style:none;
}
button, input, optgroup, select, textarea {
    font-family:inherit;
    font-size:100%;
    line-height:1.15;
    margin:0;
}
button, input {
    overflow:visible;
}
button, select {
    text-transform:none;
}
button, [type="button"], [type="reset"], [type="submit"] {
    -webkit-appearance:button;
}
button::-moz-focus-inner, [type="button"]::-moz-focus-inner, [type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner {
    border-style:none;
    padding:0;
}
button:-moz-focusring, [type="button"]:-moz-focusring, [type="reset"]:-moz-focusring, [type="submit"]:-moz-focusring {
    outline:1px dotted ButtonText;
}
fieldset {
    padding:0.35em 0.75em 0.625em;
}
legend {
    box-sizing:border-box;
    color:inherit;
    display:table;
    max-width:100%;
    padding:0;
    white-space:normal;
}
progress {
    vertical-align:baseline;
}
textarea {
    overflow:auto;
}
[type="checkbox"], [type="radio"] {
    box-sizing:border-box;
    padding:0;
}
[type="number"]::-webkit-inner-spin-button, [type="number"]::-webkit-outer-spin-button {
    height:auto;
}
[type="search"] {
    -webkit-appearance:textfield;
    outline-offset:-2px;
}
[type="search"]::-webkit-search-decoration {
    -webkit-appearance:none;
}
::-webkit-file-upload-button {
    -webkit-appearance:button;
    font:inherit;
}
details {
    display:block;
}
summary {
    display:list-item;
}
template {
    display:none;
}
[hidden] {
    display:none;
}
body {
    font:14px / 1.5 Helvetica Neue, Helvetica, Arial, Hiragino Sans GB, Hiragino Sans GB W3, Microsoft YaHei UI, Microsoft YaHei, WenQuanYi Micro Hei, sans-serif;
}
body * {
    padding:0;
    margin:0;
    box-sizing:border-box;
}
body.whole-screen {
    position:relative;
    height:100vh;
    overflow:hidden;
}
[data-flex] {
    display:-webkit-box;
    display:-webkit-flex;
    display:-ms-flexbox;
    display:flex;
}
[data-flex~="flex:wrap"] {
    flex-wrap:wrap
}
[data-flex~="flex:nowrap"] {
    flex-wrap:nowrap
}
[data-flex] > * {
    display:block;
}
[data-flex] >[data-flex] {
    display:-webkit-box;
    display:-webkit-flex;
    display:-ms-flexbox;
    display:flex;
}
[data-flex~="dir:left"] {
    -webkit-box-orient:horizontal;
    -webkit-box-direction:normal;
    -webkit-flex-direction:row;
    -ms-flex-direction:row;
    flex-direction:row;
}
[data-flex~="dir:right"] {
    -webkit-box-orient:horizontal;
    -webkit-box-direction:reverse;
    -webkit-flex-direction:row-reverse;
    -ms-flex-direction:row-reverse;
    flex-direction:row-reverse;
    -webkit-box-pack:end;
}
[data-flex~="dir:top"] {
    -webkit-box-orient:vertical;
    -webkit-box-direction:normal;
    -webkit-flex-direction:column;
    -ms-flex-direction:column;
    flex-direction:column;
}
[data-flex~="dir:bottom"] {
    -webkit-box-orient:vertical;
    -webkit-box-direction:reverse;
    -webkit-flex-direction:column-reverse;
    -ms-flex-direction:column-reverse;
    flex-direction:column-reverse;
    -webkit-box-pack:end;
}
[data-flex~="main:left"] {
    -webkit-box-pack:start;
    -webkit-justify-content:flex-start;
    -ms-flex-pack:start;
    justify-content:flex-start;
}
[data-flex~="main:right"] {
    -webkit-box-pack:end;
    -webkit-justify-content:flex-end;
    -ms-flex-pack:end;
    justify-content:flex-end;
}
[data-flex~="main:justify"] {
    -webkit-box-pack:justify;
    -webkit-justify-content:space-between;
    -ms-flex-pack:justify;
    justify-content:space-between;
}
[data-flex~="main:center"] {
    -webkit-box-pack:center;
    -webkit-justify-content:center;
    -ms-flex-pack:center;
    justify-content:center;
}
[data-flex~="cross:top"] {
    -webkit-box-align:start;
    -webkit-align-items:flex-start;
    -ms-flex-align:start;
    align-items:flex-start;
}
[data-flex~="cross:bottom"] {
    -webkit-box-align:end;
    -webkit-align-items:flex-end;
    -ms-flex-align:end;
    align-items:flex-end;
}
[data-flex~="cross:center"] {
    -webkit-box-align:center;
    -webkit-align-items:center;
    -ms-flex-align:center;
    align-items:center;
}
[data-flex~="cross:baseline"] {
    -webkit-box-align:baseline;
    -webkit-align-items:baseline;
    -ms-flex-align:baseline;
    align-items:baseline;
}
[data-flex~="cross:stretch"] {
    -webkit-box-align:stretch;
    -webkit-align-items:stretch;
    -ms-flex-align:stretch;
    align-items:stretch;
}
[data-flex~="box:mean"] > *, [data-flex~="box:first"] > *, [data-flex~="box:last"] > *, [data-flex~="box:justify"] > * {
    width:0;
    height:auto;
    -webkit-box-flex:1;
    -webkit-flex-grow:1;
    -ms-flex-positive:1;
    flex-grow:1;
    -webkit-flex-shrink:1;
    -ms-flex-negative:1;
    flex-shrink:1;
}
[data-flex~="box:first"] >:first-child, [data-flex~="box:last"] >:last-child, [data-flex~="box:justify"] >:first-child, [data-flex~="box:justify"] >:last-child {
    width:auto;
    -webkit-box-flex:0;
    -webkit-flex-grow:0;
    -ms-flex-positive:0;
    flex-grow:0;
    -webkit-flex-shrink:0;
    -ms-flex-negative:0;
    flex-shrink:0;
}
[data-flex~="dir:top"][data-flex~="box:mean"] > *, [data-flex~="dir:top"][data-flex~="box:first"] > *, [data-flex~="dir:top"][data-flex~="box:last"] > *, [data-flex~="dir:top"][data-flex~="box:justify"] > *, [data-flex~="dir:bottom"][data-flex~="box:mean"] > *, [data-flex~="dir:bottom"][data-flex~="box:first"] > *, [data-flex~="dir:bottom"][data-flex~="box:last"] > *, [data-flex~="dir:bottom"][data-flex~="box:justify"] > * {
    width:auto;
    height:0;
    -webkit-box-flex:1;
    -webkit-flex-grow:1;
    -ms-flex-positive:1;
    flex-grow:1;
    -webkit-flex-shrink:1;
    -ms-flex-negative:1;
    flex-shrink:1;
}
[data-flex~="dir:top"][data-flex~="box:first"] >:first-child, [data-flex~="dir:top"][data-flex~="box:last"] >:last-child, [data-flex~="dir:top"][data-flex~="box:justify"] >:first-child, [data-flex~="dir:top"][data-flex~="box:justify"] >:last-child, [data-flex~="dir:bottom"][data-flex~="box:first"] >:first-child, [data-flex~="dir:bottom"][data-flex~="box:last"] >:last-child, [data-flex~="dir:bottom"][data-flex~="box:justify"] >:first-child[data-flex~="dir:bottom"][data-flex~="box:justify"] >:last-child {
    height:auto;
    -webkit-box-flex:0;
    -webkit-flex-grow:0;
    -ms-flex-positive:0;
    flex-grow:0;
    -webkit-flex-shrink:0;
    -ms-flex-negative:0;
    flex-shrink:0;
}
[data-flex-box="0"] {
    -webkit-box-flex:0;
    -webkit-flex-grow:0;
    -ms-flex-positive:0;
    flex-grow:0;
    -webkit-flex-shrink:0;
    -ms-flex-negative:0;
    flex-shrink:0;
}
[data-flex-box="1"] {
    -webkit-box-flex:1;
    -webkit-flex-grow:1;
    -ms-flex-positive:1;
    flex-grow:1;
    -webkit-flex-shrink:1;
    -ms-flex-negative:1;
    flex-shrink:1;
}
[data-flex-box="2"] {
    -webkit-box-flex:2;
    -webkit-flex-grow:2;
    -ms-flex-positive:2;
    flex-grow:2;
    -webkit-flex-shrink:2;
    -ms-flex-negative:2;
    flex-shrink:2;
}
[data-flex-box="3"] {
    -webkit-box-flex:3;
    -webkit-flex-grow:3;
    -ms-flex-positive:3;
    flex-grow:3;
    -webkit-flex-shrink:3;
    -ms-flex-negative:3;
    flex-shrink:3;
}
[data-flex-box="4"] {
    -webkit-box-flex:4;
    -webkit-flex-grow:4;
    -ms-flex-positive:4;
    flex-grow:4;
    -webkit-flex-shrink:4;
    -ms-flex-negative:4;
    flex-shrink:4;
}
[data-flex-box="5"] {
    -webkit-box-flex:5;
    -webkit-flex-grow:5;
    -ms-flex-positive:5;
    flex-grow:5;
    -webkit-flex-shrink:5;
    -ms-flex-negative:5;
    flex-shrink:5;
}
[data-flex-box="6"] {
    -webkit-box-flex:6;
    -webkit-flex-grow:6;
    -ms-flex-positive:6;
    flex-grow:6;
    -webkit-flex-shrink:6;
    -ms-flex-negative:6;
    flex-shrink:6;
}
[data-flex-box="7"] {
    -webkit-box-flex:7;
    -webkit-flex-grow:7;
    -ms-flex-positive:7;
    flex-grow:7;
    -webkit-flex-shrink:7;
    -ms-flex-negative:7;
    flex-shrink:7;
}
[data-flex-box="8"] {
    -webkit-box-flex:8;
    -webkit-flex-grow:8;
    -ms-flex-positive:8;
    flex-grow:8;
    -webkit-flex-shrink:8;
    -ms-flex-negative:8;
    flex-shrink:8;
}
[data-flex-box="9"] {
    -webkit-box-flex:9;
    -webkit-flex-grow:9;
    -ms-flex-positive:9;
    flex-grow:9;
    -webkit-flex-shrink:9;
    -ms-flex-negative:9;
    flex-shrink:9;
}
[data-flex-box="10"] {
    -webkit-box-flex:10;
    -webkit-flex-grow:10;
    -ms-flex-positive:10;
    flex-grow:10;
    -webkit-flex-shrink:10;
    -ms-flex-negative:10;
    flex-shrink:10;
}
input {
    text-rendering:auto;
    letter-spacing:normal;
    word-spacing:normal;
    line-height:normal;
    text-transform:none;
    text-indent:0px;
    text-shadow:none;
    display:inline-block;
    text-align:start;
    appearance:auto;
    cursor:text;
    margin:0em;
    padding:1px 2px;
    border:none;
    text-indent:0;
    background:transparent;
    resize:none;
    outline:none;
    -webkit-appearance:none;
    line-height:normal;
}
.input:focus {
    outline:none;
    border-color:none
}
html {
    --zhuluan-white-color:#fff;
    --zhuluan-black-3-color:#333;
    --zhuluan-black-6-color:#666;
    --zhuluan-black-80-color:#808080;
    --zhuluan-custom-e8-color:#e8e8e8;
    --zhuluan-custom-d8-color:#d8d8d8;
    --zhuluan-custom-b8-color:#b8b8b8;
    --zhuluan-custom-ff-color:#f5f6f7;
    --zhuluan-white-f2-color:#f2f2f2;
    --zhuluan-white-f5-color:#f5f5f5;
    --zhuluan-primary-color:#1781ea;
    --zhuluan-neighbor-color:#5A9AF9;
    --zhuluan-primary-color-hover:#3385ff;
    --zhuluan-primary-color-active:#096dd9;
    --zhuluan-primary-rgba-10:rgba(31, 130, 242, .08);
    --zhuluan-primary-rgba-20:rgba(31, 130, 242, .2);
    --zhuluan-border-color:#e2e2e2;
    --zhuluan-border-rgba-4:rgba(225, 225, 225, .4);
    --zhuluan-warning-color:#FD6A53;
    --zhuluan-warning-rgba-10:rgba(253, 106, 83, .08);
    --zhuluan-warning-rgba-20:rgba(253, 106, 83, .2);
    --zhuluan-shadow-color:51 133 255;
    --zhuluan-switch-size:15px;
    --zhuluan-switch-box:30px;
    --zhuluan-primary-border-radius:5px;
}
body {
    background-color:var(--zhuluan-custom-ff-color)
}
@font-face {
    font-family:"iconfont";
    src:url('iconfont.woff2') format('woff2'), url('iconfont.woff') format('woff'), url('iconfont.ttf') format('truetype');
}
.input-group {
    position:relative;
    display:-ms-flexbox;
    display:flex;
    -ms-flex-wrap:wrap;
    flex-wrap:wrap;
    -ms-flex-align:stretch;
    align-items:stretch;
    width:100%
}
.input-group>.form-control, .input-group>.form-select {
    position:relative;
    -ms-flex:1 1 auto;
    flex:1 1 auto;
    width:1%;
    min-width:10
}
.form-control {
    display:block;
    width:100%;
    height:25px;
    padding:0px 5px;
    font-size:15px;
    line-height:1.42857143;
    color:#555;
    background-color:#fff;
    border:1px solid #000;
    border-radius:4px;
    -webkit-box-shadow:inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow:inset 0 1px 1px rgba(0, 0, 0, .075);
    -webkit-transition:border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    -o-transition:border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition:border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
.iconfont {
    font-family:"iconfont" !important;
    font-size:16px;
    font-style:normal;
    -webkit-font-smoothing:antialiased;
    -moz-osx-font-smoothing:grayscale;
}
.icon-shuaxin:before {
    content:"\ec08";
}
.icon-codelibrary:before {
    content:"\ebdb";
}
.icon-jiance:before {
    content:"\e674";
}
.icon-text-add:before {
    content:"\e614";
}
.icon-close:before {
    content:"\e607";
}
.icon-menu:before {
    content:"\e608";
}
.icon-copy:before {
    content:"\e617";
}
.icon-wuguan:before {
    content:"\ec5f";
}
.icon-clear-all:before {
    content:"\e8b6";
}
.icon-wenda:before {
    content:"\e60e";
}
.icon-bangzhu:before {
    content:"\e600";
}
.icon-baobiao:before {
    content:"\e601";
}
.icon-rizhi:before {
    content:"\e602";
}
.icon-pishi:before {
    content:"\e603";
}
.icon-xinwen:before {
    content:"\e604";
}
.icon-yewu:before {
    content:"\e605";
}
.icon-tixing:before {
    content:"\e606";
}
.switch-container {
    height:var(--zhuluan-switch-size);
    width:var(--zhuluan-switch-box);
}
.switch-container label, .switch-container label:before, .switch-container label:after {
    display:block;
}
.switch-container label {
    position:relative;
    background-color:var(--zhuluan-custom-e8-color);
    height:100%;
    width:100%;
    cursor:pointer;
    border-radius:25px;
}
.switch-container label:before, .switch-container label:after {
    content:'';
}
.switch-container label:before {
    border-radius:25px;
    height:100%;
    width:var(--zhuluan-switch-size);
    background-color:var(--zhuluan-white-color);
    opacity:1;
    box-shadow:1px 1px 1px 1px rgba(0, 0, 0, 0.08);
    -webkit-transition:all 0.2s;
}
.switch-container label:after {
    position:absolute;
    top:0;
    right:0;
    left:var(--zhuluan-switch-size);
    border-radius:25px;
    height:100%;
    width:var(--zhuluan-switch-size);
    background-color:white;
    opacity:0;
    box-shadow:1px 1px 1px 1px rgba(0, 0, 0, 0.08);
    transition:opacity 0.2s ease;
}
.switch:checked~label:after {
    opacity:1;
}
.switch:checked~label:before {
    opacity:0;
}
.switch:checked~label {
    background-color:var(--zhuluan-primary-color);
}
#tooltip {
    display:none;
}
#tooltip[data-show] {
    display:block;
}
.toast {
    top:50%;
    left:50%;
    position:fixed;
    -webkit-transform:translate(-50%, -50%);
    transform:translate(-50%, -50%);
    border-radius:5px;
    background:rgba(0, 0, 0, 0.6);
    color:white;
    -webkit-box-sizing:border-box;
    box-sizing:border-box;
    text-align:center;
    padding:10px 14px;
    z-index:999999;
    -webkit-animation-duration:500ms;
    animation-duration:500ms;
}
.toast.in {
    -webkit-animation-name:contentZoomIn;
    animation-name:contentZoomIn;
}
.toast .iconfont {
    font-size:30px;
    color:rgba(255, 255, 255, 0.8);
    margin-bottom:10px;
    display:block;
}
.toast .iconfont.icon-loading:before {
    display:block;
    -webkit-transform:rotate(360deg);
    animation:rotation 2.7s linear infinite;
}
.toast .text {
    text-align:center;
    max-width:300px;
    color:#fff;
    font-size:14px;
}
@-webkit-keyframes rotation {
    from {
        -webkit-transform:rotate(0deg);
    }
    to {
        -webkit-transform:rotate(360deg);
    }
}
@-webkit-keyframes contentZoomIn {
    0% {
        -webkit-transform:translate(-50%, -70%);
        transform:translate(-50%, -70%);
        opacity:0;
    }
    100% {
        -webkit-transform:translate(-50%, -50%);
        transform:translate(-50%, -50%);
        opacity:1;
    }
}
@keyframes contentZoomIn {
    0% {
        opacity:0;
    }
    100% {
        opacity:1;
    }
}
@-webkit-keyframes contentZoomOut {
    0% {
        opacity:1;
    }
    100% {
        opacity:0;
    }
}
@keyframes contentZoomOut {
    0% {
        opacity:1;
    }
    100% {
        opacity:0;
    }
}
a.unlinks-deco {
    color:var(--zhuluan-primary-color);
    text-decoration:none;
}
.layout-header {
    height:50px;
    background-color:var(--zhuluan-white-color)
}
.layout-header .logo .links {
    cursor:pointer;
    display:block;
}
.layout-header .logo .links img {
    height:100%;
}
.layout-header .icon-menu {
    display:none
}
.layout-header .nav .list {
    font-size:16px;
    font-weight:600;
    margin-left:32px;
}
.layout-header .nav .list .links {
    color:var(--zhuluan-black-6-color);
    transition:all .4s
}
.layout-header .nav .list.active .links, .layout-header .nav .list:hover .links, .article-box .links:hover {
    color:var(--zhuluan-primary-color)
}
.layout-header .nav .nav-btn {
    padding-left:35px;
}
.layout-header .nav .btn {
    padding:6px 12px;
    margin:0 0 0 14px;
    font-size:12px;
    font-weight:400;
    line-height:1.42857143;
    text-align:center;
    white-space:nowrap;
    vertical-align:middle;
    touch-action:manipulation;
    cursor:pointer;
    border-radius:var(--zhuluan-primary-border-radius);
    color:var(--zhuluan-black-3-color);
    background-color:var(--zhuluan-white-f2-color);
    transition:all .4s;
}
.layout-header .nav .btn.sign {
    color:var(--zhuluan-white-color);
    background-color:var(--zhuluan-primary-color);
    background:linear-gradient(90deg, var(--zhuluan-primary-color), var(--zhuluan-primary-color-hover));
    box-shadow:0 4px 8px 0 rgb(var(--zhuluan-shadow-color) / 12%);
}
.layout-header .nav .btn:hover {
    opacity:.8;
    box-shadow:none
}
.layout-content {
    padding:22px 0 50px;
    background-color:#343541;
}
.layout-bar {
    font-size:14px;
    color:var(--zhuluan-black-80-color)
}
.layout-bar .num span {
    margin:0 6px;
    font-size:24px;
    font-weight:300;
    font-family:Open Sans, Arial, sans-serif;
}
.layout-bar .btn {
    width:100px;
    margin-left:14px;
    text-align:center;
    height:36px;
    line-height:36px;
    font-size:14px;
    border-radius:var(--zhuluan-primary-border-radius);
    color:var(--zhuluan-primary-color);
    cursor:pointer;
    transition:all .4s;
}
.layout-bar .layout-bar-left {
    margin-right:-14px;
}
.layout-bar .layout-bar-left .btn {
    margin:0 14px 0 0
}
.layout-bar .btn .iconfont {
    margin-right:3px;
}
.layout-bar .btn .iconfont.icon-wuguan {
    margin-right:5px;
}
.layout-bar .btn .iconfont.icon-text-add {
    margin-right:5px;
    font-size:17px;
}
.layout-bar .bright-btn {
    color:var(--zhuluan-white-color);
    background-color:var(--zhuluan-primary-color);
    background:linear-gradient(90deg, var(--zhuluan-primary-color), var(--zhuluan-primary-color-hover));
    box-shadow:0 4px 8px 0 var(--zhuluan-shadow-color);
}
.precast-block {
    padding:20px 12px;
    min-height:50px;
    border:1px solid;
}
.precast-block .title {
    width:65px;
    text-indent:10px;
    color:var(--zhuluan-black-80-color)
}
.kw-text {
    padding-top:10px;
}
#kw-box {
    margin-right:20px;
}
.kw-btn.btn {
    line-height:100%;
    color:var(--zhuluan-primary-color);
    cursor:pointer;
}
.kw-btn.btn .iconfont {
    margin-right:4px;
}
.precast-block .box {
    flex:1
}
#kw-target-box {
    border-radius:var(--zhuluan-primary-border-radius) var(--zhuluan-primary-border-radius) 0 0;
    -webkit-border-radius:;
    -moz-border-radius:;
    -ms-border-radius:;
    -o-border-radius:;
}
#kw-target-box #kw-target {
    display:block;
    height:36px;
    width:100%;
    padding-left:13px;
    font-size:16px;
    font-weight:500;
    color:#fff;
}
#kw-target::-webkit-input-placeholder {
    color:var(--zhuluan-custom-b8-color)
}
#kw-tags {
    border-top:none;
    height:28px;
    margin-bottom:-6px;
}
#kw-tags #tags-clear-all {
    margin-left:10px;
}
#kw-box {
    margin-bottom:-8px;
}
#kw-list {
    font-weight:500;
    font-size:12px;
    color:var(--zhuluan-black-80-color)
}
#xiezuo-h1 {
    color:var(--zhuluan-black-3-color);
    line-height:160%;
    margin-bottom:12px
}
#kw-list .kw, .locked-kw-tags .tag {
    display:inline-block;
    padding:6px 25px 4px 10px;
    border-radius:22px;
    margin:0 0 8px 8px;
    word-break:keep-all;
    position:relative;
    color:var(--zhuluan-primary-color);
    background-color:var(--zhuluan-primary-rgba-10);
    transition:all 0.4s
}
#kw-list .kw .icon-close, .locked-kw-tags .tag .icon-close {
    cursor:pointer;
}
#kw-list .kw:hover, .locked-kw-tags .tag:hover {
    background-color:var(--zhuluan-primary-rgba-20);
}
#kw-list .kw .iconfont, .locked-kw-tags .tag .iconfont {
    font-size:12px;
    position:absolute;
    right:8px;
    top:50%;
    transform:translateY(-50%)
}
.article .is-created-none, .article:not(.created) .is-created-block {
    display:none
}
.article:not(.created) .is-created-none, .article .is-created-block {
    display:block
}
.layout-bar .line-btn {
    color:var(--zhuluan-primary-color);
}
.article.created .xiezuo-header {
    border-radius:0;
    border-bottom:none
}
.layout-bar .line-btn:hover {
    box-shadow:0 4px 8px -2px rgb(51 51 51 / 8%)
}
.layout-bar .bright-btn:hover {
    background-color:var(--zhuluan-neighbor-color);
    box-shadow:none;
    opacity:.8
}
.magnitude #word-count {
    margin-right:4px
}
.magnitude #word-count .warning {
    color:var(--zhuluan-warning-color)
}
.article {
    width:100%;
    position:relative;
}
.article .creating-loading {
    display:none;
    position:absolute;
    z-index:10008;
    left:0;
    top:0;
    right:0;
    bottom:0;
    background-color:rgba(52, 53, 65, .68)
}
.article .creating-loading.isLoading {
    display:flex
}
.article .creating-loading .tips {
    margin-top:10px;
    color:var(--zhuluan-primary-color)
}
.article .layout-article {
    min-height:420px;
    border-radius:0 0 var(--zhuluan-primary-border-radius) var(--zhuluan-primary-border-radius);
    border:1px solid var(--zhuluan-border-color);
    background-color:var(--zhuluan-white-color);
    position:relative;
}
.layout-article .w-e-toolbar, .layout-article .w-e-text-container {
    border:none !important
}
.layout-article .w-e-toolbar {
    border-bottom:1px solid var(--zhuluan-border-color) !important;
}
.article-footer-bar {
    padding:12px 12px 6px;
    height:15px;
    color:var(--zhuluan-black-80-color)
}
.article-footer-bar .switch-container {
    margin-right:8px
}
.article-footer-bar .magnitude {
    text-align:right;
    font-size:14px;
}
.bar-fast-tool .iconfont {
    color:var(--zhuluan-custom-d8-color);
    transition:all .4s;
    cursor:pointer;
}
.bar-fast-tool .iconfont:hover {
    color:var(--zhuluan-primary-color)
}
#think-kw {
    position:absolute;
    left:0;
    top:0;
    width:100%;
    border-top:1px solid var(--zhuluan-border-color);
    background-color:var(--zhuluan-white-color);
    box-shadow:0 3px 5px rgba(30, 30, 30, .02)
}
#think-kw .list {
    padding:12px 10px;
    line-height:100%;
    border-bottom:1px solid var(--zhuluan-border-rgba-4);
    transition:all .4s;
    cursor:pointer;
}
#think-kw .list:hover, #think-kw .list.pitch {
    background-color:var(--zhuluan-warning-rgba-10)
}
#think-kw .list:last-child {
    border:none
}
#think-kw .list .text span {
    color:var(--zhuluan-warning-color)
}
#think-kw .list .num {
    color:var(--zhuluan-black-80-color)
}
.bar-fast-tool .iconfont {
    font-size:20px;
    margin-left:5px;
}
.bar-fast-tool .magnitude {
    margin-left:20px;
}
.layout-site-details {
    padding-top:70px;
    font-size:14px;
    color:var(--zhuluan-black-6-color)
}
.layout-site-details h2 {
    font-size:28px;
    line-height:120%;
    padding:0 20px;
    margin:0 0 60px;
    font-weight:500;
    text-align:center;
}
.layout-site-details p {
    line-height:200%;
}
.locked-kw {
    position:relative;
    border:1px solid var(--zhuluan-border-color);
    border-top:none;
    border-bottom:none;
}
.locked-kw .locked-kw-tags {
    display:block;
    width:100%;
    border-color:var(--zhuluan-white-color);
    border-radius:var(--zhuluan-primary-border-radius);
    background-color:var(--zhuluan-white-color);
    overflow:hidden;
}
.locked-kw-tags .tag {
    margin:0 7px 7px 0;
    font-size:12px;
}
.locked-kw .locked-kw-tags #locked-kw_addTag {
    padding-left:15px;
    padding-bottom:15px;
}
.locked-kw .locked-kw-tags #clear-all, #kw-tags #tags-clear-all {
    color:var(--zhuluan-black-80-color);
    font-size:12px;
    cursor:pointer;
    transition:all .4s
}
.locked-kw .locked-kw-tags #clear-all:hover, #kw-tags #tags-clear-all:hover {
    color:var(--zhuluan-black-3-color);
}
.locked-kw .locked-kw-tags .tags_clear {
    display:block;
    margin-bottom:-7px
}
.locked-kw #locked-kw_tag {
    font-size:12px;
    font-weight:normal;
    padding:8px 0
}
.footer {
    padding-bottom:15px
}
.footer p {
    text-align:center;
    margin-bottom:8px
}
.footer p, .footer .links {
    color:var(--zhuluan-black-80-color)
}
.footer .foot-menu {
    font-size:16px
}
.footer .links {
    margin:0 6px;
    word-break:keep-all;
    transition:all .4s
}
.footer .links:hover {
    color:var(--zhuluan-black-3-color)
}
.content-list {
    padding-bottom:20px;
}
.content-list li {
    font-size:16px;
    line-height:200%;
    text-indent:32px;
    color:var(--zhuluan-black-80-color)
}
.zhuluan-cms {
    border-bottom:none;
    border-top:none
}
.zhuluan-cms img {
    width:30%;
    vertical-align:top
}
.anchor-box {
    position:fixed;
    z-index:9999;
    left:0;
    top:0;
    width:100vw;
    height:100vh;
}
.anchor-box .anchor-content {
    position:absolute;
    width:420px;
    height:220px;
    left:50%;
    top:50%;
    padding:35px 25px;
    transform:translate(-50%, -50%);
    background-color:var(--zhuluan-white-color);
    box-shadow:3px 3px 38px rgba(0, 0, 0, .1), -3px -3px 38px rgba(0, 0, 0, .1);
}
.anchor-box .anchor-content .icon-close {
    position:absolute;
    right:12px;
    top:8px;
    font-size:14px;
    color:var(--zhuluan-black-80-color);
    cursor:pointer;
}
.anchor-box .anchor-content .h4 {
    margin-bottom:12px;
    font-size:15px;
}
.anchor-box .anchor-content .h4 span {
    color:var(--zhuluan-black-80-color);
    margin-left:10px;
    font-size:12px
}
.anchor-box .anchor-content em {
    font-style:normal;
}
.anchor-box .anchor-content em.warning {
    color:var(--zhuluan-warning-color);
}
.anchor-content .input-list {
    margin-bottom:10px;
}
.anchor-content .input {
    border:solid 1px var(--zhuluan-border-color);
    padding:8px 10px;
    width:100%;
    border-radius:2px;
    display:block;
}
.anchor-content .input:focus {
    border-color:var(--zhuluan-primary-color);
}
.anchor-content .btn-box {
    color:var(--zhuluan-black-80-color);
}
.anchor-content .btn-box .btn {
    margin-left:12px;
    cursor:pointer;
    transition:all .4s
}
.anchor-content .btn-box .btn:hover {
    opacity:.6
}
.anchor-content .btn-box .btn.add {
    color:var(--zhuluan-primary-color);
}
.switch-bar .tips {
    margin-left:10px;
}
.anchor-box {
    position:absolute;
    z-index:99998;
    left:0;
    top:0;
    width:100%;
    height:100%;
    background-color:rgba(255, 255, 255, .5);
}
.banner {
    padding-top:40px;
    height:460px;
}
.banner .images {
    width:582px;
    height:424px;
}
.banner .images img {
    width:100%;
    height:auto;
}
.banner .title {
    padding-left:55px;
}
.banner h2, .title h2 {
    font-size:26px;
    font-weight:normal;
    padding-bottom:10px;
}
.banner p {
    margin-top:6px;
    color:var(--zhuluan-black-6-color)
}
.banner .btn {
    display:block;
    margin-top:22px;
    width:120px;
    height:42px;
    line-height:42px;
    border-radius:5px;
    text-align:center;
    color:var(--zhuluan-white-color);
    font-size:16px;
    background-color:var(--zhuluan-primary-color);
    box-shadow:0 6px 18px 0 rgb(var(--zhuluan-shadow-color) / 20%);
    cursor:pointer;
    transition:all .4s
}
.banner .btn .iconfont {
    font-size:18px;
    margin-right:5px;
}
.banner .btn:hover {
    background-color:var(--zhuluan-primary-color-hover);
    box-shadow:none
}
.xiezuo {
    background-color:var(--zhuluan-white-color);
    padding:65px 0;
}
.xiezuo .title {
    text-align:center;
}
.xiezuo .title h2 {
    padding-bottom:6px;
}
.xiezuo .title p {
    color:var(--zhuluan-black-80-color)
}
.xiezuo .content {
    margin-left:-20px;
    box-sizing:border-box;
    width:100%;
}
.xiezuo .list .icon, .xiezuo .list .icon img {
    margin:0 auto;
    width:120px;
    height:120px;
}
.xiezuo .list .icon {
    margin-bottom:12px;
}
.xiezuo .list .icon img {
    opacity:.9
}
.xiezuo .content .list {
    border:1px solid var(--zhuluan-custom-ff-color);
    border-radius:5px;
    padding:25px 10px;
    text-align:center;
    width:calc(19.999999% - 22px);
    margin:0 0 20px 20px;
    box-sizing:border-box;
}
.xiezuo .list a {
    display:block;
}
.xiezuo .content .list:last-child {
    display:none;
}
.xiezuo .content .list .h3 {
    font-size:24px;
    font-weight:400;
    color:var(--zhuluan-black-3-color)
}
.xiezuo .content .list p {
    font-family:sans-serif;
    font-size:18px;
    color:var(--zhuluan-black-80-color);
    text-transform:uppercase
}
.xiezuo .content .list, .xiezuo .content .list img, .xiezuo .content .list .h3, .xiezuo .content .list p {
    transition:all .35s
}
.xiezuo .content .list:hover {
    border-style:dashed;
    border-color:var(--zhuluan-primary-color)
}
.xiezuo .content .list:hover img {
    opacity:.6
}
.xiezuo .content .list:hover .h3 {
    color:var(--zhuluan-primary-color)
}
.xiezuo .content .list:hover p {
    color:var(--zhuluan-black-3-color)
}
.relevance {
    padding-top:50px;
}
.relevance .ve-clever {
    margin-left:-20px;
    padding-top:25px;
}
.relevance .list {
    border:1px dashed var(--zhuluan-custom-ff-color);
    margin:0 0 20px 20px;
    width:calc(24.999999% - 22px)
}
.relevance .list dt {
    padding:14px;
    border-bottom:1px dashed var(--zhuluan-custom-ff-color);
    text-align:center;
    font-size:16px;
    color:var(--zhuluan-black-6-color)
}
.relevance .list dd {
    padding:20px 0;
    text-align:center;
    color:var(--zhuluan-black-80-color)
}
.relevance .list dd p {
    width:49.99999%;
    padding:8px 35px;
}
.xiezuo-header {
    text-align:center;
    padding:25px 18px;
    border-radius:var(--zhuluan-primary-border-radius);
    -webkit-border-radius:var(--zhuluan-primary-border-radius);
    -moz-border-radius:var(--zhuluan-primary-border-radius);
    -ms-border-radius:var(--zhuluan-primary-border-radius);
    -o-border-radius:var(--zhuluan-primary-border-radius);
}
.xiezuo-header span {
    color:var(--zhuluan-black-80-color)
}
.xiezuo-header h3 {
    padding:10px;
    font-size:24px;
    font-weight:normal;
}
.xiezuo-header li {
    margin:0 2px 4px 2px;
}
.xiezuo-header li span {
    position:relative;
    margin-left:3px;
}
.xiezuo-header li span:before {
    content:"#";
    color:var(--zhuluan-custom-d8-color);
    margin-right:3px;
}
.container {
    padding-right:15px;
    padding-left:15px;
    margin-right:auto;
    margin-left:auto
}
@media (min-width:768px) {
    .container {
        width:750px
    }
}
@media (min-width:992px) {
    .container {
        width:980px
    }
}
@media (min-width:1200px) {
    .container {
        width:95%;
    }
}
@media (max-width:1444px) and (min-width:993px) {
    .xiezuo .content .list {
        width:calc(33.333333% - 22px);
    }
    .xiezuo .content .list:last-child {
        display:block
    }
    .relevance .list dd p {
        padding:8px 15px;
    }
}
@media (max-width:992px) {
    .layout-header {
        position;
        relative;
    }
    .layout-header, .layout-header .logo, .layout-header .logo .links {
        height:50px
    }
    .layout-header .logo .links img {
        width:auto
    }
    .layout-header .icon-menu {
        display:block
    }
    .header-nav .nav {
        display:none;
    }
    .header-nav.open-menu .nav {
        display:block;
        position:absolute;
        z-index:10008;
        left:0;
        right:0;
        top:60px;
        width:100%;
        padding:15px 0 25px;
        border-top:1px solid var(--zhuluan-custom-d8-color);
        background-color:var(--zhuluan-white-color);
        box-shadow:0 6px 6px rgba(30, 30, 30, .06)
    }
    .layout-header .nav .list {
        height:35px;
        line-height:35px;
    }
    .layout-header .nav .nav-btn {
        padding-left:32px;
        padding-top:15px;
    }
    .layout-header .nav .btn {
        margin:0 14px 0 0
    }
    .layout-content {
        padding:18px 0 32px
    }
    .article .creating-loading {
        bottom:50%;
    }
    .layout-content .article {
        -webkit-box-orient:vertical;
        -webkit-box-direction:normal;
        -webkit-flex-direction:column;
        -ms-flex-direction:column;
        flex-direction:column;
    }
    .article .layout-article {
        width:100%;
        margin-bottom:12px
    }
    .anchor-box .anchor-content {
        width:92vw
    }
    .banner {
        display:block;
        padding-top:0;
        height:400px;
    }
    .banner .images {
        width:300px;
        height:200px;
        margin:0 auto
    }
    .banner h2, .title h2 {
        font-size:24px
    }
    .banner .title {
        padding-left:25px;
    }
    .xiezuo .content .list {
        width:calc(49.999999% - 22px)
    }
    .xiezuo {
        padding:40px 0 25px
    }
    .xiezuo .content {
        padding-top:30px;
        margin-left:-10px;
    }
    .xiezuo .content .list {
        padding:15px 10px;
    }
    .xiezuo .content .list .icon, .xiezuo .content .list .icon img {
        width:20vw;
        height:20vw
    }
    .xiezuo .content .list .h3 {
        font-size:20px;
    }
    .xiezuo .content .list p {
        font-size:16px;
    }
    .layout-site-details {
        padding-top:45px;
    }
    .layout-site-details h2 {
        margin-bottom:40px;
    }
    .xiezuo .content .list:last-child {
        display:block;
    }
    .relevance .list {
        width:calc(49.999999% - 22px)
    }
    .relevance .list dd p {
        padding:4px 5px;
    }
    .zhuluan-cms img {
        width:80%;
    }
    #xiezuo-h1 {
        font-size:18px;
    }
    #kw-tags {
        height:auto;
    }
    #kw-box {
        margin-bottom:4px;
    }
    .article-box {
        margin:0 -10px;
        padding:20px;
    }
}
.semi-circle-spin {
    position:relative;
    width:35px;
    height:35px;
    overflow:hidden;
}
.semi-circle-spin:after {
    content:'';
    position:absolute;
    border-width:0px;
    border-radius:100%;
    -webkit-animation:spin-rotate 0.6s 0s infinite linear;
    animation:spin-rotate 0.6s 0s infinite linear;
    background-image:-webkit-linear-gradient(transparent 0%, transparent 70%, var(--zhuluan-primary-color) 30%, var(--zhuluan-primary-color) 100%);
    background-image:linear-gradient(transparent 0%, transparent 70%, var(--zhuluan-primary-color) 30%, var(--zhuluan-primary-color) 100%);
    width:100%;
    height:100%;
}
@-webkit-keyframes spin-rotate {
    0% {
        -webkit-transform:rotate(0deg);
        transform:rotate(0deg);
    }
    50% {
        -webkit-transform:rotate(180deg);
        transform:rotate(180deg);
    }
    100% {
        -webkit-transform:rotate(360deg);
        transform:rotate(360deg);
    }
}
@keyframes spin-rotate {
    0% {
        -webkit-transform:rotate(0deg);
        transform:rotate(0deg);
    }
    50% {
        -webkit-transform:rotate(180deg);
        transform:rotate(180deg);
    }
    100% {
        -webkit-transform:rotate(360deg);
        transform:rotate(360deg);
    }
}
.ball-clip-rotate-multiple {
    position:relative;
}
.ball-clip-rotate-multiple > div {
    -webkit-animation-fill-mode:both;
    animation-fill-mode:both;
    position:absolute;
    left:0px;
    top:0px;
    border:2px solid #fff;
    border-bottom-color:transparent;
    border-top-color:transparent;
    border-radius:100%;
    height:35px;
    width:35px;
    -webkit-animation:rotate 1s 0s ease-in-out infinite;
    animation:rotate 1s 0s ease-in-out infinite;
}
.ball-clip-rotate-multiple > div:last-child {
    display:inline-block;
    top:10px;
    left:10px;
    width:15px;
    height:15px;
    -webkit-animation-duration:0.5s;
    animation-duration:0.5s;
    border-color:#fff transparent #fff transparent;
    -webkit-animation-direction:reverse;
    animation-direction:reverse;
}
