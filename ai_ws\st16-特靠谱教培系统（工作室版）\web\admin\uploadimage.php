<?php
require_once('check_admin.php');

// 验证用户上传的文件是否存在
if (!empty($_FILES['file']['name'])) {
    // 获取上传文件的扩展名
    $fileExtension = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));

    // 允许上传的图片格式
    $allowedExtensions = array('jpg', 'jpeg', 'png', 'gif');

    // 检查文件扩展名是否在允许的格式列表中
    if (in_array($fileExtension, $allowedExtensions)) {
        // 过滤用户输入的文件名，并用time()函数创建新文件名
        $filename = htmlspecialchars($_FILES['file']['name'], ENT_QUOTES, 'UTF-8');
        $temp = explode(".", $filename);
        $newfilename = time() . '.' . end($temp);

        // 将文件移动到upload/img目录
        move_uploaded_file($_FILES['file']['tmp_name'], "../upload/img/" . $newfilename);

        // 输出文件的新路径
        echo substr($_SERVER["REQUEST_URI"], 0, strrpos(substr($_SERVER["REQUEST_URI"], 0, strrpos($_SERVER["REQUEST_URI"], '/')), '/')) . '/upload/img/' . $newfilename;
    } else {
        echo "只能上传图片格式的文件";
    }
}
