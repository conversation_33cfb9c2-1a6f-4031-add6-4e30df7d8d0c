<?php

if ((isset($_GET["type"])) && ($_GET["type"] == "all")) {
    if (isset($_GET["userrndstr"])) {
        require_once('admin/mysqlconn.php');
        $row = $conn->get('user', '*', ['rndstr' => $_GET["userrndstr"]]);
        if (empty($row)) {
            echo '{"success":false}';
        } else {
            $more = intval($_GET["more"]) ?? 0;
            $sql = "SELECT t1.conversationid, t1.id, t1.title,t1.realtime,t2.num FROM chathistory t1 INNER JOIN (SELECT conversationid, MIN(id) AS max_id,count(userid)*2 as num FROM chathistory WHERE (not ishidden) AND (userid=" . $row["id"] . ") GROUP BY conversationid ORDER BY max_id DESC LIMIT " . $more * 30 . ",30) t2 ON t1.conversationid = t2.conversationid AND t1.id = t2.max_id GROUP BY t1.conversationid ORDER BY t1.id desc";
            $result = $conn->query($sql)->fetchAll();
            // $result = $conn->debug()->select("chathistory",["[>]chathistory"=>["conversationid"=>"conversationid"],],["t1.conversationid","t1.id","t1.title","t1.realtime","t2.num"],["t1.id"=>Medoo\Medoo::raw("MIN(t2.id)"),"t2.num"=> Medoo\Medoo::raw("count(t2.userid)*2")],["AND"=>["t1.ishidden[!]"=>1,"t2.userid"=>$row["id"]],"GROUP"=>"t1.conversationid","ORDER"=>["t1.id"=>"DESC"],"LIMIT"=>[$_GET["more"] * 30,30]]);

            if (empty($result)) {
                echo '{"success":true,"conversation":""}';
            } else {
                $questions = array();
                foreach ($result as $row) {
                    $questions[] = array(
                        'id' => $row['conversationid'],
                        'title' => htmlspecialchars($row['title']),
                        'realtime' => $row['realtime'],
                        'num' => $row['num']
                    );
                }
                echo '{"success":true,"conversation":' . json_encode($questions) . '}';
            }
        }
    }
}

if ((isset($_GET["type"])) && ($_GET["type"] == "single")) {
    if (isset($_GET["userrndstr"])) {
        require_once('admin/mysqlconn.php');
        $row = $conn->get('user', '*', ['rndstr' => $_GET["userrndstr"]]);
        if (empty($row)) {
            echo '{"success":false}';
        } else {
            $result = $conn->select("chathistory", "*", ["userid" => $row["id"], "conversationid" => $_GET["conversationid"], "ORDER" => ["id" => "ASC"]]);
            if (empty($result)) {
                echo '{"success":true,"conversation":""}';
            } else {
                $conversations = array();
                foreach ($result as $row) {
                    if (!empty($row["modelid"])) {
                        $row2 = $conn->get('model', '*', ['id' => $row["modelid"]]);
                    } else {
                        $row2 = $conn->get("model", "*", ['ORDER' => ['sequenceid', 'id']]);
                    }
                    $conversations[] = array(
                        'id' => $row['id'],
                        'title' => htmlspecialchars($row['title']),
                        'question' => htmlspecialchars($row['question']),
                        'model' => $row2['modelname'],
                        'answer' => $row['answer'],
                        'realtime' => $row['realtime']
                    );
                }
                echo '{"success":true,"conversation":' . json_encode($conversations) . '}';
            }
        }
    }
}
