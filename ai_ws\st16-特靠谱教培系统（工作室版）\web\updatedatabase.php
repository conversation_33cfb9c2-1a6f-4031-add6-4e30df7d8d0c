<?php
require_once('admin/mysqlconn.php');
$sql = "SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = 'chatgpt' AND table_name = 'main' AND column_name = 'weixinaddress'";
$result = $conn->query($sql);
$row = $result->fetch();
if ($row["count"] == 0) {
    $result = $conn->query("ALTER TABLE main ADD COLUMN `weixinaddress` text");
    $result = $conn->query("UPDATE main SET weixinaddress='http://wx.ipfei.com/getwxopenid.php' where id=1");
    echo "已添加 weixinaddress 字段<br>";
}
$sql = "SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = 'chatgpt' AND table_name = 'main' AND column_name = 'weixinredirecturl'";
$result = $conn->query($sql);
$row = $result->fetch();
if ($row["count"] == 0) {
    $result = $conn->query("ALTER TABLE main ADD COLUMN `weixinredirecturl` text");
    echo "已添加 weixinredirecturl 字段<br>";
}
$sql = "SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = 'chatgpt' AND table_name = 'main' AND column_name = 'updateurl'";
$result = $conn->query($sql);
$row = $result->fetch();
if ($row["count"] == 0) {
    $result = $conn->query("ALTER TABLE main ADD COLUMN `updateurl` varchar(100) DEFAULT NULL");
    $result = $conn->query("UPDATE main SET updateurl='http://update.ipfei.com/checkupdate.php' where id=1");
    echo "已添加 updateurl 字段<br>";
}
$sql = "SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = 'chatgpt' AND table_name = 'main' AND column_name = 'license'";
$result = $conn->query($sql);
$row = $result->fetch();
if ($row["count"] == 0) {
    $result = $conn->query("ALTER TABLE main ADD COLUMN `license` varchar(100) DEFAULT NULL");
    echo "已添加 license 字段<br>";
}
$sql = "SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = 'chatgpt' AND table_name = 'main' AND column_name = 'lastupdatetime'";
$result = $conn->query($sql);
$row = $result->fetch();
if ($row["count"] == 0) {
    $result = $conn->query("ALTER TABLE main ADD COLUMN `lastupdatetime` datetime DEFAULT NULL");
    echo "已添加 weixinaddress 字段<br>";
}
$result = $conn->query("UPDATE main SET version='1.0.2318.1' where id=1");

