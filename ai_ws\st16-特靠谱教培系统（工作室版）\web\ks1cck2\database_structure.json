{"alipaylist": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "userid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "cardtype", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "ordertime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "confirmtime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "clientip", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "out_trade_no", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "memo", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "isconfirmed", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}], "apikey": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "keytype", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "apikey", "Type": "<PERSON><PERSON><PERSON>(1024)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "isvalid", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "lasttime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "errmsg", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "memo", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "createtime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "checktime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "credit", "Type": "double", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "remain", "Type": "double", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "apiadd<PERSON>", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "asr": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "asrmodel", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "openaiapikey", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "openaiapiaddress", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "assistant": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "assistant<PERSON>", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "apikey", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "apiadd<PERSON>", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "modelvalue", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "assistantname", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "description", "Type": "<PERSON><PERSON><PERSON>(512)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "instructions", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "tools", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "filelist", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "memo", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "createtime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "card": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "batchid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "cardtype", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "cardid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "cardpass", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "createtime", "Type": "datetime", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "is<PERSON><PERSON><PERSON>", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "isused", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "binduser", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "bindtime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "memo", "Type": "text", "Null": "NO", "Key": "", "Default": null, "Extra": ""}], "cardtype": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "cardname", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "price", "Type": "float", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "quota", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "extenddays", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "paymenturl", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "createtime", "Type": "datetime", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "memo", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "ishidden", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}], "cck1_groups": [{"Field": "group_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "group_name", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "<PERSON><PERSON><PERSON>(32)", "Null": "NO", "Key": "", "Default": "", "Extra": ""}, {"Field": "password", "Type": "<PERSON><PERSON><PERSON>(64)", "Null": "NO", "Key": "", "Default": "", "Extra": ""}, {"Field": "description", "Type": "var<PERSON><PERSON>(500)", "Null": "NO", "Key": "", "Default": "", "Extra": ""}, {"Field": "owner_id", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "config", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "code", "Type": "<PERSON><PERSON><PERSON>(32)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "cck1_link_views": [{"Field": "view_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "link_id", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "viewed_at", "Type": "<PERSON><PERSON><PERSON>(32)", "Null": "NO", "Key": "", "Default": "", "Extra": ""}, {"Field": "read_count", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "duration", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "remark", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": "", "Extra": ""}, {"Field": "version", "Type": "tinyint(3) unsigned", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}], "cck1_links": [{"Field": "link_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "url", "Type": "var<PERSON><PERSON>(500)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "title", "Type": "<PERSON><PERSON><PERSON>(200)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "mode", "Type": "enum('free','group')", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "group_id", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}], "cck1_material_assignments": [{"Field": "assignment_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "material_id", "Type": "int(11)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "user_id", "Type": "int(11)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "checked_out_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}, {"Field": "viewed_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "cck1_materials": [{"Field": "material_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "topic_id", "Type": "int(11)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "file_name", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "file_path", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}, {"Field": "created_by", "Type": "int(11)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}], "cck1_message_links": [{"Field": "link_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int(11)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "url", "Type": "var<PERSON><PERSON>(500)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "title", "Type": "<PERSON><PERSON><PERSON>(200)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}], "cck1_message_previews": [{"Field": "preview_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "link_id", "Type": "int(11)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "message", "Type": "text", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "uuid", "Type": "char(32)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}, {"Field": "max_times", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "current_times", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}], "cck1_topics": [{"Field": "topic_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "title", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "description", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}, {"Field": "created_by", "Type": "int(11)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}], "cck1_users": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "public_account_name", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "username", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "password", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "role", "Type": "enum('user','admin','guest','vip')", "Null": "YES", "Key": "", "Default": "guest", "Extra": ""}, {"Field": "wechat_name", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "wechat_openid", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "email", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "phone", "Type": "<PERSON><PERSON><PERSON>(20)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "group_id", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "user_id", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "cck2_groups": [{"Field": "group_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "group_name", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}], "cck2_link_views": [{"Field": "view_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int(11)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "link_id", "Type": "int(11)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "viewed_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}], "cck2_links": [{"Field": "link_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int(11)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "url", "Type": "var<PERSON><PERSON>(500)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "title", "Type": "<PERSON><PERSON><PERSON>(200)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "mode", "Type": "enum('free','group')", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "group_id", "Type": "int(11)", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}], "cck2_users": [{"Field": "user_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "username", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "password", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "group_id", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}], "cck3_groups": [{"Field": "group_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "group_name", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}], "cck3_link_views": [{"Field": "view_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int(11)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "link_id", "Type": "int(11)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "viewed_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}], "cck3_links": [{"Field": "link_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int(11)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "url", "Type": "var<PERSON><PERSON>(500)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "title", "Type": "<PERSON><PERSON><PERSON>(200)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "mode", "Type": "enum('free','group')", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "group_id", "Type": "int(11)", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}], "cck3_users": [{"Field": "user_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "username", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "password", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "group_id", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}], "cck_articles": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "link", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "<PERSON><PERSON><PERSON>(32)", "Null": "NO", "Key": "", "Default": "", "Extra": ""}, {"Field": "title", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "cck_checks": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "article_id", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "checked_at", "Type": "<PERSON><PERSON><PERSON>(32)", "Null": "NO", "Key": "", "Default": "", "Extra": ""}, {"Field": "read_count", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}], "cck_users": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "public_account_name", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "username", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "password", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "role", "Type": "enum('user','admin','guest','vip')", "Null": "YES", "Key": "", "Default": "guest", "Extra": ""}, {"Field": "wechat_name", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "wechat_openid", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "email", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "phone", "Type": "<PERSON><PERSON><PERSON>(20)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "chathistory": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "title", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "question", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "answer", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "conversationid", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "modelid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "realtime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "userid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "iserror", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "ishidden", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}], "errorlog": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "question", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "errmsg", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "conversationid", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "modelid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "realtime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "userid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "apikey", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "main": [{"Field": "id", "Type": "int(32) unsigned", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "companyname", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "websitename", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "wxappletaddress", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "apiadd<PERSON>", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": "https://api.openai.com", "Extra": ""}, {"Field": "gpt4apiaddress", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": "https://api.openai.com", "Extra": ""}, {"Field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "proxyaddress", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "<PERSON><PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "imagesitetoken", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "freetry", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "freedays", "Type": "int(11)", "Null": "YES", "Key": "", "Default": "7", "Extra": ""}, {"Field": "freetryeveryday", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "freetryshare", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "headlogo", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "contentlogo", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "mailaddress", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "mailsender", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "mailaccount", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "mailpassword", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "smprovider", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "smaddress", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "smus<PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "smpassword", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "version", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "ftp", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "payment_url", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "alipayprivatekey", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "newweixinaddress", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "weixinredirecturl", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "<PERSON>url", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "license", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "lastupdatetime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "adminweixinid", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "payment_type", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "b<PERSON><PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "isquestionsensor", "Type": "<PERSON><PERSON>(4)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "isanswersensor", "Type": "<PERSON><PERSON>(4)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "isquestionfilter", "Type": "<PERSON><PERSON>(4)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "isanswerfilter", "Type": "<PERSON><PERSON>(4)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "isdrawtranslate", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "iswebsiteinfo", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "websiteinfotitle", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "websiteinfocontent", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "websiteinfotime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "isweixinlogin", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "iswindowlogin", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "issupereasylogin", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "thirdpartyloginurl", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "isweixinregister", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "thirdpartytoken", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "isthirdpartylogin", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "fakeprompt", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "rndstrseed", "Type": "bigint(20) unsigned", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "isantisqlinjection", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "welcomemessage", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "maxcon<PERSON><PERSON><PERSON>", "Type": "int(11)", "Null": "YES", "Key": "", "Default": "3000", "Extra": ""}], "model": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "modeltype", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": "openai", "Extra": ""}, {"Field": "modelname", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "modelvalue", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "modelprice", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "isvalid", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "createtime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "memo", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "sequenceid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "isonline", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "istranslate", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "isimage", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}], "myfilter": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "keyword", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}], "officer": [{"Field": "id", "Type": "int(32) unsigned", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "usertype", "Type": "<PERSON><PERSON><PERSON>(2)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "username", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "password", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "email", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "realname", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "tel", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "memo", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "registertime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "realtime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "logintime", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "loginip", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "rechargelog": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "userid", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "quota", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "extenddays", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "rechargetime", "Type": "datetime", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "rechargecardid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "operatorid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "memo", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "wxpayl<PERSON>id", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "role": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "rolename", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "rolevalue", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "rolecode", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "isvalid", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "createtime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "memo", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "scene": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "scenename", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "scenevalue", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "isvalid", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "createtime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "memo", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "template": [{"Field": "id", "Type": "int(32) unsigned", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "process", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "template", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "tts": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "ttsmodel", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "microsoftapikey", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "microsoftapiaddress", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "microsoftrole", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": "zh-CN-XiaoxiaoNeural", "Extra": ""}, {"Field": "microsoftspeed", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "microsoftvolume", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "xun<PERSON><PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "int(10)", "Null": "NO", "Key": "", "Default": "50", "Extra": ""}, {"Field": "xunfeivolume", "Type": "int(10)", "Null": "YES", "Key": "", "Default": "50", "Extra": ""}, {"Field": "xunfeispeed", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "50", "Extra": ""}, {"Field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": "<PERSON><PERSON><PERSON><PERSON>", "Extra": ""}, {"Field": "openaiapikey", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "openaiapiaddress", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "openaimodel", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": "tts-1", "Extra": ""}, {"Field": "openairole", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "openaispeed", "Type": "float", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}], "user": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "openid", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "newopenid", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "appletopenid", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "userid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "username", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "email", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "tel", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "password", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "avatar", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "credits", "Type": "int(11)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "quota", "Type": "int(11)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "expiretime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "memo", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "registertime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "forbiddentime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "isforbidden", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "rndstr", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "loginip", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "logintime", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "ismobile", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "lastquestion", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "lastmodelid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "questioncount", "Type": "int(11)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "sharefromuserid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "visit_records": [{"Field": "record_id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "link_id", "Type": "int(11)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "ip_address", "Type": "<PERSON><PERSON><PERSON>(45)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "user_agent", "Type": "text", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "referrer", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "is_wechat", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "is_first_visit", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "1", "Extra": ""}, {"Field": "visit_time", "Type": "timestamp", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": ""}], "wxpaylist": [{"Field": "id", "Type": "int(11)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "userid", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "cardtype", "Type": "int(11)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "ordertime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "confirmtime", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "clientip", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "attachid", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "out_trade_no", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "transaction_id", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "payopenid", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "memo", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "isconfirmed", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}]}