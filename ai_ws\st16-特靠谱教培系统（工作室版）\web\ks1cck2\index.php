<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>cck2仪表盘</title>
    
</head>
<body>
<?php
require '_ks1.php';
@session_start();

if (!isset($_SESSION[SESSION_KEY]['user']['id']) || !in_array($_SESSION[SESSION_KEY]['user']['role'], ['user', 'admin'])) {
    header('Location: login.php?from=index_miss_s_u_id');
    exit;
}

$user_id = null;
$role = null;
$username = null;
$user_group_id = null; // 新增变量，用于存储用户所属群组ID

if (isset($_SESSION[SESSION_KEY]['user']['id'])) {
    $user_id = $_SESSION[SESSION_KEY]['user']['id'];
    $role = $_SESSION[SESSION_KEY]['user']['role'] ?? null;
    $username = $_SESSION[SESSION_KEY]['user']['username'] ?? null;
    $user_group_id = $_SESSION[SESSION_KEY]['user']['group_id'] ?? null; // 获取用户所属群组ID
}

if (intval($user_group_id)<=1) {
    $user_group_id = null;
}

if (isset($_GET["mode"]) && $_GET["mode"]=='group' ) {
    $page_mode=2;
}else{
    $page_mode=1;
}

$total_links_group = get_cck_links_count_mode(false);
$total_checks = get_cck_link_views_count();
$total_users = get_cck_users_count();

// 获取最热群组数据
$hot_groups = get_hot_groups();

$css_file='res/wx_style.css';
?>
<link rel="stylesheet" href="<?php echo $css_file; ?>"> <!-- 动态引用CSS -->
<style type="text/css">
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* 基础样式 */
#mode_div {
    display: flex;
    flex-wrap: wrap; /* 允许子元素换行 */
}
#mode_div > div {
    flex: 1; /* 默认情况下每个子元素占1份 */
    padding: 10px;
    box-sizing: border-box; /* 包含内边距 */
}

/* 手机端样式（屏幕宽度小于600px） */
@media (max-width: 600px) {
    #mode_div > div {
        flex: 100%; /* 每个子元素占满整行 */
    }
}

/* 电脑端样式（屏幕宽度大于600px） */
@media (min-width: 600px) {
    #mode_div > div {
        flex: 1; /* 每个子元素并排显示，占1份 */
    }
}

#mode2div {
    background-color: #f4f4f4;
    padding: 10px;
    text-align: center;
    cursor: pointer;
}
#mode2desc {
    display: none;
    margin: 10px;
}
#mode2desc li {
    margin: 5px 0;
}
.tog-btn {
    cursor: pointer;
    color: blue;
    text-decoration: underline;
}
#stage-details {
    display: none;
    margin: 10px;
}
@media (max-width: 600px) {
    #mode2div, #mode2desc, #stage-details {
        padding: 15px;
    }
}
</style>

<div style="text-align: center;">
    <?php if (isset($_SESSION[SESSION_KEY]['user']['id'])): ?>
        <h1>欢迎，<?php
if ($username=='default_user'){
    echo '请先到[profile]中修改昵称!';
}else{
    echo $username;
}        
if (!empty($user_group_id)) echo ' 组id='.$user_group_id;
         ?></h1>
        <div>
            <?php echo $nav_html; ?>
        </div>
        <?php if ($role == 'admin'): ?>
            <a href="admin.php">查看检查记录</a>
        <?php endif; ?>
    <?php endif; ?>

    <div id='mode_div' style="display: flex; justify-content: center; align-items: center;">

        <div style="flex: 1;<?php if($page_mode==2) echo ' display: none;';?>">
            <p>文章总数：<?php echo $total_links_group ?? 0; ?> 个</p>
            <p>共创总次数：<?php echo $total_checks ?? 0; ?> 个</p>
            <p>用户总数：<?php echo $total_users ?? 0; ?> 个</p>
            <div>
                <form action="dashboard.php" method="post" style="margin-top: 10px;">
                    <input type="hidden" name="group_id" value="0">
                    <button type="submit">进入大厅（非群组）</button>
                </form>
            </div>
            
        </div>
        <div style="flex: 1;<?php if($page_mode!=2) echo ' display: none;';?>">

    <div><span id='mode2div' class='tog-btn' onclick="toggleDisplay('mode2desc')">[本站的群组模式]</span></div>
    <ul id='mode2desc'>
        <li>先简单介绍一下本站说的群组模式，也有人称之为圈子。在本站是指从某一个到数个维度上有共性的一些人一起做什么。</li>
        <li>理想很丰满，现实很骨感。</li>
        <li>我服务器资源不足;小伙伴们从自己领域定位到技法经验也都有大幅成长空间。</li>
        <li>
            目前试验两种群组模式：
            <br>1.<span id="stage-group" class='tog-btn' onclick="toggleDisplay('stage-details')">【预设分阶段群组】</span>
            <div id="stage-details">
                <p>L0，自由大厅</p>
                <p>L1，定位探索期（0-100粉）</p>
                <p>L2，冷启动期（100-300粉）</p>
                <p>L3，冲刺开通期（300-500粉）</p>
                <p>L4，流量池攻坚期（500-800粉）</p>
                <p>L5，商业跃升期（800-1000以上）</p>
            </div>
            <br>2.特约自定义群组
        </li>
    </ul>

        </div>
        <div style="flex: 1;<?php if($page_mode!=2) echo ' display: none;';?>">
            <h2>群组模式</h2>
            
            <?php if ($user_group_id): ?>
                <!-- 如果用户已加入群组，显示进入群组的按钮 -->
                <form action="enter_group.php?group_id=<?php echo $user_group_id; ?>" method="post" style="margin-top: 10px;">
                    <input type="hidden" name="group_id" value="<?php echo $user_group_id; ?>">
                    <button type="submit">进入我的群组 #<?php echo $user_group_id; ?></button>
                </form>
            <?php else: ?>
                <!-- 如果用户未加入群组，显示加入群组的表单 -->
                <form action="join_group.php" method="post" style="margin-top: 10px;">
                    <input type="text" id="group_code" name="group_code" required placeholder="群组代号。自由大厅代号new，其他群组代号见介绍或社群通知。">
                    <input type="text" id="group_pawd" name="group_pawd" required placeholder="群组口令。自由大厅口令new，其他群组口令见介绍或社群通知。">
                    <button type="submit">加入群组</button>
                </form>
            <?php endif; ?>
        </div>
        <div style="flex: 1;<?php if($page_mode!=2) echo ' display: none;';?>">
            <h3>热门群组</h3>
            <ul>
                <?php foreach ($hot_groups as $group): ?>
                    <li><?php echo htmlspecialchars($group['group_name']); ?> (热度：<?php echo $group['hot_score']; ?>)<br>
                        <span><?php echo htmlspecialchars($group['description']);?></span>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
</div>

<script type="text/javascript">
    function toggleDisplay(elementId) {
        var element = document.getElementById(elementId);
        if (element.style.display === "none" || element.style.display === "") {
            element.style.display = "block";
        } else {
            element.style.display = "none";
        }
    }
</script>
</body>
</html>