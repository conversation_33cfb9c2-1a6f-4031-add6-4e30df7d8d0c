<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
$result = $conn->select("model", "*", ["isvalid" => 1, "ORDER" => ["isimage", "sequenceid", "id"]]);
$modelArray = array();
$modeltypearray = array();
foreach ($result as $row) {
    $modelArray[] = array('modelname' => $row['modelname'], 'id' => $row['id'], 'modeltype' => $row['modeltype'], 'modelvalue' => $row['modelvalue'], 'isimage' => $row['isimage']);
    $modeltypearray[$row["id"]] = array('modeltype' => $row['modeltype'], 'modelname' => $row['modelname'], 'modelvalue' => $row['modelvalue']);
}

$row = $conn->get('main', '*', ['id' => 1]);
$apiaddress = $row["apiaddress"];
$gpt4apiaddress = $row["gpt4apiaddress"];
$proxyaddress = $row["proxyaddress"];
if ((isset($_POST["action"])) && ($_POST["action"] == "addnew")) {
    $keyarray = explode("\n", $_POST["apikey"]);
    $i = 0;
    while ($i < count($keyarray)) {
        if (trim($keyarray[$i]) !== "") {
            $conn->insert('apikey', ['keytype' => $_POST["keytype"], 'apikey' => trim($keyarray[$i]), 'apiaddress' => $_POST["apiaddress"], 'isvalid' => 1, 'createtime' => date('Y-m-d H:i:s'), 'memo' => $_POST["memo"]]);
        }
        $i++;
    }
    echo "<html><head><meta charset=utf-8></head><body><script>alert('添加成功！');parent.location.href='apikeyconfig.php';</script>";
    exit;
} elseif ((isset($_GET["action"])) && ($_GET["action"] == "check")) {
    $result = $conn->select("apikey", "*", ["ORDER" => "id"]);
    foreach ($result as $row) {
        if ($modeltypearray[$row["keytype"]]["modeltype"] == "openai") {
            $headers = [
                'Accept: application/json',
                'Content-Type: application/json',
                'Authorization: Bearer ' . $row["apikey"]
            ];

            if (strpos($modeltypearray[$row["keytype"]]["modelvalue"], "gpt-4") === false) {
                $newapiaddress = $apiaddress;
            } else {
                $newapiaddress = $gpt4apiaddress;
            }
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
            if (strpos($modeltypearray[$row["keytype"]]["modelvalue"], "image") === false) {
                curl_setopt($ch, CURLOPT_URL, $newapiaddress . '/v1/models/' . $modeltypearray[$row["keytype"]]["modelvalue"]);
            } else {
                curl_setopt($ch, CURLOPT_URL, $newapiaddress . '/v1/models/gpt-3.5-turbo');
            }
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            if (!empty($proxyaddress)) {
                curl_setopt($ch, CURLOPT_PROXY, $proxyaddress);
            }
            $response = curl_exec($ch);
            echo $response;
            curl_close($ch);
            $complete = json_decode($response);
            if (isset($complete->error)) {
                $conn->update('apikey', ['isvalid' => 0, 'checktime' => date('Y-m-d H:i:s'), 'errmsg' => addslashes(trim($complete->error->message))], ['id' => $row["id"]]);
            } else {
                $next_day = date('Y-m-d', strtotime('+1 day'));
                $prev_day = date('Y-m-d', strtotime('-90 days'));
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
                curl_setopt($ch, CURLOPT_URL, $newapiaddress . '/v1/dashboard/billing/subscription');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                if (!empty($proxyaddress)) {
                    curl_setopt($ch, CURLOPT_PROXY, $proxyaddress);
                }
                $response = curl_exec($ch);
                $complete = json_decode($response);
                $total = $complete->hard_limit_usd;
                curl_close($ch);
                $ch = curl_init();
                $url = $newapiaddress . "/v1/dashboard/billing/usage?start_date=" . $prev_day . "&end_date=" . $next_day;
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                if (!empty($proxyaddress)) {
                    curl_setopt($ch, CURLOPT_PROXY, $proxyaddress);
                }
                $response = curl_exec($ch);
                $complete = json_decode($response);
                $used = ($complete->total_usage / 100);
                curl_close($ch);
                $left = $total - $used;
                $conn->update('apikey', ['isvalid' => 1, 'checktime' => date('Y-m-d H:i:s'), 'credit' => number_format($total, 2, '.', ''), 'remain' => number_format($left, 2, '.', '')], ['id' => $row["id"]]);
            }
        }
    }
    echo "<html><head><meta charset=utf-8></head><body><script>parent.location.href='apikeyconfig.php';</script>";

    exit;
} elseif ((isset($_GET["action"])) && ($_GET["action"] == "delete")) {
    $result = $conn->get('apikey', '*', ['id' => $_GET['id']]);
    if ($result) {
        $conn->delete('apikey', ['id' => $_GET['id']]);
        echo "<html><head><meta charset=utf-8></head><body><script>alert('删除成功！');parent.location.href='apikeyconfig.php';</script>";
    } else {
        echo "<html><head><meta charset=utf-8></head><body><script>alert('API_KEY不存在！');parent.location.reload();</script>";
    }
    exit;
} elseif ((isset($_POST["action"])) && ($_POST["action"] == "update")) {
    $result = $conn->get('apikey', '*', ['id' => $_POST['id']]);
    if ($result) {
        $conn->update('apikey', ['apikey' => $_POST['apikey'], 'apiaddress' => $_POST['apiaddress'], 'memo' => $_POST['memo']], ['id' => $_POST['id']]);
        echo "<html><head><meta charset=utf-8></head><body><script>parent.document.getElementById('onthego').style.display='none';</script></body></html>";
    } else {
        echo '<html><head><meta charset=utf-8></head><body><script>alert("APIKEY不存在！");parent.location.reload();</script>';
    }
    exit;
} elseif ((isset($_GET["action"])) && ($_GET["action"] == "setvalid")) {
    $result = $conn->get('apikey', '*', ['id' => $_GET['id']]);
    if ($result) {
        $conn->update('apikey', ['isvalid' => 1], ['id' => $_GET['id']]);
        echo "<html><head><meta charset=utf-8></head><body><script>alert('激活成功！');parent.location.href='apikeyconfig.php';</script>";
    } else {
        echo "<html><head><meta charset=utf-8></head><body><script>alert('API_KEY不存在！');parent.location.reload();</script>";
    }
    exit;
}
?>
<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>API_KEY配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />


    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        form {
            margin: 0px;
            display: inline
        }

        select {
            vertical-align: bottom;
            padding: 1px 4px;
            height: 23px;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }

        div.mycollapse {
            overflow: hidden;
            white-space: normal;
            text-overflow: ellipsis;
            width: 100%;
            height: 40px;
        }

        div.myexpand {
            white-space: normal;
            width: 100%;
            height: auto;
        }

        table th {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: white;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑';overflow-x: auto">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) { }
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">API_KEY配置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px;">
            <div class="row">
                <div class="space-6"></div>
                <div class="col-xs-12">
                    <div class="mycollapse" id="info"
                        style="border-radius: 10px;padding:10px 20px;line-height:20px;margin-bottom:20px;border:1px solid silver;">
                        <button onclick="toggleCollapse(this)"
                            style="float:right;font-weight:bold;padding:0 10px;margin-top:-3px;">点击展开API_KEY配置说明</button>
                        <p>每个API_KEY单独配置API地址，请注意API地址最后不要加“/”，目前API_KEY和API地址都已支持修改。</p>
                        <p>对于兼容OpenAI标准的接口，只写域名部分即可，不要写后面的/v1等。MJ画图和SD画图的环境搭建和配置方法请 <a target="_blank"
                                href="https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=81">参考这里</a>。</p>
                        <p>国内大模型目前基本都提供免费试用额度，可以 <a target="_blank"
                                href="https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=116">点击这里</a> 了解和申请。</p>
                        <p>所有已支持的模型配置API_KEY时将自动修改API地址，一般情况下无需修改即可使用。</p>
                        <p>微软Azure类型的模型需要修改下API地址，请修改为您自己的resource-url和模型名称。</p>
                        <p>百度文心千帆模型请将应用的API Key和Secret
                            Key用逗号分隔组成字符串，即为API_KEY。如：Fq1FfkOQIGtMtZqRFxzrvz6T,cbzsoFAUSl8UGV1GkGSCSjENtqsjrOTd。用户使用百度文心千帆提问后，API_KEY会增加一项access_token，并用逗号分隔合并到原API_KEY。
                        </p>
                        <p>百度文心千帆因为对话大模型种类很多，并且每个接口地址都不同，因此需要自行设置每个模型的接口地址。请 <a target="_blank"
                                href="https://cloud.baidu.com/doc/WENXINWORKSHOP/s/clntwmv7t#http%E8%B0%83%E7%94%A8">访问这里</a>
                            查看每个模型的接口地址，在左侧选择对应的模型，再点击右侧的“HTTP调用”，然后复制“请求地址”即可。
                        </p>
                        <p>讯飞星火大模型请将讯飞开放平台应用的APPID、APISecret和APIKey用逗号分隔组成字符串，即为API_KEY。如：adz9zf8b,MTdzZjg0N2QwY2ZmZzZjNGYyMjzjNjzw,e522c8936z3fec69f6dfzd32azaa40z5。
                        </p>
                        <p>讯飞星火大模型API地址设置规则：Spark Lite（wss://spark-api.xf-yun.com/v1.1/chat）、Spark
                            Pro（wss://spark-api.xf-yun.com/v3.1/chat）、Spark
                            Max（wss://spark-api.xf-yun.com/v3.5/chat）、图片理解（wss://spark-api.cn-huabei-1.xf-yun.com/v2.1/image）、图片生成（https://spark-api.xf-yun.com/v2.1/tti）。
                        </p>
                        <p>腾讯混元大模型，请先到腾讯云 <a target="_blank" href="https://console.cloud.tencent.com/cam/capi">访问密钥</a>
                            栏目申请API密钥，将SecretId和SecretKey用逗号分隔组成字符串，即为API_KEY。</p>
                        <p>MiniMax仅支持ChatCompletion
                            Pro接口，可以使用abab6.5s-chat、abab6.5t-chat、abab6.5g-chat、abab5.5-chat、abab5.5s-chat模型。注意要在默认的api地址最后填写自己的GroupId。
                        </p>
                        <p>本地知识库目前不需要验证APIKEY，可以随便填写。</p>
                        <p>如果您的服务器在国内，则访问国外大模型官方接口时需要使用反代地址，第三方接口提供的地址一般都可以在国内直接访问。</p>
                        <p>如果您的服务器在国外，则访问以下国内大模型接口时最好也用国内服务器反代一下，否则可能会出现卡顿或无法访问的情况：讯飞星火、360智脑、火山方舟</p>
                        <p>对于已失效的API_KEY，可以单击红色的“否”查看错误提示，鼠标右键点击可以重新激活该API_KEY。</p>
                    </div>
                    <button class="btn btn-sm btn-info" style="padding:2px 10px;margin-bottom:10px;"
                        onclick="$('#addnew').show();">添加API_KEY</button>
                    <div style="margin:20px 0;display:none;" id="addnew">
                        <form method=post name=addtype target="temp" onsubmit="return checkform();">
                            <input name="action" value="addnew" type=hidden>
                            <table>
                                <tr>
                                    <td style="text-align:right;padding:5px;">
                                        <p>模型名称：</p>
                                    </td>
                                    <td style="padding:5px 0;">
                                        <p><select name="keytype" onchange="changemodeltype(this.value);"
                                                class="bg-focus">
                                                <?php
                                                foreach ($modelArray as $model) {
                                                    echo '<option value="' . $model['id'] . '">' . (($model['isimage']) ? "[图像]" : "[文本]") . $model['modelname'] . '</option>';
                                                }
                                                ?>
                                            </select><input type=hidden name=modeltype><input type=hidden
                                                name=modelvalue></p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:right;padding:5px;">
                                        <p>API_KEY：</p>
                                    </td>
                                    <td style="padding:5px 0;">
                                        <p><textarea placeholder="请用回车分隔多个API_KEY" style="width:500px;height:100px;"
                                                name="apikey" class="bg-focus"></textarea></p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:right;vertical-align:top;padding:5px;">
                                        <p>API地址：</p>
                                    </td>
                                    <td style="padding:5px 0;">
                                        <p><input name=apiaddress class="bg-focus" autoComplete="off"
                                                value="https://api.openai.com" style="width:500px;">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:right;padding:5px;">
                                        <p>备注：</p>
                                    </td>
                                    <td style="padding:5px 0;">
                                        <p><input name=memo class="bg-focus" autoComplete="off" style="width:500px;">
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan=2 style="text-align:center">
                                        <button type=submit class="btn-primary" style="width:80px;">确认添加</button>
                                    </td>
                                </tr>
                            </table>
                        </form>
                    </div>
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th class="text-center" style="width:60px;">序号</th>
                                <th class="text-center" style="width:60px;">有效</th>
                                <th class="text-center" style="width:200px;">模型名称</th>
                                <th class="text-center" style="width:500px;">API_KEY</th>
                                <th class="text-center" style="width:260px;">API地址</th>
                                <th class="text-center" style="width:160px;">创建时间</th>
                                <th class="text-center">备注</th>
                                <th class="text-center" style="width:60px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $sql = "SELECT apikey.* FROM apikey JOIN model ON (apikey.keytype = model.id and model.isvalid) ORDER BY model.isimage,model.sequenceid";
                            $sqlcount = "select count(t.id) from (" . $sql . ") t";
                            $result = $conn->query($sqlcount);
                            $row = $result->fetch();
                            $totalnumber = $row[0];
                            if ($totalnumber == 0) {
                                echo "<tr><td colspan=11>暂未设置API_KEY。</td></tr>";
                            } else {
                                $count = 0;
                                $result = $conn->query($sql);
                                while ($row = $result->fetch()) {
                                    $count++;
                                    ?>
                                    <tr>
                                        <form id="form<?php echo $row["id"] ?>" method=post target="temp"
                                            action="apikeyconfig.php" onsubmit="checkandsubmit('<?php echo $row["id"] ?>');">
                                            <input type=hidden name=id value="<?php echo $row["id"] ?>"><input type=hidden
                                                name=action value=update>
                                            <td class="text-center"><?php echo $count ?></td>
                                            <td class="text-center" <?php echo ($row["isvalid"]) ? "" : "style='color:red;font-weight:bold;cursor:pointer;' title='单击查看错误消息，鼠标右键点击重新激活' onclick='alert(decodeURIComponent(\"" . rawurlencode(str_replace("+", " ", $row["errmsg"])) . "\"));' oncontextmenu='event.preventDefault();setvalid(" . $row["id"] . ");'"; ?>><?php echo ($row["isvalid"]) ? "是" : "否" ?></td>
                                            <td class="text-center"><?php echo $modeltypearray[$row["keytype"]]["modelname"] ?>
                                            </td>
                                            <td class="text-center"><input name=apikey style="width:100%;"
                                                    value="<?php echo $row["apikey"] ?>"
                                                    onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><input name=apiaddress style="width:100%;"
                                                    value="<?php echo $row["apiaddress"] ?>"
                                                    onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><?php echo $row["createtime"] ?></td>
                                            <td class="text-center"><input name=memo style="width:100%;"
                                                    value="<?php echo $row["memo"] ?>"
                                                    onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><input type=button style="width:45px;"
                                                    onclick='deleteid(<?php echo $row["id"] ?>);' value="删除"></td>
                                        </form>
                                    </tr>
                                    <?php
                                }
                            }

                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function checkandsubmit(boxid) {
            var newid = "form" + boxid;
            document.getElementById('onthego').style.display = 'block';
            document.getElementById(newid).submit();
        }

        function checkapikey() {
            var loading = layer.msg('验证中，这需要一些时间，请稍候...', {
                icon: 16,
                shade: 0.4,
                time: false
            });
            document.getElementById("result").src = "apikeyconfig.php?action=check";
        }

        function deleteid(id) {
            if (confirm("确认删除该API_KEY吗？")) {
                document.getElementById("result").src = "apikeyconfig.php?action=delete&id=" + id;
            }
        }

        function setvalid(id) {
            if (confirm("确认重新激活该API_KEY吗？")) {
                document.getElementById("result").src = "apikeyconfig.php?action=setvalid&id=" + id;
            }
        }

        function checkform() {
            if (window.addtype.apikey.value == "") {
                alert("API_KEY不能为空！");
                return false;
            } else {
                return true;
            }
        }

        function changemodeltype(id) {
            <?php
            foreach ($modelArray as $model) {
                echo 'if (id == ' . $model['id'] . ') {window.addtype.modeltype.value = "' . $model['modeltype'] . '";window.addtype.modelvalue.value = "' . $model['modelvalue'] . '";}' . "\n";
            }
            ?>
            if (window.addtype.modeltype.value == "midjourney") {
                window.addtype.apiaddress.value = "http://mj.10ns.cn/api/mj-fast/mj";
            } else if (window.addtype.modeltype.value == "chimeragpt") {
                window.addtype.apiaddress.value = "https://api.naga.ac";
            } else if (window.addtype.modeltype.value == "stablediffusion") {
                window.addtype.apiaddress.value = "https://api.ipfei.com/sd";
            } else if (window.addtype.modeltype.value == "文心千帆") {
                window.addtype.apiaddress.value = "请参考页面上方的配置说明";
            } else if ((window.addtype.modeltype.value == "通义千问") || (window.addtype.modeltype.value == "alistablediffusion")) {
                window.addtype.apiaddress.value = "https://dashscope.aliyuncs.com";
            } else if (window.addtype.modeltype.value == "腾讯混元") {
                window.addtype.apiaddress.value = "https://hunyuan.tencentcloudapi.com";
            } else if (window.addtype.modeltype.value == "清华智谱") {
                window.addtype.apiaddress.value = "https://open.bigmodel.cn";
            } else if (window.addtype.modeltype.value == "讯飞星火") {
                window.addtype.apiaddress.value = "请参考页面上方的配置说明";
            } else if (window.addtype.modeltype.value == "360智脑") {
                window.addtype.apiaddress.value = "https://api.360.cn";
            } else if (window.addtype.modeltype.value == "百川智能") {
                window.addtype.apiaddress.value = "https://api.baichuan-ai.com";
            } else if (window.addtype.modeltype.value == "月之暗面") {
                window.addtype.apiaddress.value = "https://api.moonshot.cn";
            } else if (window.addtype.modeltype.value == "火山方舟") {
                window.addtype.apiaddress.value = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";
            } else if (window.addtype.modeltype.value == "MiniMax") {
                window.addtype.apiaddress.value = "https://api.minimax.chat/v1/text/chatcompletion_pro?GroupId=";
            } else if (window.addtype.modeltype.value == "零一万物") {
                window.addtype.apiaddress.value = "https://api.lingyiwanwu.com";
            } else if (window.addtype.modeltype.value == "DeepSeek") {
                window.addtype.apiaddress.value = "https://api.deepseek.com/chat/completions";
            } else if (window.addtype.modeltype.value == "无问芯穹") {
                window.addtype.apiaddress.value = "https://cloud.infini-ai.com/maas/" + window.addtype.modelvalue.value + "/nvidia/chat/completions";
            } else if (window.addtype.modeltype.value == "硅基流动") {
                window.addtype.apiaddress.value = "https://api.siliconflow.cn";
            } else if (window.addtype.modeltype.value == "LinkAI") {
                window.addtype.apiaddress.value = "https://api.link-ai.chat";
            } else if (window.addtype.modeltype.value == "本地知识库") {
                window.addtype.apiaddress.value = window.location.protocol + "//" + window.location.hostname + "/assistantapi.php";
            } else if (window.addtype.modeltype.value == "bard") {
                window.addtype.apiaddress.value = "https://generativelanguage.googleapis.com";
            } else if (window.addtype.modeltype.value == "claude") {
                window.addtype.apiaddress.value = "https://api.anthropic.com";
            } else if (window.addtype.modeltype.value == "azure") {
                window.addtype.apiaddress.value = "https://resource-url.openai.azure.com/openai/deployments/gpt-35-turbo-16k/chat/completions?api-version=2023-05-15";
            } else {
                window.addtype.apiaddress.value = "https://api.openai.com";
            }
        }
        changemodeltype('<?php echo $modelArray[0]['id']; ?>');

        function toggleCollapse(elem) {
            var myDiv = document.getElementById("info");
            if (myDiv.className == "mycollapse") {
                myDiv.className = "myexpand";
                elem.innerText = "点击收起API_KEY配置说明";
            } else {
                myDiv.className = "mycollapse";
                elem.innerText = "点击展开API_KEY配置说明";
            }
        }
    </script>

    <iframe style="display:none;" id=result name=temp></iframe>
</body>

</html>