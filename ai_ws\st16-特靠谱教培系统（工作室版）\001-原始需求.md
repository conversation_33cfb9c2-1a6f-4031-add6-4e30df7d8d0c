## 目标

一个课外补习班，几个老师，几个教室，若单学生，需要课表和请假功能。用php7.4+mysql 5.1开发一个支持电脑和手机访问的，最好能支持微信登陆和绑定用户，用户可以在小程序或web系统上查看课表和请假，老师可以在小程序或web系统上查看学生的请假申请和审批。


## 技术实现方案
对于PHP7.4+MySQL5.1的开发方案：

### 系统架构
前端：Bootstrap + JavaScript (响应式设计)

后端：PHP 7.4+ (使用MVC模式)

数据库：MySQL 5.1+

微信登录：使用微信开放平台API

### 核心功能模块
用户管理：教师、学生账户系统，支持微信绑定

课程管理：课程创建、编辑、删除，安排教师和教室

课表系统：可视化课表展示，支持周视图和日视图

请假系统：学生提交申请，教师审批，自动更新课表状态

教室管理：教室资源分配和冲突检测

### 微信登录集成
使用微信开放平台OAuth2.0授权流程。具体参考网站 web 目录在加载扩展  `web\diy\end\ks1cck2_init.php` `web\ks1cck2\login.php`

获取用户基本信息，与系统账户绑定

支持微信内直接访问和外部浏览器访问