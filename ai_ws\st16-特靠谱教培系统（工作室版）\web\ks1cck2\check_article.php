<?php
require '_ks1.php';
if (!isset($_SESSION[SESSION_KEY]['user']['id']) || !in_array($_SESSION[SESSION_KEY]['user']['role'], ['user', 'admin'])) {
    header('Location: login.php');
    exit;
}


$user_id = $_SESSION[SESSION_KEY]['user']['id'];
$role = $_SESSION[SESSION_KEY]['user']['role']??null;
$username = $_SESSION[SESSION_KEY]['user']['username']??null;

$pub_name=get_public_account_name($user_id);

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $article_id = $_POST['article_id'];
    $read_count = $_POST['read_count'];

    //$tmp_pass_time=$_SESSION['tmp_pass_time']??null;
    $tmp_pass_time=$_SESSION['tmp_pass_time']??0;
    //var_dump($tmp_pass_time);
    if($tmp_pass_time<=1){
        $tmp3= '请先完成文章阅读!';
        echo get_err_msg_div($tmp3);
    }else
    if($tmp_pass_time >=1 && $tmp_pass_time <30){
        $tmp3=read_time_tips(2,$tmp_pass_time);
        echo get_err_msg_div($tmp3);
    }else
    if (record_check($user_id, $article_id, $read_count,$tmp_pass_time)) {
        echo "Check recorded successfully.";
    } else {
        echo "Failed to record check.";
    }
}

$url='dashboard.php';
if(isset($_SESSION['temp_group_mode'])  && $_SESSION['temp_group_mode']=='1'){
    $url='enter_group.php';
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>提交阅读记录</title>
</head>
<body>

<p>请稍候，<span id="totalSecond" style="color:red">2</span>秒后自动跳转…</p>

<p><a href="<?php echo $url;?> target="_self">立即跳转</p>

<script language="javascript" type="text/javascript">
    var second = document.getElementById('totalSecond').textContent;

    if (navigator.appName.indexOf("Explorer") > -1) {
        second = document.getElementById('totalSecond').innerText;
    } else {
        second = document.getElementById('totalSecond').textContent;
    }

    setInterval("redirect()", 1000);

    function redirect() {
        if (second < 0) {
            location.href = '<?php echo $url;?>';
        } else {
            if (navigator.appName.indexOf("Explorer") > -1) {
                document.getElementById('totalSecond').innerText = second--;
            } else {
                document.getElementById('totalSecond').textContent = second--;
            }
        }
    }
</script>