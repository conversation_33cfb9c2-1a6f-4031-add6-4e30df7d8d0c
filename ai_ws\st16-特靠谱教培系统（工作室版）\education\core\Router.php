<?php
namespace Core;

/**
 * 路由器类
 */
class Router
{
    private $routes = [];
    
    /**
     * 构造函数
     */
    public function __construct()
    {
    }
    
    /**
     * 添加路由
     * 
     * @param string $method 请求方法
     * @param string $path 路径
     * @param array $handler 处理器 [控制器, 方法]
     */
    public function addRoute($method, $path, $handler)
    {
        $this->routes[] = [
            'method' => strtoupper($method),
            'path' => $path,
            'handler' => $handler
        ];
    }
    
    /**
     * 分发请求
     * 
     * @param Request $request 请求对象
     */
    public function dispatch($request)
    {
        $method = $request->getMethod();
        $path = $request->getPath();
        
        foreach ($this->routes as $route) {
            if ($route['method'] !== $method && $route['method'] !== 'ANY') {
                continue;
            }
            
            // 简单路由匹配
            if ($route['path'] === $path) {
                $this->executeHandler($route['handler'], $request);
                return;
            }
            
            // 带参数的路由匹配
            $pattern = $this->convertRouteToRegex($route['path']);
            if (preg_match($pattern, $path, $matches)) {
                array_shift($matches); // 移除完整匹配
                $this->executeHandler($route['handler'], $request, $matches);
                return;
            }
        }
        
        // 未找到路由
        $this->notFound();
    }
    
    /**
     * 将路由转换为正则表达式
     * 
     * @param string $route 路由
     * @return string 正则表达式
     */
    private function convertRouteToRegex($route)
    {
        $pattern = preg_replace('/\{([a-zA-Z0-9_]+)\}/', '([^/]+)', $route);
        return '#^' . $pattern . '$#';
    }
    
    /**
     * 执行处理器
     * 
     * @param array $handler 处理器
     * @param Request $request 请求对象
     * @param array $params 参数
     */
    private function executeHandler($handler, $request, $params = [])
    {
        list($controllerName, $methodName) = $handler;
        
        // 添加命名空间前缀
        $controllerClass = '\\Controllers\\' . $controllerName;
        
        if (!class_exists($controllerClass)) {
            $this->notFound();
            return;
        }
        
        $controller = new $controllerClass();
        
        if (!method_exists($controller, $methodName)) {
            $this->notFound();
            return;
        }
        
        // 调用控制器方法
        call_user_func_array([$controller, $methodName], array_merge([$request], $params));
    }
    
    /**
     * 404 页面
     */
    private function notFound()
    {
        header('HTTP/1.1 404 Not Found');
        echo '<h1>404 Not Found</h1>';
        echo '<p>The page you requested was not found.</p>';
        exit;
    }
}