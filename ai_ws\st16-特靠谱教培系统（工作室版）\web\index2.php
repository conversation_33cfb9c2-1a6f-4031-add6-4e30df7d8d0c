<?php
$beginfiles = glob('diy/begin/*.php');
foreach ($beginfiles as $file) {
  require_once $file;
}
require_once('admin/mysqlconn.php');
$sql = "select * from main where id=1";
$result = $conn->query($sql);
$row = $result->fetch();
$companyname = $row["companyname"];
$websitename = $row["websitename"];
$headlogo = $row["headlogo"];
$contentlogo = $row["contentlogo"];
$paytype = $row["payment_type"];
$payurl =  $row["payment_url"];
$isquestionsensor = $row["isquestionsensor"];
$isanswersensor = $row["isanswersensor"];
$isquestionfilter = $row["isquestionfilter"];
$isanswerfilter = $row["isanswerfilter"];
$iswebsiteinfo = $row["iswebsiteinfo"];
$websiteinfotitle = $row["websiteinfotitle"];
$websiteinfocontent = $row["websiteinfocontent"];
$isweixinlogin = $row["isweixinlogin"];
$isweixinregister = $row["isweixinregister"];
$isthirdpartylogin = $row["isthirdpartylogin"];
$iswindowlogin = $row["iswindowlogin"];
$thirdpartyloginurl = $row["thirdpartyloginurl"];
$freetryshare = $row["freetryshare"];

if (strpos($paytype, 'weixin') !== false) {
  $isweixinpay = true;
} else {
  $isweixinpay = false;
}
if (strpos($paytype, 'alipay') !== false) {
  $isalipay = true;
} else {
  $isalipay = false;
}
if (strpos($paytype, 'shop') !== false) {
  $isshoppay = true;
} else {
  $isshoppay = false;
}

$rechargetext = '<div id="buypanel"><div style="height: 80%; display: flex; flex-direction: column; justify-content: center; align-items: center;">';
$sql = "select * from cardtype where not ishidden order by id";
$result = $conn->query($sql);
while ($row = $result->fetch()) {
  $rechargetext .= '<div class="product" productid="' . $row["id"] . '"><div class="product-name">' . $row["cardname"] . '</div><div class="product-price">' . $row["price"] . ' 元</div><div class="product-limit">' . $row["quota"] . ' 次</div><div class="product-days">' . $row["extenddays"] . ' 天</div></div>';
}
$rechargetext .= '</div><div style="height: 20%; display: flex; justify-content: center; align-items: center;"><input type=hidden id="productid">';
if ($isweixinpay) {
  $rechargetext .= '<button onclick="showwxpayqrcode();" style="background-color: #007bff; color: #fff; border: none; padding: 5px 10px; font-size: 16px; cursor: pointer; margin:10px;">微信支付</button>';
}
if ($isalipay) {
  $rechargetext .= '<button onclick="showalipayqrcode();" style="background-color: #007bff; color: #fff; border: none; padding: 5px 10px; font-size: 16px; cursor: pointer; margin:10px;">支付宝</button>';
}
if ($isshoppay) {
  $rechargetext .= '<button onclick="gotoshop();" style="background-color: #007bff; color: #fff; border: none; padding: 5px 10px; font-size: 16px; cursor: pointer; margin:10px;">购卡/卡密充值</button>';
}
$rechargetext .= '</div></div>';

$sql = "select * from model where isvalid order by id";
$result = $conn->query($sql);
$modelArray = array();
while ($row = $result->fetch()) {
  $modelArray[] = array('modelname' => $row['modelname'], 'modelid' => $row['id'], 'modelprice' => $row['modelprice']);
}
$sql = "select * from model where isvalid order by id";
$result = $conn->query($sql);
$row = $result->fetch();
$defaultmodelid = $row["id"];
$sql = "select * from role where isvalid order by id";
$result = $conn->query($sql);
$roleArray = array();
while ($row = $result->fetch()) {
  $roleArray[] = array('rolename' => $row['rolename'], 'rolevalue' => $row['rolevalue']);
}
$sql = "select * from scene where isvalid order by id";
$result = $conn->query($sql);
$sceneArray = array();
while ($row = $result->fetch()) {
  $sceneArray[] = array('scenename' => $row['scenename'], 'scenevalue' => $row['scenevalue']);
}

?>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <link rel="icon" type="image/png" href="./favicon.ico">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title><?php echo $websitename; ?></title>
  <link rel="stylesheet" href="css/hightlight.css">
  <link rel="stylesheet" href="bootstrap/css/bootstrap.min.css">
  <link rel="stylesheet" href="css/font-awesome.css">
  <link rel="stylesheet" href="css/layui.css">
  <link rel="stylesheet" href="css/test.css?v=2.1.2325">
  <?php
  $headfiles = glob('diy/head/*.php');
  foreach ($headfiles as $file) {
    require_once $file;
  }
  ?>
</head>

<body class="bg-white dark:bg-gray-800" style="min-height: 100vh;">
  <div class="marquee">
    <span onclick='showshare();' style='cursor:pointer;'>邀请好友使用送双方查询次数，点击查看详情</span>
  </div>

  <nav class="fixed top-0 left-0 right-0 border-b z-20 bg-white shadow dark:bg-gray-800 dark:border-b-gray-900">
    <div class="container py-3 px-4 sm:px-6 lg:flex lg:justify-between lg:items-center" style="margin-left: auto;margin-right: auto;max-width: 72rem; display: flex; justify-content: space-between; align-items: center;">

      <div style="display: flex; align-items: center;">
        <img class="w-auto h-10 sm:h-12 mr-3" src="<?php echo $headlogo; ?>" alt="AI Chat">
        <span class="font-mono" style='font-family: " Microsoft YaHei", sans-serif !important;font-weight: 700;font-size:
                1.5rem;line-height: 2rem'><?php echo $websitename; ?></span>
      </div>

      <div id="rightbanner" class="my-2 text-gray-700 transition-colors duration-300 transform dark:text-gray-200 md:mx-4 md:my-0">
        <span style="text-decoration:none;cursor:pointer;" onclick="showqrcode();">一键登录</span>
      </div>

    </div>

  </nav>
  <div class="card card-header show-history toggle" style="display: none;">
    历<br>史<br>对<br>话</div>
  <div class="cardlist card collapse history">

    <div class="hide-history" title="点击这里缩回左侧">
      <span style="float:left;margin:0 10px;">&lt;&lt;</span>
      <span style="float:right;margin:0 10px;">&lt;&lt;</span>
      收回对话窗口
    </div>
    <div id="conversation" style="overflow: auto;height:calc(100% - 78px);">
    </div>
    <div onclick="deleteallhistory();" style="font-size:20px;cursor:pointer;position:absolute;text-align: center;bottom:0;z-index:99999;background-color: #f8f8f8;width:100%;color:#ff5a5a;border:0;border-top:var(--bs-card-border-width) solid var(--bs-card-border-color) !important;">
      清空所有对话
    </div>
  </div>

  <div id="main" class="relative py-20 dark:bg-gray-800" style="padding-top: 6rem; padding-bottom: 6rem;max-width: 64rem;margin-left: auto;margin-right: auto">
    <div id="userpanel" class="fixed bg-white" style="width:100%;margin-left:1rem;max-width:1000px;padding-right: 1rem;height:50px;margin-top:-30;padding-top:20px;display: none;justify-content:center">
      <span class="px-4 sm:px-6 flex flex-row items-center justify-between" style="display: flex; justify-content: space-between; width: 100%;max-width:600px;">
        <span>剩余 <span style="color:darkblue;" id="quota"></span> 次</span><span><span style="color:darkblue;" id="expiretime"></span> 过期</span><span id="showlog" style="cursor:pointer;">全部对话日志</span>
      </span>
    </div>
    <div style="height:24px;"></div>

    <!-- 聊天框界面 -->
    <div class="dark:bg-gray-900" style="margin-top: 1rem;margin-bottom: 1rem;padding-left: 1rem;padding-right: 1rem;">

      <!-- 默认显示界面 -->
      <div id="PromptPage" style="height:calc(100vh - 250px);padding: 6rem 0;display: flex;">
        <img src="<?php echo $contentlogo; ?>" style="max-width:80%;margin:auto; opacity:0.5;align-items:center">
      </div>

      <!-- 互动界面 -->
      <div class="border dark:border-gray-900 rounded-md" id="ChatPage" style="display: none;margin-bottom:70px;">
      </div>

    </div>

    <div class="fixed bottom-0 left-0 right-0 py-3 bg-gray-100 border-t flex items-center justify-center dark:bg-gray-800 dark:border-t-gray-900">
      <div class="divpanel">
        <div class="divpanelbackground"></div>
        <div class="divnewchat" title="清空聊天框，开启新话题" onclick="clearchatpage();">
          新话题
        </div>
        <div class="divmodel">
          <select id="model">
            <option value="<?php echo $defaultmodelid ?>">模型</option>
            <?php
            foreach ($modelArray as $model) {
              echo '<option value="' . $model['modelid'] . '">' . $model['modelname'] . '（计费' . $model['modelprice'] . '次）</option>';
            }
            ?>
          </select>
        </div>
        <div class="divrole">
          <select id="role">
            <option value="">角色</option>
            <?php
            foreach ($roleArray as $role) {
              echo '<option value="' . $role['rolevalue'] . '">' . $role['rolename'] . '</option>';
            }
            ?>
          </select>
        </div>
        <div class="divscene">
          <select id="scene" onchange='$("#question").val($("#scene").val());'>
            <option value="">场景</option>
            <?php
            foreach ($sceneArray as $scene) {
              echo '<option value="' . $scene['scenevalue'] . '">' . $scene['scenename'] . '</option>';
            }
            ?>
          </select>
        </div>
      </div>
      <form class="flex-grow flex flex-row justify-center items-center pr-4 sm:pl-4 sm:pr-6" style="max-width: 64rem;padding-left: .5rem">
        <input type="text" id="question" class="px-2 text-gray-700 bg-white border rounded-md mr-2 sm:mr-4 dark:bg-gray-900 dark:text-gray-300 dark:border-gray-600 focus:border-blue-400 dark:focus:border-blue-300 focus:outline-none focus:ring focus:ring-blue-300 focus:ring-opacity-40 flex-grow" style="padding-top: .5rem;padding-bottom: .5rem" placeholder="请输入您的问题">
        <button type="button" id="go" style="padding-top: .5rem;padding-bottom: .5rem;font-weight: bold;font-size: 16px;text-transform: capitalize" class="px-4 text-sm tracking-wide text-white transition-colors duration-300 transform bg-blue-700 rounded-md hover:bg-blue-600 focus:outline-none focus:bg-blue-600 whitespace-nowrap" onclick="gogogo()"> 发送 </button>
      </form>
    </div>
  </div>
  <div id="mydialog" style="display:none;">
    <div id="mytitle" style="width: 300px; height: 50px;background-color: rgb(79, 128, 225);color: #fff;font-size: 16px;line-height: 50px;text-align: center;"></div>
    <div id="mycontent" style="width: 300px; height: 300px;padding: 20px;text-align: center;"></div>
  </div>

  <div id="notice" style="display:none;position: relative;height: 100%;">
    <div style="height: calc(100% - 50px);overflow: auto;padding:20px;"><?php echo $websiteinfocontent ?></div>
    <div style="position: absolute;bottom: 0;height: 50px;padding:5px;width: 100%;text-align:center"><button onclick="layer.closeAll();" style="background-color: #007bff; color: #fff; border: none; padding: 5px 10px; font-size: 16px; cursor: pointer;">好的</button></div>
  </div>
  <script src="js/jquery.min.js"></script>
  <script src="js/layui.js"></script>
  <script src="js/highlight.min.js"></script>
  <script src="js/showdown.min.js"></script>
  <script src="bootstrap/js/bootstrap.min.js"></script>
  <script src="js/jquery.qrcode.min.js"></script>
  <script src="js/crypto-js.min.js"></script>
  <script src="js/aes.min.js"></script>
  <script src="js/jquery.cookie.min.js"></script>
  <script src="js/remarkable.js"></script>
  <script src="js/chat.js?v=2.1.2325"></script>
  <script>
    function buy() {
      buylayer = layer.open({
        type: 1,
        title: '充值',
        area: ['350px', '400px'],
        shade: 0.5,
        scrollbar: true,
        offset: ['calc(50% - 200px)', 'calc(50% - 175px)'],
        fixed: true,
        content: '<?php echo $rechargetext; ?>'
      });
    }

    iswindowloginonly = false;
    isthirdpartyloginonly = false;

    thirdpartyloginurl = '<?php echo $thirdpartyloginurl ?>';
    isquestionsensor = <?php echo $isquestionsensor ?>;
    isanswersensor = <?php echo $isanswersensor ?>;
    isquestionfilter = <?php echo $isquestionfilter ?>;
    isanswerfilter = <?php echo $isanswerfilter ?>;
    var user_uid, user_email;
    <?php
    if (isset($_GET["i"])) {
      echo "$.cookie('sharefromuserid', '" . $_GET["i"] . "', {expires: 1, path: '/'});";
    }
    if ($iswebsiteinfo) {
    ?>
      layer.open({
        type: 1,
        title: '<div style="font-weight:bold;text-align:center"><?php echo $websiteinfotitle ?></div>',
        area: [(0.6 * $(document.body).width() > 380) ? '60%' : '380px', '60%'],
        offset: ['20%', (0.6 * $(document.body).width() > 380) ? '20%' : (Number(($(document.body).width() - 380) / 2) + 'px')],
        closeBtn: 1,
        content: $('#notice'),
        fixed: true,
        shadeClose: true
      });

    <?php
    }
    if (($isweixinlogin) && ($isthirdpartylogin)) {
    ?>
      logintitleright = '<div style="display: flex;cursor:pointer;"><div style="width: 100%; height: 50px;background-color: rgb(79, 128, 225);color: #fff;font-size: 16px;line-height: 50px;text-align: center;" onclick="showwxlogin();">微信扫码登录</div><div style="width: 100%; height: 50px;background-color: #fff;color: #000;font-size: 16px;line-height: 50px;text-align: center;"">邮箱密码登录</div></div>';
      <?php
      if ($iswindowlogin) {
      ?>
        logintitleleft = '<div style="display: flex;cursor:pointer;"><div style="width: 100%; height: 50px;background-color: #fff;color: #000;font-size: 16px;line-height: 50px;text-align: center;">微信扫码登录</div><div style="width: 100%; height: 50px;background-color: rgb(79, 128, 225);color: #fff;font-size: 16px;line-height: 50px;text-align: center;" onclick="showuserlogin();">邮箱密码登录</div></div>';
      <?php
      } else {
      ?>
        logintitleleft = '<div style="display: flex;cursor:pointer;"><div style="width: 100%; height: 50px;background-color: #fff;color: #000;font-size: 16px;line-height: 50px;text-align: center;">微信扫码登录</div><div style="width: 100%; height: 50px;background-color: rgb(79, 128, 225);color: #fff;font-size: 16px;line-height: 50px;text-align: center;" onclick="window.open(thirdpartyloginurl, \'_blank\');">第三方用户登录</div></div>';
      <?php
      }
    } elseif ($isweixinlogin) {
      ?>
      logintitleleft = '<div style="display: flex;"><div style="width: 100%; height: 50px;background-color: rgb(79, 128, 225);color: #fff;font-size: 16px;line-height: 50px;text-align: center;">微信扫码登录</div></div>';
      <?php
    } elseif ($isthirdpartylogin) {
      if ($iswindowlogin) {
      ?>
        iswindowloginonly = true;
        logintitleright = '<div style="display: flex;"><div style="width: 100%; height: 50px;background-color: rgb(79, 128, 225);color: #fff;font-size: 16px;line-height: 50px;text-align: center;">邮箱密码登录</div></div>';
      <?php
      } else {
      ?>
        isthirdpartyloginonly = true;
      <?php
      }
    }
    if ($freetryshare > 0) {
      ?>
      freetryshare = <?php echo $freetryshare ?>;
      $(function() {
        var marquee = $('.marquee');
        marquee.show();
        var span = marquee.find('span');
        var width = span.width();
        var duration = (width + marquee.width()) / 50;

        span.css('animation-duration', duration + 's');
        span.on('animationiteration webkitAnimationIteration', function() {
          $(this).css('left', '100%');
        });
      });


    <?php
    }
    ?>
  </script>
  <?php
  $endfiles = glob('diy/end/*.php');
  foreach ($endfiles as $file) {
    require_once $file;
  }
  ?>
</body>

</html>