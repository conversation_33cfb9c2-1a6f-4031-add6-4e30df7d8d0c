<?php
/**
 * 教室模型
 * 处理教室数据的操作
 */
class ClassroomModel extends Model {
    /**
     * 表名
     * @var string
     */
    protected $table = 'classrooms';
    
    /**
     * 根据ID获取教室信息
     * @param int $id 教室ID
     * @return array|null 教室信息
     */
    public function getClassroomById($id) {
        $sql = "SELECT * FROM {$this->table} WHERE id = ?";
        return $this->db->selectOne($sql, [$id]);
    }
    
    /**
     * 获取教室列表
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array 教室列表
     */
    public function getClassrooms($page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $sql = "SELECT * FROM {$this->table} ORDER BY id DESC LIMIT {$offset}, {$perPage}";
        return $this->db->select($sql);
    }
    
    /**
     * 获取所有教室
     * @return array 教室列表
     */
    public function getAllClassrooms() {
        $sql = "SELECT * FROM {$this->table} ORDER BY name ASC";
        return $this->db->select($sql);
    }
    
    /**
     * 获取教室总数
     * @return int 教室总数
     */
    public function getClassroomCount() {
        return $this->db->count("SELECT COUNT(*) FROM {$this->table}");
    }
    
    /**
     * 创建教室
     * @param array $data 教室数据
     * @return int 新教室的ID
     */
    public function createClassroom($data) {
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * 更新教室信息
     * @param int $id 教室ID
     * @param array $data 教室数据
     * @return bool 是否成功
     */
    public function updateClassroom($id, $data) {
        return $this->db->update($this->table, $data, "id = ?", [$id]);
    }
    
    /**
     * 删除教室
     * @param int $id 教室ID
     * @return bool 是否成功
     */
    public function deleteClassroom($id) {
        return $this->db->delete($this->table, "id = ?", [$id]);
    }
    
    /**
     * 搜索教室
     * @param string $keyword 关键词
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array 教室列表
     */
    public function searchClassrooms($keyword, $page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $keyword = "%{$keyword}%";
        $sql = "SELECT * FROM {$this->table} 
                WHERE name LIKE ? OR location LIKE ? OR facilities LIKE ? 
                ORDER BY id DESC 
                LIMIT {$offset}, {$perPage}";
        return $this->db->select($sql, [$keyword, $keyword, $keyword]);
    }
    
    /**
     * 获取搜索结果总数
     * @param string $keyword 关键词
     * @return int 结果总数
     */
    public function getSearchCount($keyword) {
        $keyword = "%{$keyword}%";
        $sql = "SELECT COUNT(*) FROM {$this->table} 
                WHERE name LIKE ? OR location LIKE ? OR facilities LIKE ?";
        return $this->db->count($sql, [$keyword, $keyword, $keyword]);
    }
    
    /**
     * 获取教室的课表
     * @param int $classroomId 教室ID
     * @return array 课表列表
     */
    public function getClassroomSchedules($classroomId) {
        $sql = "SELECT s.*, c.name as course_name, t.name as teacher_name 
                FROM schedules s 
                JOIN courses c ON s.course_id = c.id 
                JOIN teachers t ON c.teacher_id = t.id 
                JOIN users u ON t.user_id = u.id 
                WHERE s.classroom_id = ? 
                ORDER BY s.day_of_week, s.start_time";
        return $this->db->select($sql, [$classroomId]);
    }
    
    /**
     * 检查教室在指定时间是否可用
     * @param int $classroomId 教室ID
     * @param int $dayOfWeek 星期几 (0-6, 0表示星期日)
     * @param string $startTime 开始时间 (HH:MM:SS)
     * @param string $endTime 结束时间 (HH:MM:SS)
     * @param int $excludeScheduleId 排除的课表ID (用于编辑时)
     * @return bool 是否可用
     */
    public function isClassroomAvailable($classroomId, $dayOfWeek, $startTime, $endTime, $excludeScheduleId = 0) {
        $params = [$classroomId, $dayOfWeek, $startTime, $endTime, $startTime, $endTime];
        
        $excludeClause = '';
        if ($excludeScheduleId > 0) {
            $excludeClause = 'AND id != ?';
            $params[] = $excludeScheduleId;
        }
        
        $sql = "SELECT COUNT(*) FROM schedules 
                WHERE classroom_id = ? AND day_of_week = ? 
                AND (
                    (start_time <= ? AND end_time > ?) OR 
                    (start_time < ? AND end_time >= ?) OR 
                    (start_time >= ? AND end_time <= ?)
                ) 
                {$excludeClause}";
        
        // 添加最后两个参数
        $params[] = $startTime;
        $params[] = $endTime;
        
        return $this->db->count($sql, $params) == 0;
    }
    
    /**
     * 获取教室在指定时间的冲突课程
     * @param int $classroomId 教室ID
     * @param int $dayOfWeek 星期几 (0-6, 0表示星期日)
     * @param string $startTime 开始时间 (HH:MM:SS)
     * @param string $endTime 结束时间 (HH:MM:SS)
     * @param int $excludeScheduleId 排除的课表ID (用于编辑时)
     * @return array 冲突的课程列表
     */
    public function getClassroomConflicts($classroomId, $dayOfWeek, $startTime, $endTime, $excludeScheduleId = 0) {
        $params = [$classroomId, $dayOfWeek, $startTime, $endTime, $startTime, $endTime, $startTime, $endTime];
        
        $excludeClause = '';
        if ($excludeScheduleId > 0) {
            $excludeClause = 'AND s.id != ?';
            $params[] = $excludeScheduleId;
        }
        
        $sql = "SELECT s.*, c.name as course_name, u.name as teacher_name 
                FROM schedules s 
                JOIN courses c ON s.course_id = c.id 
                JOIN teachers t ON c.teacher_id = t.id 
                JOIN users u ON t.user_id = u.id 
                WHERE s.classroom_id = ? AND s.day_of_week = ? 
                AND (
                    (s.start_time <= ? AND s.end_time > ?) OR 
                    (s.start_time < ? AND s.end_time >= ?) OR 
                    (s.start_time >= ? AND s.end_time <= ?)
                ) 
                {$excludeClause} 
                ORDER BY s.start_time";
        
        return $this->db->select($sql, $params);
    }
    
    /**
     * 获取特定容量以上的教室
     * @param int $minCapacity 最小容量
     * @return array 教室列表
     */
    public function getClassroomsByCapacity($minCapacity) {
        $sql = "SELECT * FROM {$this->table} WHERE capacity >= ? ORDER BY capacity ASC";
        return $this->db->select($sql, [$minCapacity]);
    }
    
    /**
     * 获取特定位置的教室
     * @param string $location 位置
     * @return array 教室列表
     */
    public function getClassroomsByLocation($location) {
        $sql = "SELECT * FROM {$this->table} WHERE location = ? ORDER BY name ASC";
        return $this->db->select($sql, [$location]);
    }
    
    /**
     * 获取所有教室位置（去重）
     * @return array 位置列表
     */
    public function getAllLocations() {
        $sql = "SELECT DISTINCT location FROM {$this->table} ORDER BY location ASC";
        $results = $this->db->select($sql);
        
        $locations = [];
        foreach ($results as $row) {
            $locations[] = $row['location'];
        }
        
        return $locations;
    }
}