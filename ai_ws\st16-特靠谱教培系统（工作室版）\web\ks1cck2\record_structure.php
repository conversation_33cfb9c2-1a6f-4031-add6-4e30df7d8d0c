<?php
require '_ks1.php'; // 引入公共配置和函数
@session_start();

// 检查用户是否登录
if (!is_logged_in()) {
    header('Location: login.php');
    exit;
}

// 获取所有表名
$tables = [];
$result = get_data("SHOW TABLES");
//var_dump($result);die();

foreach ($result as $row) {
    //var_dump($row);//die();
    $tmptbl=implode('',$row);
    $tables[] = $tmptbl;    //$row[0];
}

// 获取每个表的结构
$tableStructures = [];
foreach ($tables as $table) {
    $structure = [];
    $result = get_data("DESCRIBE $table");
    foreach ($result as $row) {
        $structure[] = [
            'Field' => $row['Field'],
            'Type' => $row['Type'],
            'Null' => $row['Null'],
            'Key' => $row['Key'],
            'Default' => $row['Default'],
            'Extra' => $row['Extra']
        ];
    }
    $tableStructures[$table] = $structure;
}

// 将结构信息存储到 JSON 文件
file_put_contents('database_structure.json', json_encode($tableStructures, JSON_PRETTY_PRINT));

echo "数据库结构已记录到 database_structure.json 文件中。";
?>