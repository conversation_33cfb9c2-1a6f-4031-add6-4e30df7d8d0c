<?php
$beginfiles = glob('diy/begin/*.php');
foreach ($beginfiles as $file) {
    require_once $file;
}
require_once('admin/mysqlconn.php');
$row = $conn->get('main', '*', ['id' => 1]);
$companyname = $row["companyname"];
$websitename = $row["websitename"];
$imagesiteurl = $row["imagesiteurl"];
$headlogo = $row["headlogo"];
$contentlogo = $row["contentlogo"];
$paytype = $row["payment_type"];
$payurl = $row["payment_url"];
$isquestionsensor = $row["isquestionsensor"];
$isanswersensor = $row["isanswersensor"];
$isquestionfilter = $row["isquestionfilter"];
$isanswerfilter = $row["isanswerfilter"];
$iswebsiteinfo = $row["iswebsiteinfo"];
$websiteinfotitle = $row["websiteinfotitle"];
$websiteinfocontent = $row["websiteinfocontent"];
$websiteinfotime = $row["websiteinfotime"];
$isweixinlogin = $row["isweixinlogin"];
$isweixinregister = $row["isweixinregister"];
$isthirdpartylogin = $row["isthirdpartylogin"];
$iswindowlogin = $row["iswindowlogin"];
$thirdpartyloginurl = $row["thirdpartyloginurl"];
$freetryshare = $row["freetryshare"];
$welcomemessage = $row["welcomemessage"];
$midjourneymodelid = 0;
$midjourneymodelname = "";

if (strpos($paytype, 'weixin') !== false) {
    $isweixinpay = true;
} else {
    $isweixinpay = false;
}
if (strpos($paytype, 'alipay') !== false) {
    $isalipay = true;
} else {
    $isalipay = false;
}
if (strpos($paytype, 'shop') !== false) {
    $isshoppay = true;
} else {
    $isshoppay = false;
}


$rechargetext = '<div id="buypanel" style="height:100%;"><div style="height: 80%; display: flex; flex-direction: column; justify-content: center; align-items: center;">';
$result = $conn->select("cardtype", "*", ["ishidden[!]" => 1, "ORDER" => "id"]);
foreach ($result as $row) {
    $rechargetext .= '<div class="product" productid="' . $row["id"] . '"><div class="product-name">' . $row["cardname"] . '</div><div class="product-price">' . $row["price"] . ' 元</div><div class="product-limit">' . $row["quota"] . ' 积分</div><div class="product-days">' . $row["extenddays"] . ' 天</div></div>';
}
$rechargetext .= '</div><div style="height: 20%; display: flex; justify-content: center; align-items: center;"><input type=hidden id="productid">';
if ($isweixinpay) {
    $rechargetext .= '<button onclick="showwxpayqrcode();" style="background-color: #007bff; color: #fff; border: none; padding: 5px 10px; font-size: 16px; cursor: pointer; margin:5px;">微信支付</button>';
}
if ($isalipay) {
    $rechargetext .= '<button onclick="showalipayqrcode();" style="background-color: #007bff; color: #fff; border: none; padding: 5px 10px; font-size: 16px; cursor: pointer; margin:5px;">支付宝</button>';
}
if ($isshoppay) {
    $rechargetext .= '<button onclick="gotoshop();" style="background-color: #007bff; color: #fff; border: none; padding: 5px 10px; font-size: 16px; cursor: pointer; margin:5px;">购卡</button><button onclick="showrechargecard();" style="background-color: #007bff; color: #fff; border: none; padding: 5px 10px; font-size: 16px; cursor: pointer; margin:5px;">卡密充值</button>';
}
$rechargetext .= '</div></div>';

// 模型
$result = $conn->select("model", "*", ["isvalid" => true, "ORDER" => ["sequenceid", "isimage", "id"]]);
$modelArray = array();
foreach ($result as $row) {
    if ($row["modelvalue"] == "midjourney_image") {
        $midjourneymodelid = $row["id"];
        $midjourneymodelname = $row["modelname"];
    }
    if (!$row["isimage"]) {
        $modelArray[] = array('title' => $row['modelname'], 'modelid' => $row['id'], 'modelvalue' => $row['modelvalue'], 'modelisimage' => 0, 'modelprice' => $row['modelprice']);
    } else {
        if ($row["isimage"] % 2 == 1) {
            $modelArray[] = array('title' => '画图-' . $row['modelname'], 'modelid' => $row['id'], 'modelvalue' => $row['modelvalue'], 'modelisimage' => 1, 'modelprice' => $row['modelprice']);
        }
        if (intval(abs(intval(abs($row["isimage"] - 4.5)) - 1.5)) == 0) {
            $modelArray[] = array('title' => '改图-' . $row['modelname'], 'modelid' => $row['id'], 'modelvalue' => $row['modelvalue'], 'modelisimage' => 2, 'modelprice' => $row['modelprice']);
        }
        if ($row["isimage"] >= 4) {
            $modelArray[] = array('title' => '识图-' . $row['modelname'], 'modelid' => $row['id'], 'modelvalue' => $row['modelvalue'], 'modelisimage' => 4, 'modelprice' => $row['modelprice']);
        }
    }
}
usort($modelArray, function ($a, $b) use ($modelArray) {
    $indexA = array_search($a, $modelArray);
    $indexB = array_search($b, $modelArray);
    if ($a['modelisimage'] == $b['modelisimage']) {
        return $indexA - $indexB; // 按照原始顺序排序
    }
    return $a['modelisimage'] - $b['modelisimage']; // 按照modelisimage排序
});

// 角色
$roleArray = array();
$result = $conn->select("role", "*", ["isvalid" => true, "ORDER" => "id"]);
$roleArray_2[] = array('title' => '默认', 'rolevalue' => '');
foreach ($result as $row) {
    $roleArray[] = array('rolename' => $row['rolename'], 'rolevalue' => $row['rolevalue']);
    $roleArray_2[] = array('title' => $row['rolename'], 'rolevalue' => $row['rolevalue']);
}
$role_js = json_encode($roleArray_2, JSON_UNESCAPED_UNICODE);

// 场景
$result = $conn->select("scene", "*", ["isvalid" => true, "ORDER" => "id"]);
$sceneArray = array();
foreach ($result as $row) {
    $sceneArray[] = array('scenename' => $row['scenename'], 'scenevalue' => $row['scenevalue']);
}

// 默认模型名称
$row = $conn->get("model", "*", ["isvalid" => true, "ORDER" => "sequenceid"]);
$defaultmodelid = $row["id"];
$defaultmodelisimage = $row["isimage"];
$defaultmodelvalue = $row["modelvalue"];
$defaultmodelname = $row["modelname"];

$conn->pdo->beginTransaction();
$rndstrseed = 0;
try {
    // 读取当前计数器数值
    $rndstrseed = $conn->get('main', 'rndstrseed', ['id' => 1, 'FOR UPDATE']);
    // 更新计数器字段
    $conn->update("main", ["rndstrseed[+]" => 1]);
    // 提交事务
    $conn->pdo->commit();
} catch (Exception $e) {
    // 回滚事务
    $conn->pdo->rollback();
}

mt_srand($rndstrseed);
$websiterndstr = mt_rand(10000000, 99999999);
// 关闭数据库连接


?>
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <title><?php echo $websitename; ?></title>
    <link rel="stylesheet" type="text/css" href="css/main.css" />
    <link rel="stylesheet" type="text/css" href="css/index.css?v=6.0.2410" />
    <link rel="stylesheet" href="css/layui.css">
    <link rel="stylesheet" href="css/test.css">
    <link rel="stylesheet" href="katex/katex.min.css">
    <?php
    $headfiles = glob('diy/head/*.php');
    foreach ($headfiles as $file) {
        require_once $file;
    }
    ?>
</head>


<body>
    <!-- 主体 -->
    <div class="home_container home_container">
        <!-- 左 -->
        <div class="home_sidebar">
            <div class="home_sidebar-header">
                <div class="home_sidebar-title"><?php echo $websitename; ?></div>
                <div class="home_sidebar-sub-title" id='userpanel' style="display: none;margin-top:10px;width:100%;">
                    剩余 <span id="quota"></span> 积分，<span id="expiretime"></span> 过期
                </div>
                <?php
                if ($freetryshare > 0) {
                    ?>
                    <div class="home_sidebar-sub-title" style="margin-top:10px;width:100%;cursor:pointer;" id="sharebutton">
                        邀请好友送双方各 <?php echo $freetryshare; ?> 积分，点击查看详情
                    </div>
                    <?php
                }
                ?>
                <div class="home_sidebar-logo no-dark"><img src="<?php echo $headlogo; ?>" style="width:40px;" /></div>
            </div>
            <div class="home_sidebar-header-bar">
                <button class="button_icon-button button_shadow home_sidebar-bar-button clickable">
                    <div class="button_icon-button-icon"><i class="layui-icon">&#xe66f;</i></div>
                    <div class="button_icon-button-text">&nbsp;用户登录</div>
                </button>


            </div>

            <!-- 会话列表 -->
            <div class="home_sidebar-body"></div>

            <div class="home_sidebar-tail">
                <div><button id="createchatbutton" class="button_icon-button button_shadow  clickable" role="button">
                        <div class="button_icon-button-icon"><i class="layui-icon">&#xe61f;</i></div>
                        <div class="button_icon-button-text">新的聊天</div>
                    </button></div>
                <div><button id="chathistorybutton" class="button_icon-button button_shadow  clickable" role="button">
                        <div class="button_icon-button-icon"><i class="layui-icon">&#xe62d;</i></div>
                        <div class="button_icon-button-text">全部聊天日志</div>
                    </button></div>
            </div>
        </div>
        <!-- 右 -->
        <div class="home_window-content" id="app-body">
            <div class="home_chat">
                <input id='role_js' type="hidden" value='<?php echo $role_js ?>'>
                <div class="window-header">
                    <div class="window-header-title">
                        <div class="window-header-main-title">新的聊天</div>
                        <div class="window-header-sub-title">与 <?php echo $websitename; ?> 的 <span
                                id='chat_msg_count'>0</span> 条对话</div>
                    </div>
                    <div class="window-actions">
                        <div class="window-action-button home_mobile">
                            <button class="button_icon-button button_border  clickable" title="查看消息列表" role="button">
                                <div class="button_icon-button-icon">
                                    <i class="layui-icon layui-icon-shrink-right"></i>
                                </div>
                            </button>
                        </div>
                        <div class="window-action-button" style="display:none;" id="divsharechat">
                            <button class="button_icon-button button_border clickable" title="导出聊天记录" role="button">
                                <div class="button_icon-button-icon false"><i class="layui-icon">&#xe641;</i></div>
                            </button>
                        </div>
                        <div class="window-action-button max_min">
                            <button class="button_icon-button button_border clickable" title="界面展开/收回" role="button">
                                <div class="button_icon-button-icon">
                                    <i class="layui-icon layui-icon-screen-full"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                    <div class="chat_prompt-toast"></div>
                </div>

                <!-- 消息 -->
                <div class="home_chat-body"></div>

                <!-- 底部配置及输入框 -->
                <div class="home_chat-input-panel">
                    <!-- 模型 -->
                    <div id="model_list_div" class="home_prompt-hints" style="display: none;">

                        <div class="tabs">
                            <div class="tab-pane">
                                <input type="radio" name="tab" id="tab01" checked />
                                <label class="tab-item" for="tab01">
                                    <i class="layui-icon" style="font-size: 20px;">&#xe63a;</i>&nbsp;智能对话
                                </label>
                                <div class="tab-content">
                                    <?php
                                    foreach ($modelArray as $model) {
                                        if (!$model['modelisimage']) {
                                            $price = $model['modelprice'] ? $model['modelprice'] . '积分' : '免费';
                                            ?>
                                            <div class="home_prompt-hint modelisimage0" style="margin: 0;"
                                                id="checked_<?php echo $model['modelid'] ?>_<?php echo $model['modelisimage'] ?>"
                                                onclick="$('#model_list_div').toggle();set_model(<?php echo $model['modelid'] . ',\'' . $model['modelvalue'] . '\',' . $model['modelisimage'] . ',\'' . $model['title'] . '\'' . ',' . $model['modelprice'] ?>)">
                                                <div class="home_hint-title">
                                                    <?php echo $model['title'] ?><span
                                                        style="float: right;"><?php echo $price ?></span>
                                                </div>
                                            </div>
                                            <?php
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="tab-pane">
                                <input type="radio" name="tab" id="tab02" />
                                <label class="tab-item" for="tab02">
                                    <i class="layui-icon" style="font-size: 20px;">&#xe66a;</i>&nbsp;智能绘图
                                </label>
                                <div class="tab-content">
                                    <?php
                                    foreach ($modelArray as $model) {
                                        if ($model['modelisimage']) {
                                            $price = $model['modelprice'] ? $model['modelprice'] . '积分' : '免费';
                                            ?>
                                            <div class="home_prompt-hint modelisimage<?php echo $model['modelisimage']; ?>"
                                                style="margin: 0;"
                                                id="checked_<?php echo $model['modelid'] ?>_<?php echo $model['modelisimage'] ?>"
                                                onclick="$('#model_list_div').toggle();set_model(<?php echo $model['modelid'] . ',\'' . $model['modelvalue'] . '\',' . $model['modelisimage'] . ',\'' . $model['title'] . '\'' . ',' . $model['modelprice'] ?>)">
                                                <div class="home_hint-title">
                                                    <?php echo $model['title'] ?><span
                                                        style="float: right;"><?php echo $price ?></span>
                                                </div>
                                            </div>
                                            <?php
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>

                        <div class="pc_div" style="display:flex;">
                            <div class="home_prompt-hints_div"
                                style="box-shadow:2px 2px 2px 2px rgba(0,0,0,.1);margin:10px 0;height:100%;">
                                <div class="home_hint-title" style="padding: 10px 25px;font-size:18px;"><i
                                        class="layui-icon" style="font-size: 20px;">&#xe63a;</i>&nbsp;智能对话</div>
                                <?php
                                foreach ($modelArray as $model) {
                                    if (!$model['modelisimage']) {
                                        $price = $model['modelprice'] ? $model['modelprice'] . '积分' : '免费';
                                        ?>
                                        <div class="home_prompt-hint modelisimage0"
                                            id="checked_<?php echo $model['modelid'] ?>_<?php echo $model['modelisimage'] ?>"
                                            onclick="$('#model_list_div').toggle();set_model(<?php echo $model['modelid'] . ',\'' . $model['modelvalue'] . '\',' . $model['modelisimage'] . ',\'' . $model['title'] . '\'' . ',' . $model['modelprice'] ?>)">
                                            <div class="home_hint-title">
                                                <?php echo $model['title'] ?><span
                                                    style="float: right;"><?php echo $price ?></span>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                }
                                ?>
                            </div>

                            <div class="home_prompt-hints_div"
                                style="float: right;box-shadow:2px 2px 2px 2px rgba(0,0,0,.1);margin:10px 1% 10px 3%;height:100%;">
                                <div class="home_hint-title" style="padding: 10px 25px;font-size:18px;"><i
                                        class="layui-icon" style="font-size: 20px;">&#xe66a;</i>&nbsp;智能绘图</div>
                                <?php
                                foreach ($modelArray as $model) {
                                    if ($model['modelisimage']) {
                                        $price = $model['modelprice'] ? $model['modelprice'] . '积分' : '免费';
                                        ?>
                                        <div class="home_prompt-hint modelisimage<?php echo $model['modelisimage']; ?>"
                                            id="checked_<?php echo $model['modelid'] ?>_<?php echo $model['modelisimage'] ?>"
                                            onclick="$('#model_list_div').toggle();set_model(<?php echo $model['modelid'] . ',\'' . $model['modelvalue'] . '\',' . $model['modelisimage'] . ',\'' . $model['title'] . '\'' . ',' . $model['modelprice'] ?>)">
                                            <div class="home_hint-title">
                                                <?php echo $model['title'] ?><span
                                                    style="float: right;"><?php echo $price ?></span>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                }
                                ?>
                            </div>
                        </div>


                    </div>
                    <!-- 场景 -->
                    <div id="scene_list_div" class="home_prompt-hints" style="display: none;">
                        <?php
                        foreach ($sceneArray as $scene) {
                            ?>
                            <div class="home_prompt-hint">
                                <div class="home_hint-title"><?php echo $scene['scenename'] ?></div>
                                <div class="home_hint-content"><?php echo $scene['scenevalue'] ?></div>
                            </div>
                            <?php
                        }
                        ?>
                    </div>

                    <div class="chat_chat-input-actions">
                        <div class="chat_chat-input-action clickable mode_js" id='model_button' title="模型">
                            <i class="layui-icon">&#xe653;</i>
                            <span><?php echo $defaultmodelname ?>
                        </div>
                        <div class="chat_chat-input-action clickable role_js" title="角色">
                            <i class="layui-icon">&#xe66f;</i><span></span>
                        </div>
                        <div class="chat_chat-input-action clickable" id='scene_button' title="场景"><i
                                class="layui-icon">&#xe62a;</i></div>
                        <input id='model' type="hidden" value='<?php echo $defaultmodelid ?>'>
                        <input id='modelvalue' type="hidden" value='<?php echo $defaultmodelvalue ?>'>
                        <input id='modelisimage' type="hidden" value='<?php echo $defaultmodelisimage ?>'>
                        <input id='imageurl' type="hidden">
                        <input id='blendimageurl' type="hidden">
                        <input id='role' type="hidden" value=''>
                        <div class="chat_chat-input-action clickable" id='all_style' title="亮色/暗色模式"><i
                                class="layui-icon">&#xe68d;</i><span></div>
                    </div>
                    <!-- 输入 -->
                    <div class="home_chat-input-panel-inner">
                        <div class="chat_chat-input-actions" style="width:50px;align-items:center;">
                            <div id="uploadpicbutton" class="chat_chat-input-action clickable" title="上传图片"
                                style="margin:0;">
                                <i class="layui-icon"></i>
                            </div>
                            <div id="sendvoicebutton" class="chat_chat-input-action clickable" title="发送语音"
                                style="margin:0;">
                                <i class="layui-icon"></i>
                            </div>
                        </div>
                        <button id="recordingbutton" class="button_icon-button home_chat-input clickable button_primary"
                            role="button" style="width:100%;padding:10px;display:none;">
                            <div class="button_icon-button-text">按住 说话</div>
                        </button>
                        <textarea id="question" class="home_chat-input" placeholder="Ctrl + Enter 发送"
                            rows="3"></textarea>
                        <button id="textinputbutton"
                            class="button_icon-button home_chat-input-send clickable button_primary" role="button"
                            style="padding:0;">
                            <div id="goplane" class="button_icon-button-icon no-dark"
                                style="padding:19px 13px 19px 18px;margin:0;"><i class="layui-icon">&#xe609;</i></div>
                            <div class="button_icon-button-text" style="padding:10px 0;margin:0;"><span id='go'
                                    style="padding:10px 7px 10px 0;">发送</span><span
                                    style="margin:0;padding:0;border-left:1px solid silver;"></span><i
                                    class="layui-icon enter_js" style="font-size:12px;padding:10px;"></i></div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录框 -->
    <div id="mydialog" style="display: none;">
        <div id="mytitle"
            style="width: 300px; height: 50px;background-color: rgb(79, 128, 225);color: #fff;font-size: 16px;line-height: 50px;text-align: center;">
        </div>
        <div id="mycontent" style="width: 300px; height: 300px;padding: 20px;text-align: center;"></div>
    </div>
    <div class="modal-mask" id='chat_out' style="display: none;">
        <div class="ui-lib_modal-container">
            <div class="ui-lib_modal-header">
                <div class="ui-lib_modal-title">导出聊天记录为 Markdown</div>
                <div class="ui-lib_modal-close-btn" onclick="$('#chat_out').toggle()"><i class="layui-icon">&#x1006;</i>
                </div>
            </div>
            <div class="ui-lib_modal-content">
                <div class="markdown-body">
                    <pre class="home_export-content"></pre>
                </div>
            </div>
            <div class="ui-lib_modal-footer">
                <div class="ui-lib_modal-actions">
                    <div class="ui-lib_modal-action"><button class="button_icon-button button_border clickable copy_msg"
                            copy='all_msg' role="button">
                            <div class="button_icon-button-icon"><i class="layui-icon">&#xe655;</i></div>
                            <div class="button_icon-button-text">全部复制</div>
                        </button></div>
                    <div class="ui-lib_modal-action"><button class="button_icon-button button_border clickable"
                            role="button" onclick="download_msg()">
                            <div class="button_icon-button-icon"><i class="layui-icon">&#xe601;</i></div>
                            <div class="button_icon-button-text">下载文件</div>
                        </button></div>
                </div>
            </div>
        </div>
    </div>
    <div id="notice" style="display:none;position: relative;height: 100%;">
        <div style="height: 100%;overflow: auto;padding:20px;"><?php echo $websiteinfocontent ?></div>
    </div>
    <div class="overlay">
        <img src="" alt="展示图片">
    </div>
    <input type="file" id="uploadImage" onchange="previewImage()" accept="image/*" style="display:none;">
    <input type="file" id="uploadBlendImage" onchange="previewImage(1)" accept="image/*" style="display:none;">
    <iframe id=temp style="display:none;"></iframe>
    <script src="js/jquery.min.js"></script>
    <script src="js/crypto-js.min.js"></script>
    <script src="js/jquery.qrcode.min.js"></script>
    <script src="js/jquery.cookie.min.js"></script>
    <script src="js/layui.js"></script>
    <script src="js/remarkable.js"></script>
    <script src="js/index.js?v=6.6.2434"></script>
    <script src="js/clipboard.min.js"></script>
    <script src="js/howler.min.js"></script>
    <link rel="stylesheet" href="css/hightlight.css">
    <script src="js/highlight.min.js"></script>
    <script src="js/recorder.js"></script>
    <script src="js/md5.js"></script>
    <script>
        function buy() {
            buylayer = layer.open({
                type: 1,
                title: '充值',
                area: ['350px', '400px'],
                shade: 0.5,
                scrollbar: true,
                offset: ['calc(50% - 200px)', 'calc(50% - 175px)'],
                fixed: true,
                content: '<?php echo $rechargetext; ?>'
            });
        }
        iswindowloginonly = false;
        isthirdpartyloginonly = false;

        thirdpartyloginurl = '<?php echo $thirdpartyloginurl ?>';
        isquestionsensor = <?php echo $isquestionsensor ?>;
        isanswersensor = <?php echo $isanswersensor ?>;
        isquestionfilter = <?php echo $isquestionfilter ?>;
        isanswerfilter = <?php echo $isanswerfilter ?>;
        websiterndstr = <?php echo $websiterndstr ?>;
        welcomemessage = decodeURIComponent(escape(atob('<?php echo $welcomemessage ?>')));
        imagesiteurl = '<?php echo $imagesiteurl ?>';
        isAndroidApp = <?php echo ((isset($_GET["from"])) && ($_GET["from"] == 'android')) ? 1 : 0; ?>;
        midjourneymodelid = <?php echo $midjourneymodelid; ?>;
        midjourneymodelname = '<?php echo $midjourneymodelname; ?>';
        <?php
        if ($freetryshare > 0)
            echo 'freetryshare = ' . $freetryshare . ';';
        if (isset($_GET["i"])) {
            echo "$.cookie('sharefromuserid', '" . $_GET["i"] . "', {expires: 1, path: '/'});";
        }
        if ($iswebsiteinfo) {
            ?>
            if (!($.cookie('websiteinfotime')) || ($.cookie('websiteinfotime') != '<?php echo $websiteinfotime; ?>')) {
                layer.open({
                    type: 1,
                    title: '<div style="font-weight:bold;text-align:center"><?php echo $websiteinfotitle ?></div>',
                    area: [(0.6 * $(document.body).width() > 380) ? '60%' : '380px', '60%'],
                    offset: ['20%', (0.6 * $(document.body).width() > 380) ? '20%' : (Number(($(document.body).width() - 380) / 2) + 'px')],
                    content: $('#notice'),
                    fixed: true,
                    shadeClose: true,
                    btn: ['已阅读，请下次再提醒', '已阅读，请不要再提醒本公告'],
                    btnAlign: 'c',
                    yes: function (index, layero) {
                        layer.close(index);
                    },
                    btn2: function (index, layero) {
                        $.cookie('websiteinfotime', '<?php echo $websiteinfotime; ?>');
                        layer.close(index);
                    }
                });
            }

            <?php
        }
        if (($isweixinlogin) && ($isthirdpartylogin)) {
            ?>
            logintitleright = '<div style="display: flex;cursor:pointer;"><div style="width: 100%; height: 50px;background-color: rgb(79, 128, 225);color: #fff;font-size: 16px;line-height: 50px;text-align: center;" onclick="showwxlogin();">微信扫码登录</div><div style="width: 100%; height: 50px;background-color: #fff;color: #000;font-size: 16px;line-height: 50px;text-align: center;"">邮箱密码登录</div></div>';
            <?php
            if ($iswindowlogin) {
                ?>
                logintitleleft = '<div style="display: flex;cursor:pointer;"><div style="width: 100%; height: 50px;background-color: #fff;color: #000;font-size: 16px;line-height: 50px;text-align: center;">微信扫码登录</div><div style="width: 100%; height: 50px;background-color: rgb(79, 128, 225);color: #fff;font-size: 16px;line-height: 50px;text-align: center;" onclick="showuserlogin();">邮箱密码登录</div></div>';
                <?php
            } else {
                ?>
                logintitleleft = '<div style="display: flex;cursor:pointer;"><div style="width: 100%; height: 50px;background-color: #fff;color: #000;font-size: 16px;line-height: 50px;text-align: center;">微信扫码登录</div><div style="width: 100%; height: 50px;background-color: rgb(79, 128, 225);color: #fff;font-size: 16px;line-height: 50px;text-align: center;" onclick="window.open(thirdpartyloginurl, \'_blank\');">第三方用户登录</div></div>';
                <?php
            }
        } elseif ($isweixinlogin) {
            ?>
            logintitleleft = '<div style="display: flex;"><div style="width: 100%; height: 50px;background-color: rgb(79, 128, 225);color: #fff;font-size: 16px;line-height: 50px;text-align: center;">微信扫码登录</div></div>';
            <?php
        } elseif ($isthirdpartylogin) {
            if ($iswindowlogin) {
                ?>
                iswindowloginonly = true;
                logintitleright = '<div style="display: flex;"><div style="width: 100%; height: 50px;background-color: rgb(79, 128, 225);color: #fff;font-size: 16px;line-height: 50px;text-align: center;">邮箱密码登录</div></div>';
                <?php
            } else {
                ?>
                isthirdpartyloginonly = true;
                <?php
            }
        }
        ?>
        // 获取会话数据
        if ($("#quota").text() == "") {
            var html = '<div class="home_chat-message">' +
                '<div style="width:calc(100% - 40px);top:50%;position:absolute;"><img src="<?php echo $contentlogo; ?>" style="max-width:80%;margin:auto; opacity:0.5;"></div>' +
                '<div class="home_chat-message-container">' +
                '<div class="home_chat-message-item">' +
                '<div class="markdown-body">' +
                '<p>请先<span style="cursor:pointer;" onclick="showqrcode();"> 登录 </span>再提问，未授权用户无法使用</p>' +
                '</div>' +
                '</div>' +
                '<div class="home_chat-message-actions">' +
                '<div class="home_chat-message-action-date">' + getMyDate() + '</div>' +
                '</div>' +
                '</div>' +
                '</div>';
            $('.home_chat-body').append(html);
        }
    </script>
    <script src="katex/katex.min.js"></script>
    <!--
    <script type="text/x-mathjax-config">
        MathJax.Hub.Config({
            showProcessingMessages: false,
            messageStyle: "none",
            tex2jax: {
                inlineMath: [],  // 禁用行内公式的自动解析
                displayMath: []  // 禁用显示公式的自动解析
            }
        });
    </script>
    <script src="//cdn.bootcss.com/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script>
-->
    <?php
    $endfiles = glob('diy/end/*.php');
    foreach ($endfiles as $file) {
        require_once $file;
    }
    ?>
</body>


</html>