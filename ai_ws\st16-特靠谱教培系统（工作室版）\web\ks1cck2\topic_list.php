<?php
require '_ks1.php';
@session_start();

// 检查用户是否已登录
if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
    header('Location: login.php');
    exit;
}

// 获取所有选题
function get_all_topics() {
    $table = get_table_name('topics');
    $sql = "SELECT t.*, tm.views, tm.favorites, tm.platforms, tm.keywords 
            FROM $table t 
            LEFT JOIN cck1_topic_meta tm ON t.topic_id = tm.topic_id 
            ORDER BY t.created_at DESC";
    return get_data($sql);
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选题列表</title>
    <link rel="stylesheet" href="res/ks1table.css">
</head>
<body>
    <h1>选题列表</h1>
    <div><?php echo $nav_html; ?></div>

    <h2>所有选题</h2>
    <table border="1">
        <tr>
            <th>选题标题</th>
            <th>描述</th>
            <th>关键词</th>
            <th>适用平台</th>
            <th>创建时间</th>
        </tr>
        <?php
        $topics = get_all_topics();
        if ($topics) {
            foreach ($topics as $topic) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($topic['title']) . '</td>';
                echo '<td>' . htmlspecialchars($topic['description']) . '</td>';
                echo '<td>' . htmlspecialchars($topic['keywords']) . '</td>';
                echo '<td>' . htmlspecialchars($topic['platforms']) . '</td>';
                echo '<td>' . htmlspecialchars($topic['created_at']) . '</td>';
                echo '</tr>';
            }
        } else {
            echo '<tr><td colspan="5">暂无选题。</td></tr>';
        }
        ?>
    </table>
    <div><?php echo $nav_html; ?></div>
</body>
</html>