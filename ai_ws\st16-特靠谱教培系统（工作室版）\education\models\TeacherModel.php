<?php
/**
 * 教师模型
 * 处理教师数据的操作
 */
class TeacherModel extends Model {
    /**
     * 表名
     * @var string
     */
    protected $table = 'teachers';
    
    /**
     * 根据ID获取教师信息
     * @param int $id 教师ID
     * @return array|null 教师信息
     */
    public function getTeacherById($id) {
        $sql = "SELECT t.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} t 
                JOIN users u ON t.user_id = u.id 
                WHERE t.id = ?";
        return $this->db->selectOne($sql, [$id]);
    }
    
    /**
     * 根据用户ID获取教师信息
     * @param int $userId 用户ID
     * @return array|null 教师信息
     */
    public function getTeacherByUserId($userId) {
        $sql = "SELECT t.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} t 
                JOIN users u ON t.user_id = u.id 
                WHERE t.user_id = ?";
        return $this->db->selectOne($sql, [$userId]);
    }
    
    /**
     * 获取教师列表
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array 教师列表
     */
    public function getTeachers($page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $sql = "SELECT t.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} t 
                JOIN users u ON t.user_id = u.id 
                ORDER BY t.id DESC 
                LIMIT {$offset}, {$perPage}";
        return $this->db->select($sql);
    }
    
    /**
     * 获取教师总数
     * @return int 教师总数
     */
    public function getTeacherCount() {
        return $this->db->count("SELECT COUNT(*) FROM {$this->table}");
    }
    
    /**
     * 创建教师
     * @param array $data 教师数据
     * @return int 新教师的ID
     */
    public function createTeacher($data) {
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * 更新教师信息
     * @param int $id 教师ID
     * @param array $data 教师数据
     * @return bool 是否成功
     */
    public function updateTeacher($id, $data) {
        return $this->db->update($this->table, $data, "id = ?", [$id]);
    }
    
    /**
     * 删除教师
     * @param int $id 教师ID
     * @return bool 是否成功
     */
    public function deleteTeacher($id) {
        return $this->db->delete($this->table, "id = ?", [$id]);
    }
    
    /**
     * 根据科目获取教师列表
     * @param string $subject 科目
     * @return array 教师列表
     */
    public function getTeachersBySubject($subject) {
        $sql = "SELECT t.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} t 
                JOIN users u ON t.user_id = u.id 
                WHERE t.subject = ? 
                ORDER BY t.id DESC";
        return $this->db->select($sql, [$subject]);
    }
    
    /**
     * 搜索教师
     * @param string $keyword 关键词
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array 教师列表
     */
    public function searchTeachers($keyword, $page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $keyword = "%{$keyword}%";
        $sql = "SELECT t.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} t 
                JOIN users u ON t.user_id = u.id 
                WHERE u.name LIKE ? OR t.subject LIKE ? OR t.title LIKE ? 
                ORDER BY t.id DESC 
                LIMIT {$offset}, {$perPage}";
        return $this->db->select($sql, [$keyword, $keyword, $keyword]);
    }
    
    /**
     * 获取搜索结果总数
     * @param string $keyword 关键词
     * @return int 结果总数
     */
    public function getSearchCount($keyword) {
        $keyword = "%{$keyword}%";
        $sql = "SELECT COUNT(*) FROM {$this->table} t 
                JOIN users u ON t.user_id = u.id 
                WHERE u.name LIKE ? OR t.subject LIKE ? OR t.title LIKE ?";
        return $this->db->count($sql, [$keyword, $keyword, $keyword]);
    }
}