/**!
 * easy<PERSON>ieChart
 * Lightweight plugin to render simple, animated and retina optimized pie charts
 *
 * @license 
 * <AUTHOR> <<EMAIL>> (http://robert-fleischmann.de)
 * @version 2.1.5
 **/
(function(a,b){if(typeof exports==="object"){module.exports=b(require("jquery"))}else{if(typeof define==="function"&&define.amd){define(["jquery"],b)}else{b(a.jQuery)}}}(this,function(c){var a=function(e,n){var l;var f=document.createElement("canvas");e.appendChild(f);if(typeof(G_vmlCanvasManager)!=="undefined"){G_vmlCanvasManager.initElement(f)}var m=f.getContext("2d");f.width=f.height=n.size;var k=1;if(window.devicePixelRatio>1){k=window.devicePixelRatio;f.style.width=f.style.height=[n.size,"px"].join("");f.width=f.height=n.size*k;m.scale(k,k)}m.translate(n.size/2,n.size/2);m.rotate((-1/2+n.rotate/180)*Math.PI);var i=(n.size-n.lineWidth)/2;if(n.scaleColor&&n.scaleLength){i-=n.scaleLength+2}Date.now=Date.now||function(){return +(new Date())};var j=function(q,o,r){r=Math.min(Math.max(-1,r||0),1);var p=r<=0?true:false;m.beginPath();m.arc(0,0,i,0,Math.PI*2*r,p);m.strokeStyle=q;m.lineWidth=o;m.stroke()};var d=function(){var q;var p;m.lineWidth=1;m.fillStyle=n.scaleColor;m.save();for(var o=24;o>0;--o){if(o%6===0){p=n.scaleLength;q=0}else{p=n.scaleLength*0.6;q=n.scaleLength-p}m.fillRect(-n.size/2+q,0,p,1);m.rotate(Math.PI/12)}m.restore()};var h=(function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(o){window.setTimeout(o,1000/60)}}());var g=function(){if(n.scaleColor){d()}if(n.trackColor){j(n.trackColor,n.lineWidth,1)}};this.getCanvas=function(){return f};this.getCtx=function(){return m};this.clear=function(){m.clearRect(n.size/-2,n.size/-2,n.size,n.size)};this.draw=function(p){if(!!n.scaleColor||!!n.trackColor){if(m.getImageData&&m.putImageData){if(!l){g();l=m.getImageData(0,0,n.size*k,n.size*k)}else{m.putImageData(l,0,0)}}else{this.clear();g()}}else{this.clear()}m.lineCap=n.lineCap;var o;if(typeof(n.barColor)==="function"){o=n.barColor(p)}else{o=n.barColor}j(o,n.lineWidth,p/100)}.bind(this);this.animate=function(r,q){var o=Date.now();n.onStart(r,q);var p=function(){var t=Math.min(Date.now()-o,n.animate.duration);var s=n.easing(this,t,r,q-r,n.animate.duration);this.draw(s);n.onStep(r,q,s);if(t>=n.animate.duration){n.onStop(r,q)}else{h(p)}}.bind(this);h(p)}.bind(this)};var b=function(g,h){var d={barColor:"#ef1e25",trackColor:"#f9f9f9",scaleColor:"#dfe0e0",scaleLength:5,lineCap:"round",lineWidth:3,size:110,rotate:0,animate:{duration:1000,enabled:true},easing:function(k,l,j,n,m){l=l/(m/2);if(l<1){return n/2*l*l+j}return -n/2*((--l)*(l-2)-1)+j},onStart:function(k,j){return},onStep:function(l,k,j){return},onStop:function(k,j){return}};if(typeof(a)!=="undefined"){d.renderer=a}else{if(typeof(SVGRenderer)!=="undefined"){d.renderer=SVGRenderer}else{throw new Error("Please load either the SVG- or the CanvasRenderer")}}var e={};var f=0;var i=function(){this.el=g;this.options=e;for(var j in d){if(d.hasOwnProperty(j)){e[j]=h&&typeof(h[j])!=="undefined"?h[j]:d[j];if(typeof(e[j])==="function"){e[j]=e[j].bind(this)}}}if(typeof(e.easing)==="string"&&typeof(jQuery)!=="undefined"&&jQuery.isFunction(jQuery.easing[e.easing])){e.easing=jQuery.easing[e.easing]}else{e.easing=d.easing}if(typeof(e.animate)==="number"){e.animate={duration:e.animate,enabled:true}}if(typeof(e.animate)==="boolean"&&!e.animate){e.animate={duration:1000,enabled:e.animate}}this.renderer=new e.renderer(g,e);this.renderer.draw(f);if(g.dataset&&g.dataset.percent){this.update(parseFloat(g.dataset.percent))}else{if(g.getAttribute&&g.getAttribute("data-percent")){this.update(parseFloat(g.getAttribute("data-percent")))}}g.style.width=g.style.height=e.size+"px";g.style.lineHeight=(e.size-1)+"px"}.bind(this);this.update=function(j){j=parseFloat(j);if(e.animate.enabled){this.renderer.animate(f,j)}else{this.renderer.draw(j)}f=j;return this}.bind(this);this.disableAnimation=function(){e.animate.enabled=false;return this};this.enableAnimation=function(){e.animate.enabled=true;return this};i()};c.fn.easyPieChart=function(d){return this.each(function(){var e;if(!c.data(this,"easyPieChart")){e=c.extend({},d,c(this).data());c.data(this,"easyPieChart",new b(this,e))}})}}));