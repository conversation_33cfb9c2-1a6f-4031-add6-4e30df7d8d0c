<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
if (isset($_POST["action"])) {
    $arr = array();
    if ($_POST["weixinpay"] == "on") {
        array_push($arr, "weixin");
    }
    if ($_POST["alipay"] == "on") {
        array_push($arr, "alipay");
    }
    if ($_POST["shoppay"] == "on") {
        array_push($arr, "shop");
    }
    $conn->update('main',['payment_type'=>implode(',', $arr),'payment_url'=>$_POST["payurl"],'adminweixinid'=>$_POST["adminweixinid"],'alipayappid'=>$_POST["alipayappid"],'alipayprivatekey'=>$_POST["alipayprivatekey"],'alipaypublickey'=>$_POST["alipaypublickey"]],['id'=>1]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');</script>";
    exit;
}

$row = $conn->get('main','*',['id'=>1]);
$adminweixinid = $row["adminweixinid"];
$paytype = $row["payment_type"];
$payurl = $row["payment_url"];
$alipayappid = $row["alipayappid"];
$alipayprivatekey = $row["alipayprivatekey"];
$alipaypublickey = $row["alipaypublickey"];
if (strpos($paytype, 'weixin') !== false) {
    $isweixinpay = true;
} else {
    $isweixinpay = false;
}
if (strpos($paytype, 'alipay') !== false) {
    $isalipay = true;
} else {
    $isalipay = false;
}
if (strpos($paytype, 'shop') !== false) {
    $isshoppay = true;
} else {
    $isshoppay = false;
}


?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>支付方式配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />

    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>

    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script type="text/javascript">
        ace.vars['base'] = '.';
    </script>
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>

    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-money"></i> 充值管理</li>
                <li class="active">支付方式配置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px 0;">
            <form class="form-horizontal" method=post target=temp>
                <input type=hidden name=action value=set>
                <div class="page-content-area">
                    <div class="row">
                        <div class="space-6"></div>
                        <div class="col-xs-12">
                            <div style="padding-left:50px;">
                                <div style="font-size:25px;padding-bottom:20px;"><input type=checkbox name=weixinpay <?php if ($isweixinpay) echo 'checked'; ?> style="width:20px;height:20px;margin:2px 10px 3px 10px;">微信支付JSAPI（无需生成充值卡，直接购买）</div>
                                <div style="width:400px;display:flex;justify-content: space-between;">
                                    <div><img src="../img/logo_wechat.png" style="width:150px;"></div>
                                </div>
                                <div style="margin-top:30px;">
                                    支付跳转地址：<input type="text" style="text-align:left;" size=60 name="payurl" value="<?php echo $payurl; ?>" autocomplete="off" /><br><br>
                                    管理员微信ID（用于提示用户充值失败时加微信沟通）：<input type="text" value="<?php echo $adminweixinid; ?>" style="text-align:left;" id="adminweixinid" name="adminweixinid" size=27 autoComplete="off"><br><br>

                                </div>
                                <div style="font-size:20px;">微信支付公众号安装包下载请 <a href='wxpay.zip' target='_blank'>点击这里</a> ，填写和部署说明请 <a href='https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=34' target='_blank'>点击这里</a></div>
                            </div>
                            <div style="padding-left:50px;">
                                <div style="font-size:25px;padding-bottom:20px;"><input type=checkbox name=alipay <?php if ($isalipay) echo 'checked'; ?> style="width:20px;height:20px;margin:2px 10px 3px 10px;">支付宝当面付（无需生成充值卡，直接购买）</div>
                                <div style="width:400px;display:flex;justify-content: space-between;">
                                    <div><img src="../img/logo_alipay.png" style="width:150px;"></div>
                                </div>
                                <div style="margin-top:30px;">
                                    支付宝APPID：<input type="text" style="text-align:left;" size=60 name="alipayappid" value="<?php echo $alipayappid; ?>" autocomplete="off" /><br><br>
                                    支付宝商户私钥：<input type="text" style="text-align:left;" size=60 name="alipayprivatekey" value="<?php echo $alipayprivatekey; ?>" autocomplete="off" /><br><br>
                                    支付宝商户公钥：<input type="text" style="text-align:left;" size=60 name="alipaypublickey" value="<?php echo $alipaypublickey; ?>" autocomplete="off" /><br><br>
                                </div>
                                <div style="font-size:20px;">发起支付只和APPID和私钥有关，回调不成功只和公钥有关。填写和部署说明请 <a href='https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=76' target='_blank'>点击这里</a></div>
                            </div>
                            <div style="padding:50px 0 0 50px;">
                                <div style="font-size:25px;padding-bottom:20px;"><input type=checkbox name=shoppay <?php if ($isshoppay) echo 'checked'; ?> style="width:20px;height:20px;margin:2px 10px 3px 10px;">第三方商城（购买链接在充值卡类型中配置）</div>
                                <div style="width:600px;display:flex;"><img src="../img/logo_taobao.png" style="width:200px;"><img src="../img/logo_weidian.png" style="width:200px;"><img src="../img/logo_third.png" style="width:200px;"></div>
                                <div style="padding-top:20px;">
                                    <div class="form-group" style="margin-right:150px;margin-top:30px;text-align:center;">
                                        <div class="col-lg-6">
                                            <button type="submit" class="btn btn-primary">确认修改</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <iframe style="display:none;" id=temp name=temp></iframe>
</body>

</html>