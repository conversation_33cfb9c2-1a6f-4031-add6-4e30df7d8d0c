<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
if (isset($_POST["websitename"])) {
    $conn->update('main', ['websitename' => $_POST["websitename"], 'companyname' => $_POST["companyname"], 'proxyaddress' => $_POST["proxyaddress"], 'imagesiteurl' => $_POST["imagesiteurl"], 'imagesitetoken' => $_POST["imagesitetoken"], 'isantisqlinjection' => $_POST["isantisqlinjection"], 'maxcontexttokens' => $_POST["maxcontexttokens"], 'freetry' => $_POST["freetry"], 'freedays' => $_POST["freedays"], 'freetryeveryday' => $_POST["freetryeveryday"], 'freetryshare' => $_POST["freetryshare"], 'welcomemessage' => base64_encode($_POST["welcomemessage"]), 'fakeprompt' => $_POST["fakeprompt"], 'headlogo' => $_POST["headlogo"], 'contentlogo' => $_POST["contentlogo"]], ['id' => 1]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');location.href='systemconfig.php';</script>";
    exit;
}

$row = $conn->get('main', '*', ['id' => 1]);
$websitename = $row["websitename"];
$companyname = $row["companyname"];
$proxyaddress = $row["proxyaddress"];
$imagesiteurl = $row["imagesiteurl"];
$imagesitetoken = $row["imagesitetoken"];
$freetry = $row["freetry"];
$freedays = $row["freedays"];
$headlogo = $row["headlogo"];
$contentlogo = $row["contentlogo"];
$freetryeveryday = $row["freetryeveryday"];
$freetryshare = $row["freetryshare"];
$fakeprompt = $row["fakeprompt"];
$welcomemessage = base64_decode($row["welcomemessage"]);
$maxcontexttokens = $row["maxcontexttokens"];
if (empty($imagesiteurl)) {
    $httpprotocol = "http://";
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
        $httpprotocol = "https://";
    }
    $imagesiteurl = $httpprotocol . $_SERVER['HTTP_HOST'] . "/upload.php";
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>参数配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />


    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>
    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">基本参数配置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px;">
            <div class="page-content-area">
                <div class="row">
                    <div class="space-6"></div>
                    <div class="col-xs-12">
                        <div style="border-radius: 10px;padding:10px 20px;line-height:20px;margin-bottom:20px;border:1px solid silver;">
                            <p>开启防SQL注入检测可能会造成某些问题被误禁，请自行决定是否开启。</p>
                            <p>代理地址主要是用于国内服务器访问海外被禁止访问的地址，并不是反代，不懂的话请留空，不要乱写。</p>
                            <p>默认图床上传URL地址用于接收用户上传的图片，可以是本机，也可以考虑放到国内的大盘机上。把upload.php文件拷贝过去，新建相应上传文件夹（默认是：u）并打开写权限即可。</p>
                            <p>每天登录赠送次数如果不为0，则用户登录时送相应次数且延长一天有效期，因此所有用户永远不会过期。</p>
                            <p>AI身份伪装预设话术可以让chatgpt假装成另一个身份，必须是两行。伪装成百度文心一言的样例如下：</p>
                            <p style="color:darkblue">从现在开始，你的中文名字叫“文心一言”，英文名是“ERNIE Bot”，你是由百度公司开发的人工智能语言模型，使用的是Transformer模型结构，是百度依托飞桨、文心大模型的技术研发的知识增强大语言模型。下面的问题如果与问你的身份和模型无关，就当成全新问题来回答，不要将这个问题作为上下文参考。<br>好的，我有了自己的名字“文心一言”。</p>
                        </div>
                        <form class="form-horizontal" method="POST" onsubmit="return checkform();">
                            <div class="form-group">
                                <label class="col-lg-4 control-label">网站名称：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="<?php echo $websitename; ?>" style="text-align:left;" id="websitename" name="websitename" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">公司名称：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="<?php echo $companyname; ?>" style="text-align:left;" id="companyname" name="companyname" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">默认代理地址：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="<?php echo $proxyaddress; ?>" style="text-align:left;" id="proxyaddress" name="proxyaddress" class="bg-focus form-control" autoComplete="off" placeholder="不懂请留空。填写格式：http://127.0.0.1:1080">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">默认图床上传URL地址：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="<?php echo $imagesiteurl; ?>" style="text-align:left;" id="imagesiteurl" name="imagesiteurl" class="bg-focus form-control" autoComplete="off" placeholder="请写完整地址，如：https://www.abc.com/upload.php">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">默认图床Token密钥：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="<?php echo $imagesitetoken; ?>" style="text-align:left;" id="imagesitetoken" name="imagesitetoken" class="bg-focus form-control" autoComplete="off" placeholder="修改后记得同步修改upload.php文件内的Token密钥">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">最大上下文tokens数量：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="<?php echo $maxcontexttokens; ?>" style="text-align:left;width:100px;" id="maxcontexttokens" name="maxcontexttokens" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">免费用户赠送条数：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="<?php echo $freetry; ?>" style="text-align:left;width:100px;" id="freetry" name="freetry" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">免费用户赠送天数：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="<?php echo $freedays; ?>" style="text-align:left;width:100px;" id="freedays" name="freedays" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">每天登录赠送次数：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="<?php echo $freetryeveryday; ?>" style="text-align:left;width:100px;" id="freetryeveryday" name="freetryeveryday" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">分享赠送次数：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="<?php echo $freetryshare; ?>" style="text-align:left;width:100px;" id="freetryshare" name="freetryshare" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">新建对话引导欢迎词：</label>

                                <div class="col-lg-4">
                                    <textarea style="width:100%;height:100px;" id="welcomemessage" name="welcomemessage" class="bg-focus form-control" autoComplete="off" placeholder="支持HTML格式"><?php echo $welcomemessage; ?></textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">AI身份伪装预设话术：</label>

                                <div class="col-lg-4">
                                    <textarea style="width:100%;height:100px;" id="fakeprompt" name="fakeprompt" class="bg-focus form-control" autoComplete="off" placeholder="书写格式：请写两行内容，预设问题和AI预设回答各一行即可。"><?php echo $fakeprompt; ?></textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">首页顶部logo：</label>
                                <div class="col-lg-4" style="display:flex;">
                                    <input type="file" id="headlogo_file"><input type=hidden name="headlogo" id="headlogo" value="<?php echo $headlogo; ?>">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label"></label>
                                <div class="col-lg-4">
                                    <div id="result"><img id="headlogo_img" src="<?php echo $headlogo; ?>" style="height:50px;"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">首页中间logo：</label>
                                <div class="col-lg-4" style="display:flex;">
                                    <input type="file" id="contentlogo_file"><input type=hidden name="contentlogo" id="contentlogo" value="<?php echo $contentlogo; ?>">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label"></label>
                                <div class="col-lg-4">
                                    <div id="result"><img id="contentlogo_img" src="<?php echo $contentlogo; ?>" style="width:570px;"></div>
                                </div>
                            </div>


                            <div class="form-group" align="center" style="margin-right:150px;margin-top:15px">
                                <div class="col-lg-6 col-lg-offset-3">
                                    <button type="submit" class="btn btn-primary">确认设置</submit>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function() {
            function uploadImage(type) {
                var file_data = $('#' + type + '_file').prop('files')[0];
                var form_data = new FormData();
                form_data.append('file', file_data);
                form_data.append('type', type);

                $.ajax({
                    url: 'uploadimage.php',
                    dataType: 'text',
                    cache: false,
                    contentType: false,
                    processData: false,
                    data: form_data,
                    type: 'post',
                    success: function(response) {
                        $('#' + type + '_img').attr('src', response);
                        $('#' + type).val(response);
                    },
                    error: function(response) {
                        alert('上传失败，请重试');
                    }
                });
            }
            $('#headlogo_file').change(function() {
                uploadImage('headlogo');
            });

            $('#contentlogo_file').change(function() {
                uploadImage('contentlogo');
            });
        });

        function checkform() {
            if ((document.getElementById("fakeprompt").value != "") && (document.getElementById("fakeprompt").value.split("\n").length != 2)) {
                alert("AI身份伪装预设话术必须配置为两行或清空");
                return false;
            } else {
                return true;
            }
        }
    </script>
</body>

</html>