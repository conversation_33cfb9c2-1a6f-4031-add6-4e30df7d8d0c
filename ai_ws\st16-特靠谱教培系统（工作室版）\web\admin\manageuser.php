<?php
require_once('check_admin.php');
if (isset($_REQUEST["action"]) && ($_REQUEST["action"] == "changepass")) {
    require_once('mysqlconn.php');
    if ($conn->has('officer', ['id' => $_REQUEST['id']])) {
        $conn->update('officer', ['password' => md5(md5($_REQUEST["password"]) . "chatgpt@2023")], ['id' => $_REQUEST['id']]);
        echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');</script>";
    } else {
        echo "<html><head><meta charset=utf-8></head><body><script>alert('用户不存在！');parent.location.reload();</script>";
    }
    exit;
}

if (isset($_REQUEST["action"]) && ($_REQUEST["action"] == "delete")) {
    require_once('mysqlconn.php');
    if ($conn->count('officer', ['usertype' => 0]) == 1) {
        echo "<html><head><meta charset=utf-8></head><body><script>alert('请至少保留一个管理员!\\n   确保系统正常运行!');parent.location.reload();</script>";
        exit;
    }
    if ($conn->has('officer', ['id' => $_REQUEST['id']])) {
        $conn->delete('officer', ['id' => $_REQUEST['id']]);
        echo "<html><head><meta charset=utf-8></head><body><script>alert('删除成功！');parent.location.href='manageuser.php';</script>";
    } else {
        echo "<html><head><meta charset=utf-8></head><body><script>alert('用户不存在！');parent.location.reload();</script>";
    }
    exit;
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>维护管理员</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #forie td {
            height: 39px;
            padding: 8px;
            border: 1px solid #dddddd;
            font-size: 14px;
            line-height: 24px;
        }

        #forie tr:nth-child(odd) {
            background: #f9f9f9;
        }

        #forie input {
            height: 26px;
            border: 1px solid #a9a9a9;
            width: 100%;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>
</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div id="changepassdiv" style="display:none;z-index:9999;position:absolute;width:100%;height:100%;margin:0;">
            <iframe style="z-index:9999;position:absolute;width:100%;height:100%;background:black;opacity:0.8;margin:0;"></iframe>
            <div style="position:absolute;left:calc(50% - 300px);top:calc(50% - 200px);border-radius:10px;border:10px solid #F7F7F7;width:600px;height:400px;background:#F7F7F7;;z-index:9999;">
                <table style="width:100%;height:100%;border:0;">
                    <tr>
                        <td colspan=2 style="text-align:center;font-size:20px;vertical-align:bottom;font-weight:bold;">
                            修改密码
                        </td>
                    </tr>
                    <tr>
                        <td style="width:50%;text-align:center;font-size:20px;">请输入新密码：<br><br>请再次输入：</td>
                        <td style="width:50%;text-align:left;font-size:20px;">
                            <input onclick="this.select();" type=password size=20 id="pass1"><br><br>
                            <input onclick="this.select();" type=password size=20 id="pass2">
                            <input type=hidden id=userid>
                        </td>
                    </tr>
                    <tr>
                        <td colspan=2 style="text-align:center;font-size:20px;vertical-align:top;"><input type=button onclick="setpassword();" value="　确　认　">　　　　<input type=button onclick="$('#changepassdiv').fadeOut();" value="　取　消　"></td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">维护管理员</li>
            </ul>
        </div>

        <div class="main-content" style="margin:20px 0;">
            <div class="page-content">
                <div class="page-content-area">
                    <div class="row">
                        <div class="col-xs-12">
                            <button class="btn btn-primary" onclick="location.href='adduser.php';">添加管理员</button>
                            <div class="space-12"></div>
                            <table class="table table-striped table-bordered table-hover" id="forie">
                                <thead>
                                    <tr style="background:#f1f1f1;">
                                        <th class="text-center">登录ID</th>
                                        <th class="text-center">姓名</th>
                                        <th class="text-center">邮箱</th>
                                        <th class="text-center" style="width:120px;">电话</th>
                                        <th class="text-center" style="width:160px;">最后登录日期</th>
                                        <th class="text-center" style="width:80px;">登录次数</th>
                                        <th class="text-center">备注</th>
                                        <th class="text-center" style="width:220px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    require_once('mysqlconn.php');
                                    $result = $conn->select('officer', '*', ['usertype' => '0', 'ORDER' => 'id']);
                                    foreach ($result as $row) {
                                        if ($row["logintime"] == null) {
                                            $logintimes = array();
                                            $toalert = "该管理员未登录过";
                                        } else {
                                            $logintimes = array_reverse(explode(";", $row["logintime"]));
                                            $loginips = array_reverse(explode(";", $row["loginip"]));
                                            $toalert = "";
                                            for ($x = 0; $x < sizeof($logintimes); $x++) {
                                                $toalert = $toalert . '登录时间：' . $logintimes[$x] . ' 登录IP：' . $loginips[$x] . '\n';
                                            }
                                        }
                                    ?>
                                        <tr>
                                            <form id="form<?php echo $row["id"] ?>" method=post target="temp" action="updateofficer.php" onsubmit="checkandsubmit('<?php echo $row["id"] ?>');">
                                                <td align=" center"><?php echo $row["username"] ?></td>
                                                <td align="center"><input name=realname size=5 value="<?php echo $row["realname"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');">
                                                </td>
                                                <td><input name=email value="<?php echo $row["email"] ?>" style="ime-mode:disabled!important;-webkit-ime-mode:disabled!important;">
                                                </td>
                                                <td align="center"><input name=tel size=11 value="<?php echo $row["tel"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');">
                                                </td>
                                                <td align="center"><?php if (sizeof($logintimes) > 0) echo $logintimes[sizeof($logintimes) - 1] ?></td>
                                                <td align="center" style="cursor:pointer;" onclick="alert('<?php echo $toalert ?>');"><?php echo sizeof($logintimes) ?></td>

                                                <td align="center"><input name=memo value="<?php echo $row["memo"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');">
                                                </td>
                                                <td align="center"><input type=button style="width:80px;" onclick="changepasswd('<?php echo $row["id"] ?>');" value="修改密码">&nbsp;&nbsp;<input type=submit value="更新" style="width:45px;">&nbsp;&nbsp;<input type=button style="width:45px;" onclick="deleteid('<?php echo $row["id"] ?>');" value="删除"></td>

                                                <input name="id" id="id<?php echo $row["id"] ?>" type="hidden" value="<?php echo $row["id"] ?>">
                                            </form>
                                        </tr>
                                    <?php
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <script>
                function checkandsubmit(boxid) {
                    var newid = "form" + boxid;
                    document.getElementById('onthego').style.display = 'block';
                    document.getElementById(newid).submit();
                }

                function changepasswd(id) {
                    document.getElementById("userid").value = id;
                    document.getElementById("pass1").value = "";
                    document.getElementById("pass2").value = "";
                    $("#changepassdiv").fadeIn();
                    document.getElementById("pass1").focus();
                }

                function setpassword() {
                    newpass = document.getElementById("pass1").value;
                    if (newpass == document.getElementById("pass2").value) {
                        if (newpass.length < 6) {
                            alert("密码不能小于6位，请重新输入！");
                            document.getElementById("pass1").focus();
                            document.getElementById("pass1").select();
                        } else {
                            document.getElementById("result").src = "manageuser.php?action=changepass&id=" + document.getElementById("userid").value + "&password=" + md5(md5(newpass) + 'Libra');
                            $("#changepassdiv").fadeOut();
                        }
                    } else {
                        alert("两次密码输入不一致，请重新输入！");
                        document.getElementById("pass1").focus();
                        document.getElementById("pass1").select();
                    }
                }

                function deleteid(id) {
                    if (confirm("确认删除该管理员吗？")) {
                        document.getElementById("result").src = "manageuser.php?action=delete&id=" + id;
                    }
                }
            </script>
            <iframe style="display:none;" id=result name=temp></iframe>
</body>

</html>