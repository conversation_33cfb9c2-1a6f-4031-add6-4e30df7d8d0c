<?php
require_once('admin/mysqlconn.php');
$adminweixinid = $conn->get('main','adminweixinid',['id'=>1]);
if (isset($_GET["userrndstr"])) {
    $row = $conn->get('user','*',['rndstr'=>$_GET["userrndstr"]]);
    if (empty($row)) {
        exit;
    } else {
        $userid = $row["userid"];
        if (!empty($row['tel']) || !empty($row['email'])) {
            echo '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>支付成功</title><style>body{background-color:#f2f2f2;font-family:Arial,sans-serif;font-size:18px;color:#333;text-align:center;padding-top:50px}h1{font-size:48px;margin-bottom:20px}p{font-size:36px;margin-bottom:40px}</style></head><body><h1>感谢支持！</h1><p>您已完成支付，如充值失败请联系站长微信：' . $adminweixinid . '</p>';
            if (isset($_GET["from"])) {
                echo '<h1><a href="index.php" style="text-decoration:none;">回到首页</a></h1>';
            }
            exit;
        }
    }
}
if (isset($_GET["userid"])) {
    if (strpos($_GET["email_phone"], '@') !== false) {
        $conn->update('user',['email'=>$_GET['email_phone'],'password'=>md5(md5($_GET['password']) . "chatgpt@2023")],['userid'=>$_GET["userid"]]);
    } else {
        $conn->update('user',['tel'=>$_GET['email_phone'],'password'=>md5(md5($_GET['password']) . "chatgpt@2023")],['userid'=>$_GET["userid"]]);
    }
    echo '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>绑定成功</title><style>body{background-color:#f2f2f2;font-family:Arial,sans-serif;font-size:18px;color:#333;text-align:center;padding-top:50px}h1{font-size:48px;margin-bottom:20px}p{font-size:36px;margin-bottom:40px}</style></head><body><h1>谢谢配合！</h1><p>您已完成支付，如充值失败请联系站长微信：' . $adminweixinid . '</p>';
    if (isset($_GET["from"])) {
        echo '<h1><a href="index.php" style="text-decoration:none;">回到首页</a></h1>';
    }
    exit;
}

?>
<!DOCTYPE html>
<html>

<head>
    <title>支付成功</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0" />
    <style>
        body {
            background-color: #f2f2f2;
            font-family: Arial, sans-serif;
            font-size: 16px;
            color: #333;
        }

        .container {
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
        }

        h1 {
            text-align: center;
            font-size: 24px;
            margin-top: 0;
        }

        h2 {
            font-size: 18px;
            margin-top: 0;
        }

        form {
            margin-top: 20px;
        }

        label {
            display: block;
            margin-bottom: 15px;
            font-weight: bold;
        }

        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            margin-bottom: 20px;
            box-sizing: border-box;
            font-size: 14px;
        }

        button[type="submit"] {
            background-color: #4CAF50;
            color: #fff;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }

        button[type="submit"]:hover {
            background-color: #3e8e41;
        }
    </style>
    <link rel="stylesheet" href="css/layui.css">
    <script src="js/md5.js"></script>
    <script src="js/jquery.min.js"></script>
    <script src="js/layui.js"></script>
</head>

<body>
    <div class="container">
        <h1>感谢您的支持！</h1>
        <h2>为了防止由于微信屏蔽而无法访问本站或微信扫码无法登录，建议您绑定电子邮箱作为其他登录方式。</h2>
        <hr>
        <p style="margin-top:15px;font-weight:bold;font-size:16px;">用户ID: <?php echo $userid; ?></p>
        <form onsubmit="if (($('#email_phone').val()=='')||($('#password').val()=='')){layer.msg('请填写完整再提交，谢谢！');return false};layer.msg('提交中，请稍候...', {icon: 16,shade: 0.4,time: false});">
            <label for="email_phone">电子邮箱:</label>
            <input type="text" id="email_phone" name="email_phone" placeholder="请输入您的电子邮箱">
            <label for="password">密码:</label>
            <input type="password" id="password1" onchange="document.getElementById('password').value=md5(md5(this.value)+'Libra');" name="password1" placeholder="请输入密码">
            <input type=hidden name="password" id="password">
            <input type=hidden name="userid" value="<?php echo $userid; ?>">
            <?php if (isset($_GET["from"])) echo '<input type=hidden name="from" value="wx">'; ?>
            <div style="width:100%;text-align:center;"><button type="submit">确认绑定</button></div>
        </form>
    </div>
</body>

</html>