<?php
require_once('mysqlconn.php');
if (isset($_REQUEST["action"]) && ($_REQUEST["action"] == "sm")) {
    $conn->update('template',['template'=>$_REQUEST["template"]],['process'=>$_REQUEST['process']]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('设置成功！');</script></body></html>";
    exit;
}
require_once('check_admin.php');
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>通知模板设置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>


    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-print"></i> 系统管理</li>
                <li class="active">短信和邮件通知模版设置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px;">
            <div class="page-content-area">
                <div style="width:100%;">
                    <div class="space-6"></div>
                    <div class="col-xs-12">
                        <form class="form-horizontal" target=temp>
                            <input type=hidden name="action" value="sm">

                            <div class="form-group">
                                <label class="col-lg-3 control-label" style="font-size:16px;">业务流程：</label>

                                <div class="col-lg-8" style="text-align:left;">
                                    <?php
                                    $process = isset($_REQUEST["process"])?$_REQUEST["process"]:"REGISTER";
                                    if ($conn->has('template',['process'=>$process])) {
                                        $row = $conn->get('template','*',['process'=>$process]);
                                    ?>
                                        <select name=process style="width:220px;" onchange="location.href='setshortmessage.php?process='+this.value;">
                                            <option value="REGISTER" <?php if ($process == "REGISTER") echo "selected"; ?>>
                                                用户注册短信
                                            </option>
                                            <option value="RESETPASS" <?php if ($process == "RESETPASS") echo "selected"; ?>>
                                                重置密码短信
                                            </option>
                                            <option value="MAILREGISTER" <?php if ($process == "MAILREGISTER") echo "selected"; ?>>
                                                用户注册邮件
                                            </option>
                                            <option value="MAILRESETPASS" <?php if ($process == "MAILRESETPASS") echo "selected"; ?>>
                                                重置密码邮件
                                            </option>
                                        </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label" style="font-size:16px;">短信模板：</label>

                                <div class="col-lg-8" style="text-align:left;font-size:12px;">
                                    插入变量：<span onclick="document.getElementById('template').value+='{'+this.innerHTML+'}';" style="cursor:pointer;">登录名</span>
                                    &nbsp;&nbsp;<span onclick="document.getElementById('template').value+='{'+this.innerHTML+'}';" style="cursor:pointer;">姓名</span>
                                    &nbsp;&nbsp;<span onclick="document.getElementById('template').value+='{'+this.innerHTML+'}';" style="cursor:pointer;">电话</span>
                                    &nbsp;&nbsp;<span onclick="document.getElementById('template').value+='{'+this.innerHTML+'}';" style="cursor:pointer;">邮箱</span>
                                    &nbsp;&nbsp;<span onclick="document.getElementById('template').value+='{'+this.innerHTML+'}';" style="cursor:pointer;">验证码</span>
                                    <textarea name="template" id="template" style="width:100%;height:80px;"><?php echo $row["template"]; ?></textarea>
                                </div>
                            </div>
                        <?php
                                    }
                        ?>
                        <div class="form-group" align="center" style="padding:10px;">
                            <button type="submit" class="btn btn-primary">确认设置</button>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <iframe id=temp name=temp style="display:none;"></iframe>

</body>

</html>