<?php
/**
 * 用户控制器
 */

class UserController extends \Core\Controller
{
    /**
     * 登录页面
     */
    public function login()
    {
        // 如果已经登录，则重定向到首页
        if ($this->isLoggedIn()) {
            $this->redirect('/');
        }
        
        // 渲染登录页面
        $this->render('user/login', [
            'title' => '用户登录 - 特靠谱教培系统'
        ]);
    }
    
    /**
     * 处理登录请求
     */
    public function doLogin()
    {
        // 获取请求参数
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');
        $verifycode = $this->request->getPost('verifycode');
        
        // 验证参数
        if (empty($email) || empty($password) || empty($verifycode)) {
            $this->jsonResponse([
                'success' => false,
                'message' => '请填写完整的登录信息'
            ]);
            return;
        }
        
        // 验证验证码
        if (!$this->validateVerifyCode($verifycode)) {
            $this->jsonResponse([
                'success' => false,
                'message' => '验证码错误'
            ]);
            return;
        }
        
        // 验证用户
        $userModel = new \Models\UserModel();
        $user = $userModel->findByEmail($email);
        
        if (!$user || !password_verify($password, $user['password'])) {
            $this->jsonResponse([
                'success' => false,
                'message' => '邮箱或密码错误'
            ]);
            return;
        }
        
        // 检查用户状态
        if ($user['status'] != 1) {
            $this->jsonResponse([
                'success' => false,
                'message' => '账号已被禁用'
            ]);
            return;
        }
        
        // 更新登录信息
        $rndstr = md5(uniqid(mt_rand(), true));
        $ismobile = $this->request->isMobile() ? 1 : 0;
        $loginip = $this->request->getClientIp();
        $logintime = time();
        
        $userModel->updateLoginInfo($user['id'], [
            'rndstr' => $rndstr,
            'ismobile' => $ismobile,
            'loginip' => $loginip,
            'logintime' => $logintime
        ]);
        
        // 设置会话
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['rndstr'] = $rndstr;
        
        // 返回成功
        $this->jsonResponse([
            'success' => true,
            'message' => '登录成功',
            'redirect' => '/'
        ]);
    }
    
    /**
     * 微信登录
     */
    public function wxLogin()
    {
        // 获取openid
        $openid = $this->request->getGet('openid');
        
        if (empty($openid)) {
            $this->render('error/index', [
                'title' => '错误 - 特靠谱教培系统',
                'message' => '无效的微信登录请求'
            ]);
            return;
        }
        
        // 查找用户
        $userModel = new \Models\UserModel();
        $user = $userModel->findByOpenid($openid);
        
        // 如果用户不存在，则创建新用户
        if (!$user) {
            // 获取微信用户信息
            // 这里需要调用微信API获取用户信息
            $wxUserInfo = $this->getWxUserInfo($openid);
            
            // 创建新用户
            $userId = $userModel->createWxUser([
                'openid' => $openid,
                'name' => $wxUserInfo['nickname'] ?? '微信用户',
                'avatar' => $wxUserInfo['headimgurl'] ?? '',
                'status' => 1,
                'role' => 'student', // 默认为学生角色
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            $user = $userModel->findById($userId);
        }
        
        // 检查用户状态
        if ($user['status'] != 1) {
            $this->render('error/index', [
                'title' => '错误 - 特靠谱教培系统',
                'message' => '账号已被禁用'
            ]);
            return;
        }
        
        // 更新登录信息
        $rndstr = md5(uniqid(mt_rand(), true));
        $ismobile = $this->request->isMobile() ? 1 : 0;
        $loginip = $this->request->getClientIp();
        $logintime = time();
        
        $userModel->updateLoginInfo($user['id'], [
            'rndstr' => $rndstr,
            'ismobile' => $ismobile,
            'loginip' => $loginip,
            'logintime' => $logintime
        ]);
        
        // 设置会话
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'] ?? '';
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['rndstr'] = $rndstr;
        
        // 重定向到首页或指定页面
        $redirectUrl = $this->request->getGet('redirect') ?? '/';
        $this->redirect($redirectUrl);
    }
    
    /**
     * 注销登录
     */
    public function logout()
    {
        // 清除会话
        session_destroy();
        
        // 重定向到登录页
        $this->redirect('/login');
    }
    
    /**
     * 注册页面
     */
    public function register()
    {
        // 如果已经登录，则重定向到首页
        if ($this->isLoggedIn()) {
            $this->redirect('/');
        }
        
        // 渲染注册页面
        $this->render('user/register', [
            'title' => '用户注册 - 特靠谱教培系统'
        ]);
    }
    
    /**
     * 处理注册请求
     */
    public function doRegister()
    {
        // 获取请求参数
        $name = $this->request->getPost('name');
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');
        $confirmPassword = $this->request->getPost('confirm_password');
        $verifycode = $this->request->getPost('verifycode');
        
        // 验证参数
        if (empty($name) || empty($email) || empty($password) || empty($confirmPassword) || empty($verifycode)) {
            $this->jsonResponse([
                'success' => false,
                'message' => '请填写完整的注册信息'
            ]);
            return;
        }
        
        // 验证密码
        if ($password !== $confirmPassword) {
            $this->jsonResponse([
                'success' => false,
                'message' => '两次输入的密码不一致'
            ]);
            return;
        }
        
        // 验证验证码
        if (!$this->validateVerifyCode($verifycode)) {
            $this->jsonResponse([
                'success' => false,
                'message' => '验证码错误'
            ]);
            return;
        }
        
        // 验证邮箱是否已存在
        $userModel = new \Models\UserModel();
        if ($userModel->emailExists($email)) {
            $this->jsonResponse([
                'success' => false,
                'message' => '该邮箱已被注册'
            ]);
            return;
        }
        
        // 创建用户
        $userId = $userModel->create([
            'name' => $name,
            'email' => $email,
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'status' => 1,
            'role' => 'student', // 默认为学生角色
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        if (!$userId) {
            $this->jsonResponse([
                'success' => false,
                'message' => '注册失败，请稍后再试'
            ]);
            return;
        }
        
        // 返回成功
        $this->jsonResponse([
            'success' => true,
            'message' => '注册成功，请登录',
            'redirect' => '/login'
        ]);
    }
    
    /**
     * 验证验证码
     */
    private function validateVerifyCode($code)
    {
        // 这里简化处理，实际应该验证session中的验证码
        return isset($_SESSION['verifycode']) && strtolower($_SESSION['verifycode']) === strtolower($code);
    }
    
    /**
     * 获取微信用户信息
     */
    private function getWxUserInfo($openid)
    {
        // 这里需要调用微信API获取用户信息
        // 简化处理，返回空数组
        return [];
    }
}