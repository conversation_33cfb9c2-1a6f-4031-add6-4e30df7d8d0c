<?php
if ((isset($_GET["s"])) && (isset($_GET["p"]))) {
    require_once('admin/mysqlconn.php');
    $row = $conn->get('cardtype','*',['id'=>$_GET["p"]]);
    $cardname = $row["cardname"];
    $price = $row["price"];
    $userid = $conn->get('user','userid',['rndstr'=>$_GET["s"]]);
    $attachid = $_SERVER['HTTP_HOST'] . "_" . $userid . "_" . time();
    $conn->insert('wxpaylist',['userid'=>$userid,'cardtype'=>$_GET["p"],'ordertime'=>date('Y-m-d H:i:s'),'clientip'=>$_SERVER["REMOTE_ADDR"],'attachid'=>$attachid]);
    if (!$conn->error) {
        $payurl = $conn->get('main','payment_url',['id'=>1]);
        if (isset($_REQUEST["m"])) {
            header("Location:" . $payurl . "?cardtype=" . $_GET["p"] . "&cardname=" . $cardname . "&price=" . $price . "&attachid=" . $attachid . "&returnurl=http%3A%2F%2F" . $_SERVER['HTTP_HOST'] . "%2Fwxpayreturn.php%3Ffrom%3Dwx%26userrndstr%3D" . $_REQUEST["s"]);
        } else {
            header("Location:" . $payurl . "?cardtype=" . $_GET["p"] . "&cardname=" . $cardname . "&price=" . $price . "&attachid=" . $attachid . "&returnurl=http%3A%2F%2F" . $_SERVER['HTTP_HOST'] . "%2Fwxpayreturn.php%3Fuserrndstr%3D" . $_REQUEST["s"]);
        }
    } else {
        echo $conn->error;
        
    }
}
