<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? '特靠谱教培系统' ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">特靠谱教培系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <?php if (isset($_SESSION['user_role']) && ($_SESSION['user_role'] == 'admin' || $_SESSION['user_role'] == 'teacher')): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="/teacher"><i class="fas fa-chalkboard-teacher"></i> 教师管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/student"><i class="fas fa-user-graduate"></i> 学生管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/course"><i class="fas fa-book"></i> 课程管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/classroom"><i class="fas fa-door-open"></i> 教室管理</a>
                    </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <a class="nav-link" href="/schedule"><i class="fas fa-calendar-alt"></i> 课表查询</a>
                    </li>
                    <?php if (isset($_SESSION['user_id'])): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="/leave"><i class="fas fa-calendar-times"></i> 请假管理</a>
                    </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isset($_SESSION['user_id'])): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= htmlspecialchars($_SESSION['user_name']) ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/profile"><i class="fas fa-id-card"></i> 个人信息</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                        </ul>
                    </li>
                    <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="/login"><i class="fas fa-sign-in-alt"></i> 登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/register"><i class="fas fa-user-plus"></i> 注册</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <?= $content ?>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light text-center text-lg-start mt-5">
        <div class="container p-4">
            <div class="row">
                <div class="col-lg-6 col-md-12 mb-4 mb-md-0">
                    <h5 class="text-uppercase">特靠谱教培系统</h5>
                    <p>提供专业的教育培训管理解决方案，帮助教育机构高效管理课程、教师、学生和教室资源。</p>
                </div>
                <div class="col-lg-3 col-md-6 mb-4 mb-md-0">
                    <h5 class="text-uppercase">链接</h5>
                    <ul class="list-unstyled mb-0">
                        <li><a href="/" class="text-dark">首页</a></li>
                        <li><a href="/schedule" class="text-dark">课表查询</a></li>
                        <li><a href="/about" class="text-dark">关于我们</a></li>
                        <li><a href="/contact" class="text-dark">联系我们</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4 mb-md-0">
                    <h5 class="text-uppercase">联系方式</h5>
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-map-marker-alt"></i> 上海市浦东新区</li>
                        <li><i class="fas fa-phone"></i> 021-12345678</li>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="text-center p-3" style="background-color: rgba(0, 0, 0, 0.05);">
            © <?= date('Y') ?> 特靠谱教培系统 - 版权所有
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="/js/script.js"></script>
</body>
</html>