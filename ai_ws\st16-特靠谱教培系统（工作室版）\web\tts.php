<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: audio/mp3");
header("X-Accel-Buffering: no");
set_time_limit(0);
require_once('admin/mysqlconn.php');
if (isset($_POST["userrndstr"])) {
    $userrndstr = $_POST["userrndstr"];
} else {
    $userrndstr = $_GET["userrndstr"];
}
if (isset($_POST["msg"])) {
    $msg = $_POST["msg"];
} else {
    $msg = $_GET["msg"];
}
if (($userrndstr != 'admin') && (!$conn->has('user', ['rndstr' => $userrndstr]))) {
    exit;
}

require_once('class_websocketclient.php');

$row = $conn->get('tts', '*', ['id' => 1]);
$ttsmodel = $row["ttsmodel"];
$microsoftapiaddress = $row["microsoftapiaddress"];
$microsoftrole = $row["microsoftrole"];
$microsoftspeed = $row["microsoftspeed"];
$microsoftvolume = $row["microsoftvolume"];
$xunfeiapikey = $row["xunfeiapikey"];
$xunfeiapiaddress = $row["xunfeiapiaddress"];
$xunfeiapikey = $row["xunfeiapikey"];
$xunfeiapiaddress = $row["xunfeiapiaddress"];
$xunfeirole = $row["xunfeirole"];
$xunfeipitch = $row["xunfeipitch"];
$xunfeispeed = $row["xunfeispeed"];
$xunfeivolume = $row["xunfeivolume"];
$openaiapikey = $row["openaiapikey"];
$openaiapiaddress = $row["openaiapiaddress"];
$openaimodel = $row["openaimodel"];
$openairole = $row["openairole"];
$openaispeed = $row["openaispeed"];
$row = $conn->get('main', '*', ['id' => 1]);
$proxyaddress = $row["proxyaddress"];
$license = $row["license"];

$prompt = urldecode($msg);
switch ($ttsmodel) {
    case "openai":
        openaitts();
        break;
    case "xunfei":
        xunfeitts();
        break;
    case "microsoft":
        microsofttts();
        break;
    default:
        echo "TTS model is not selected.";
}

function microsofttts()
{
    global $prompt, $license, $microsoftapiaddress, $microsoftrole, $microsoftspeed, $microsoftvolume;
    $postdata = [
        "input" => $prompt,
        "role" => $microsoftrole,
        "volume" => $microsoftvolume,
        "speed" => $microsoftspeed
    ];
    $postdata = json_encode($postdata);
    $headers  = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $license
    ];
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_URL, $microsoftapiaddress);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($ch, $data) {
        echo $data; // 实时输出音频流数据
        return strlen($data);
    });
    curl_setopt($ch, CURLOPT_AUTOREFERER, true);
    curl_exec($ch);
    curl_close($ch);
}

function xunfeitts()
{
    global $prompt, $xunfeiapikey, $xunfeiapiaddress, $xunfeirole, $xunfeipitch, $xunfeispeed, $xunfeivolume;
    function assembleAuthUrl($addr, $apiSecret, $apiKey)
    {
        $ul = parse_url($addr);
        if (($apiKey . $apiSecret == "") || ($ul === false)) {
            return $addr;
        }
        $timestamp = time();
        $rfc1123_format = gmdate("D, d M Y H:i:s \G\M\T", $timestamp);
        $signString = array("host: " . $ul["host"], "date: " . $rfc1123_format, "GET " . $ul["path"] . " HTTP/1.1");
        $sgin = implode("\n", $signString);
        $sha = hash_hmac('sha256', $sgin, $apiSecret, true);
        $signature_sha_base64 = base64_encode($sha);
        $authUrl = 'api_key="' . $apiKey . '", algorithm="hmac-sha256", headers="host date request-line", signature="' . $signature_sha_base64 . '"';
        $authAddr = $addr . '?' . http_build_query(array(
            'host' => $ul['host'],
            'date' => $rfc1123_format,
            'authorization' => base64_encode($authUrl),
        ));
        return $authAddr;
    }
    $apikeyarray = explode(",", $xunfeiapikey);
    $authUrl = assembleAuthUrl($xunfeiapiaddress, $apikeyarray[1], $apikeyarray[2]);
    try {
        $ws = new WebSocketClient($authUrl);
        $requestData = '{"common": {"app_id": "' . $apikeyarray[0] . '"},"business": {"aue": "lame", "sfl":1, "tte":"UTF8", "vcn": "' . $xunfeirole . '", "pitch": ' . $xunfeipitch . ', "volume": ' . $xunfeivolume . ', "speed": ' . $xunfeispeed . '}, "data": {"status": 2, "text": "' . base64_encode($prompt) . '"}}';
        $ws->ping();
        $ws->send($requestData);
        while ($frame = $ws->recv()) {
            $json = json_decode($frame->playload);
            if (!isset($json->data)) {
                break;
            }
            if ((isset($json->data)) && (isset($json->data->audio))) {
                echo base64_decode($json->data->audio);
            }
            flush();
            if ((isset($json->data)) && (isset($json->data->status)) && ($json->data->status == 2)) {
                break;
            }
        }
        $ws->close();
    } catch (Exception $e) {
        echo 'data: {"error":{"code":"unknown","message":"' . urlencode($e->getMessage()) . '"}}' . "\n\n";
    }
}

function openaitts()
{
    global $prompt, $openaiapikey, $openaiapiaddress, $openaimodel, $openairole, $openaispeed, $proxyaddress;
    $postdata = [
        "model" => $openaimodel,
        "input" => $prompt,
        "voice" => $openairole,
        "speed" => $openaispeed
    ];
    $postdata = json_encode($postdata);
    $headers  = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $openaiapikey
    ];
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_URL, $openaiapiaddress);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($ch, $data) {
        echo $data; // 实时输出音频流数据
        return strlen($data);
    });
    curl_setopt($ch, CURLOPT_AUTOREFERER, true);
    if (!empty($proxyaddress)) {
        curl_setopt($ch, CURLOPT_PROXY, $proxyaddress);
    }
    $responsedata = curl_exec($ch);
    curl_close($ch);
    echo $responsedata;
}
