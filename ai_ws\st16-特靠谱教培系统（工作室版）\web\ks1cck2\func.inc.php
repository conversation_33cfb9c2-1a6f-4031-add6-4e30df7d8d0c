<?php

// functions.php

function is_logged_in() {
    //return isset($_SESSION[SESSION_KEY]['user']['id']);
    if (!isset($_SESSION[SESSION_KEY]['user']['id']) || !in_array($_SESSION[SESSION_KEY]['user']['role'], ['user', 'admin'])) {
        echo __METHOD__.'() '.SESSION_KEY;
        return false;
    }
    return true;
}

function is_admin_in() {
    if (!isset($_SESSION[SESSION_KEY]['user']['id']) || $_SESSION[SESSION_KEY]['user']['role']!='admin') {
        return false;
    }
    return true;
}

function get_current_user_group() {
    if (isset($_SESSION[SESSION_KEY]['user']['group_id']) ) {
        $group_id=intval($_SESSION[SESSION_KEY]['user']['group_id']);
        if($group_id>0){
             return $group_id;
        }
       
    }
    //return $_SESSION['group_id'] ?? null;
    return false;
}

function get_table_name($tbl_name){
    global $tablePrefix;
    return $tablePrefix.$tbl_name;
}

function get_last_insert_id(){
    return last_id();
}

define('PASSWD_SALT', '2D39FF64CB4A48A048258BB400518CDB');
function user_login($username, $password1) {

// 如果没有找到记录，添加一条默认记录
    $user_id = web_user_login($username, $password1);
    if ($user_id) {
        if(user_login_xp($user_id)){
            header('Location: index.php?from='.__METHOD__);    
            exit;
            echo 'login ok';
        }else{
            echo 'login miss';
            //return ;
        }
    }else{
        echo 'user miss';
    }
    die();

    $table_name=get_table_name('users');

    $password = md5($password1. PASSWD_SALT);
    $sql = "
    SELECT
cck_users.id,
cck_users.username,
cck_users.role,
cck_users.wechat_name,
cck_users.wechat_openid,
cck_users.email,
cck_users.phone,
cck_users.public_account_name
FROM
$table_name as cck_users
 WHERE username = ?s AND password = ?s LIMIT 1";
    /*
    echo $password1;
    echo ' --- ';
    echo $password;
    */
    //$sql = "SELECT * FROM cck_users WHERE username = ?s LIMIT 1";
    $sql = prepare($sql, array($username, $password));
    $user = get_line($sql);
    //var_dump($user);
    if ($user) {
        $_SESSION[SESSION_KEY] = [
            'user' => [
                'id' => $user['user_id'], // 用户ID
                'username' => $user['username'], // 用户名
                'role' => $user['role'], // 用户角色（如'user'或'admin'）
                'wx_name' => $user['wechat_name'], // 微信用户名称
                'openid' => $user['wechat_openid'], // 微信OpenID
                'email' => $user['email'], // 用户邮箱
                'phone' => $user['phone'], // 用户手机号码
                'p_a_name' => $user['public_account_name'] // 公众号名称
                ,'group_id' => $user['group_id']
            ]
        ];
        return true;
    }else{
        // 如果没有找到记录，添加一条默认记录
        $user_id = web_user_login($username, $password1);
        if ($user_id) {
            if(user_login_xp($user_id)){
                header('Location: index.php');    
                exit;
            }else{
                return ;
            }
        }
            
    }
    return false;
}

function web_user_login($username, $password1){
    $table_name='user'; //get_table_name('users');

    $password2=md5(md5($password1) . "Libra");
    $password=md5(md5($password2) . "chatgpt@2023");
    
    $sql = "  SELECT *  FROM $table_name  WHERE email = ?s AND password = ?s LIMIT 1";
    $sql = prepare($sql, array($username, $password));
    $row = get_line($sql);
    
    if (empty($row)) {
        echo '{"success":false,"message":"用户名或密码错误"}';
        return false;
    } else {
        if ($row["isforbidden"]) {
            echo '{"success":false,"message":"该账号已被封禁"}';
            return false;
        } else {
            //$conn->update('user', ['rndstr' => $userrndstr, 'ismobile' => 0, 'loginip' => $row['loginip'] . ";" . $_SERVER["REMOTE_ADDR"], 'logintime' => $row['logintime'] . ";" . date('Y-m-d H:i:s')], ['id' => $row['id']]);
            //echo '{"success":true}';
            //var_dump($row);
            return $row["id"];
        }
    }
    return false;
}

// 登录成功
function login_success($id, $name, $group,$email,$dataArr=null)
{
    if(user_login_xp($id)){
        header('Location: index.php?from='.__METHOD__);    
        exit;
    }else{
        return ;
    }
     
    // 初始化会话数据结构
    $_SESSION[SESSION_KEY] = [
        'user' => [

            'id' => $dataArr['id'], // 用户ID
            'username' => $dataArr['username'], // 用户名
            'role' => $dataArr['role'], // 用户角色（如'user'或'admin'）
            'wx_name' => $dataArr['wechat_name'], // 微信用户名称
            'openid' => $dataArr['wechat_openid'], // 微信OpenID
            'email' => $dataArr['email'], // 用户邮箱
            'phone' => $dataArr['phone'], // 用户手机号码
            'p_a_name' => $dataArr['public_account_name'] // 公众号名称
            ,'group_id' => $dataArr['group_id']
        ]
    ];
    
    //var_dump($_SESSION);    exit;
    /*    
    // 根据用户角色跳转到不同的页面
    switch ($_SESSION[SESSION_KEY]['user']['role']) {
        case 'user':
            header('Location: dashboard.php');
            break;
        case 'admin':
            header('Location: admin.php');
            break;
        default:
            header('Location: index.php');
            break;
    }
    */
    header('Location: index.php');
    
    exit;

}

function user_login_xp_v1($user_id) {

    $table_name=get_table_name('users');
    $sql = "
    SELECT
cck_users.id,
cck_users.username,
cck_users.role,
cck_users.wechat_name,
cck_users.wechat_openid,
cck_users.email,
cck_users.phone,
cck_users.public_account_name,
cck_users.group_id
FROM 
$table_name as cck_users
 WHERE id = ?s  LIMIT 1";
    
    
    $sql = prepare($sql, array($user_id));

    if(DEBUG_MODE) echo __METHOD__.'() sql='.$sql.'<br>';

    $user = get_line($sql);
    //var_dump($user);
    if ($user) {
        $_SESSION[SESSION_KEY] = [
            'user' => [
                'id' => $user['id'], // 用户ID
                'username' => $user['username'], // 用户名
                'role' => $user['role'], // 用户角色（如'user'或'admin'）
                'wx_name' => $user['wechat_name'], // 微信用户名称
                'openid' => $user['wechat_openid'], // 微信OpenID
                'email' => $user['email'], // 用户邮箱
                'phone' => $user['phone'], // 用户手机号码
                'p_a_name' => $user['public_account_name'] // 公众号名称
                ,'group_id' => $user['group_id']
            ]
        ];
        return true;
    }
    return false;
}

function user_login_xp($user_id) {
    $table_name = get_table_name('users');
    $sql = "
    SELECT
        cck_users.id,
        cck_users.user_id,
        cck_users.username,
        cck_users.role,
        cck_users.wechat_name,
        cck_users.wechat_openid,
        cck_users.email,
        cck_users.phone,
        cck_users.public_account_name,
        cck_users.group_id
    FROM 
        $table_name as cck_users
    WHERE 
        user_id = ?s  
    LIMIT 1";

    $sql = prepare($sql, array($user_id));

    if (DEBUG_MODE) echo __METHOD__ . '() sql=' . $sql . '<br>';

    $user = get_line($sql);

    if ($user) {
        $_SESSION[SESSION_KEY] = [
            'user' => [
                'id' => $user['user_id'], // 用户ID
                'username' => $user['username'], // 用户名
                'role' => $user['role'], // 用户角色（如'user'或'admin'）
                'wx_name' => $user['wechat_name'], // 微信用户名称
                'openid' => $user['wechat_openid'], // 微信OpenID
                'email' => $user['email'], // 用户邮箱
                'phone' => $user['phone'], // 用户手机号码
                'p_a_name' => $user['public_account_name'], // 公众号名称
                'group_id' => $user['group_id']
            ]
        ];
        return true;
    } else {
        // 如果没有找到记录，添加一条默认记录
        $result = add_default_user_info($user_id);
        if ($result) {
            // 插入成功后，重新查询并设置会话
            $user = get_line($sql); // 重新查询
            if ($user) {
                $_SESSION[SESSION_KEY] = [
                    'user' => [
                        'id' => $user_id, // 用户ID
                        'username' => $user['username'], // 用户名
                        'role' => $user['role'], // 用户角色（如'user'或'admin'）
                        'wx_name' => $user['wechat_name'], // 微信用户名称
                        'openid' => $user['wechat_openid'], // 微信OpenID
                        'email' => $user['email'], // 用户邮箱
                        'phone' => $user['phone'], // 用户手机号码
                        'p_a_name' => $user['public_account_name'], // 公众号名称
                        'group_id' => $user['group_id']
                    ]
                ];
                return true;
            }
        }
        return false;
    }
}


function add_default_user_info($user_id) {
    $table_name = get_table_name('users');
   
    // 如果没有找到记录，添加一条默认记录
    $default_user = [
        'user_id' => $user_id,
        'public_account_name' => '默认公众号',
        'username' => 'default_user',
        'password' => '', 
        'role' => 'user',
        'wechat_name' => '默认微信用户',
        'wechat_openid' => 'default_openid',
        'email' => '<EMAIL>',
        'phone' => '***********',
        'group_id' => 0
    ];

    $insert_sql = "
    INSERT INTO $table_name (
        user_id,
        public_account_name,
        username,
        password,
        role,
        wechat_name,
        wechat_openid,
        email,
        phone,            
        group_id
    ) VALUES (
        ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?s
    )";

    $insert_sql = prepare($insert_sql, array(
        $user_id,
        $default_user['public_account_name'],
        $default_user['username'],
        $default_user['password'],
        $default_user['role'],
        $default_user['wechat_name'],
        $default_user['wechat_openid'],
        $default_user['email'],
        $default_user['phone'],            
        $default_user['group_id']
    ));

    if (DEBUG_MODE) echo __METHOD__ . '() insert_sql=' . $insert_sql . '<br>';

    return  run_sql($insert_sql);        
}

// 提交新文章
function submit_article($user_id, $link) {

    $tmpHtm = fetch_link_html($link); // 获取链接的 HTML 内容
    $title = fetch_pub_title_from_html($tmpHtm); // 获取链接标题
    $title2 = fetch_title_from_script($tmpHtm); // 获取 $title2

    if (!empty($title2)) {
        $title = '【' . $title2 . '】' . $title; // 将 $title2 添加到标题中
    }

    $table_name=get_table_name('users');

    // 检查是否存在用户记录，如果不存在则插入新记录
    $sql_check = "SELECT COUNT(*) AS count FROM $table_name WHERE id = ?i";
    $sql_check = prepare($sql_check, array($user_id));
    if(DEBUG_MODE) echo __METHOD__.'() sql_check='.$sql_check.'<br>';
    $result1 = get_var($sql_check);

    if ($result1 && intval($result1) > 0) {
        // 用户记录存在，更新公众号名称
        $sql_update = "UPDATE $table_name SET public_account_name = ?s WHERE id = ?i";
        $sql_update = prepare($sql_update, array($title2, $user_id));
        if(DEBUG_MODE) echo __METHOD__.'() sql_update='.$sql_update.'<br>';
        run_sql($sql_update);
    } else {
        // 用户记录不存在，插入新记录
        //$sql_insert = "INSERT INTO cck_users (id, public_account_name) VALUES (?i, ?s)";
        //$sql_insert = prepare($sql_insert, array($user_id, $title2));
        $sql_insert = "INSERT INTO `$table_name` (`id`,
    `username`, 
    `password`, 
    `role`, 
    `wechat_name`, 
    `wechat_openid`, 
    `email`, 
    `phone`, 
    `public_account_name`
) VALUES (?s,
    ?s,
    '',
    'user',
    NULL,
    NULL,
    '',
    '',
    ?s
);";
        $sql_insert = prepare($sql_insert, array($user_id,$_SESSION['username'],$title2));
        if(DEBUG_MODE) echo __METHOD__.'() sql_insert='.$sql_insert.'<br>';
        run_sql($sql_insert);
    }

    // 插入文章记录
    $group_id=$_SESSION[SESSION_KEY]['user']['group_id']??null;
    if($group_id!==null){
        $mode='group';
    }else{
        $mode='free';
        $group_id=0;
    }

    $table_name=get_table_name('links');

    $sql = "INSERT INTO $table_name (user_id, url, title, mode,group_id, created_at) VALUES (?i, ?s, ?s, ?s, ?s, NOW())";
    $sql = prepare($sql, array($user_id, $link, $title,$mode,$group_id));
    if(DEBUG_MODE) echo __METHOD__.'() sql='.$sql.'<br>';
    return run_sql($sql);
}



function get_unchecked_articles($user_id) {

    return get_unchecked_articles_by_group($user_id, 0);

    $tbl_a=get_table_name('links');
    $tbl_u=get_table_name('users');
    $tbl_lv=get_table_name('link_views');
    $sql = "
        SELECT a.link_id as id, a.url,a.url as link, a.title, u.username ,u.user_id
        FROM $tbl_a a 
        JOIN $tbl_u u ON a.user_id = u.user_id AND u.user_id is not null AND a.user_id is not null
        LEFT JOIN $tbl_lv c ON a.user_id = c.user_id AND a.user_id = ?i AND a.link_id=c.link_id 
        WHERE c.link_id IS NULL
        ORDER BY a.created_at ASC
        LIMIT 10
    ";
    $sql = prepare($sql, array($user_id));
    return get_data($sql);
}


function get_unchecked_articles_by_group($user_id, $group_id) {
    $tbl_a = get_table_name('links');
    $tbl_u = get_table_name('users');
    $tbl_lv = get_table_name('link_views');
    $sql = "
        SELECT a.link_id as id, a.url, a.url as link, a.title, u.username ,u.user_id
        FROM $tbl_a a 
        JOIN $tbl_u u ON a.user_id = u.user_id AND u.user_id is not null AND a.user_id is not null
        LEFT JOIN $tbl_lv c ON a.link_id = c.link_id AND c.user_id = ?i
        WHERE c.link_id IS NULL
          AND a.group_id = ?i
        ORDER BY a.created_at ASC
        LIMIT 10
    ";
    $sql = prepare($sql, array($user_id, $group_id));
    return get_data($sql);
}
/*
$limit = 10;  // 每页显示 10 条记录
$offset = 0;  // 第一页
$results = get_all_checks($limit, $offset);

// 获取第二页数据
$offset = 10;  // 跳过前 10 条记录
$results = get_all_checks($limit, $offset);
*/

// 获取分页参数
$limit = 10; // 每页显示的记录数
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1; // 当前页码，默认为第1页
$offset = ($page - 1) * $limit; // 计算跳过的记录数

function get_all_checks($limit = 10, $offset = 0) {
    $sql = "
        SELECT c.checked_at, u.username AS checker, a.link 
        FROM cck_checks c 
        JOIN cck_users u ON c.user_id = u.id 
        JOIN cck_articles a ON c.article_id = a.id
        ORDER BY c.checked_at DESC        
    ";
    $sql .=' LIMIT '.intval($limit).' OFFSET '.intval($offset);
    //$sql = prepare($sql, array($limit, $offset));

    //echo $sql; 
    return get_data($sql);
}

function record_check($user_id, $article_id, $read_count,$duration=1) {
    $today1 = std_fmt_now();
    $table_name=get_table_name('link_views');
    $sql = "INSERT INTO $table_name (user_id, link_id, read_count,viewed_at,duration) VALUES (?s, ?s, ?s, ?s,?s)";
    $sql = prepare($sql, array($user_id, $article_id, $read_count,$today1,$duration));
    if(DEBUG_MODE) hid_var_dump($sql)  ;
    return run_sql($sql);
}

function record_check2($user_id, $article_id, $read_count,$duration,$version,$remark) {
    $today1 = std_fmt_now();
    $table_name=get_table_name('link_views');
    $sql = "INSERT INTO $table_name (user_id, link_id, read_count,viewed_at,duration,version,remark) VALUES (?s, ?s, ?s, ?s,?s, ?s,?s)";
    $sql = prepare($sql, array($user_id, $article_id, $read_count,$today1,$duration,$version,$remark));
    if(DEBUG_MODE) hid_var_dump($sql)  ;
    return run_sql($sql);
}


//cck1_link_views
function get_today_submissions_checks($user_id) {
    $table_name=get_table_name('link_views');
    $today = date('Y-m-d'); // 当天日期
    $sql = "
        SELECT COUNT(*) AS count
        FROM $table_name
        WHERE user_id = ?i
        AND viewed_at like ?s
    ";
    $sql = prepare($sql, array($user_id,$today.'%'));
    //if(DEBUG_MODE) hid_        var_dump($sql)  ;
    $result = get_line($sql);
    return $result['count'] ?? 0;
}

function get_today_submitted_links($user_id) {
    $table_name=get_table_name('links');
    $today = date('Y-m-d'); // 当天日期
    $sql = "
        SELECT link_id as id,link_id,url, created_at,title
        FROM $table_name 
        WHERE user_id = ?i
        AND created_at like ?s
    ";
    $sql = prepare($sql, array($user_id,$today.'%'));
    //if(DEBUG_MODE) hid_    var_dump($sql)  ;
    return get_data($sql);
}

//cck1_links
function get_cck_links_count_all() {
    $table_name=get_table_name('links');
    $sql = "
        SELECT COUNT(*) as num FROM $table_name
    ";
    //if(DEBUG_MODE) echo __METHOD__.'() sql='.$sql.'<br>';
    return get_var($sql);
}

function get_cck_links_count_mode($free_mode=true) {
    $table_name=get_table_name('links');

    $sql = "
        SELECT COUNT(*) as num FROM $table_name WHERE mode =?s 
    ";

    if($free_mode){
        $sql = prepare($sql, array('free'));
    }else{
        $sql = prepare($sql, array('group'));
    }
    //if(DEBUG_MODE) echo __METHOD__.'() sql='.$sql.'<br>';
    return get_var($sql);
}

//cck1_link_views
function get_cck_link_views_count() {
    $table_name=get_table_name('link_views');
    $sql = "
        SELECT COUNT(*) as num FROM $table_name
    ";
    //if(DEBUG_MODE) echo __METHOD__.'() sql='.$sql.'<br>';
    return get_var($sql);
}

//cck1_users
function get_cck_users_count() {
    $table_name=get_table_name('users');
    $sql = "
        SELECT COUNT(*) as num FROM $table_name
    ";
    //if(DEBUG_MODE) echo __METHOD__.'() sql='.$sql.'<br>';
    return get_var($sql);
}

function get_articles_viewed_by_others($user_id) {
    
    $sql = "
        SELECT a.id, a.link, u.username
        FROM cck_articles a
        JOIN cck_users u ON a.user_id = u.id
        JOIN cck_checks c ON u.id = c.user_id
        LEFT JOIN cck_checks c2 ON a.id = c2.article_id AND c2.user_id = ?s
        WHERE c.article_id IN (
            SELECT id FROM cck_articles WHERE user_id = ?s
        ) AND c2.id IS NULL
        ORDER BY a.created_at ASC
    ";
    $sql = prepare($sql, array($user_id, $user_id));
    return get_data($sql);
}

function get_articles_viewed_today($user_id) {
    
    $today = date('Y-m-d'); // 当天日期

    $sql = "
        SELECT a.id, a.link, u.username
        FROM cck_articles a
        JOIN cck_users u ON a.user_id = u.id
        JOIN cck_checks c ON u.id = c.user_id
        LEFT JOIN cck_checks c2 ON a.id = c2.article_id AND c2.user_id = ?s
        WHERE c.article_id IN (
            SELECT id FROM cck_articles WHERE user_id = ?s AND DATE(created_at) = ?s
        ) 
        AND a.created_at >= ?s 
        AND c2.id IS NULL
        ORDER BY a.created_at ASC
    ";
    $sql = prepare($sql, array($user_id, $user_id, $today, $today));
    return get_data($sql);
}

function clean_cache($cache_dir, $expire_time = 3600) {
    if (!is_dir($cache_dir)) return;

    $files = scandir($cache_dir);
    foreach ($files as $file) {
        if ($file == '.' || $file == '..') continue;
        $file_path = $cache_dir . $file;
        if (time() - filemtime($file_path) > $expire_time) {
            unlink($file_path); // 删除过期的缓存文件
        }
    }
}



function fetch_link_title_v1($link) {

    // 生成缓存文件名
    $cache_dir = __DIR__ . '/cache/'; // 缓存目录
    if (!is_dir($cache_dir)) {
        mkdir($cache_dir, 0755, true); // 如果缓存目录不存在，则创建
    }

    // 清理过期缓存
    //clean_cache($cache_dir, 3600);

    $cache_file = $cache_dir . md5($link) . '.html'; // 缓存文件名

    // 检查缓存是否存在
    if (file_exists($cache_file) && (time() - filemtime($cache_file) < 3600*24)) { // 缓存有效期为24小时
        $html = file_get_contents($cache_file); // 从缓存文件读取内容
    } else {
        // 使用curl获取网页内容
        $ch = curl_init($link);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 忽略SSL证书验证
        $html = curl_exec($ch);
        curl_close($ch);

        // 将获取的内容写入缓存文件
        if ($html) {
            file_put_contents($cache_file, $html);
        }
    }

    // 提取标题
    if ($html) {
        $title1=fetch_pub_title_from_html($html);
        $title2=fetch_title_from_script($html);
        if(empty($title2)){
            return $title1;
        }
        return '【'.$title2.'】'.$title1;
    }
    return null; // 如果无法获取标题，则返回null
}

function fetch_link_html($link) {

    // 生成缓存文件名
    $cache_dir = __DIR__ . '/cache/'; // 缓存目录
    if (!is_dir($cache_dir)) {
        mkdir($cache_dir, 0755, true); // 如果缓存目录不存在，则创建
    }

    // 清理过期缓存
    //clean_cache($cache_dir, 3600);

    $cache_file = $cache_dir . md5($link) . '.html'; // 缓存文件名

    // 检查缓存是否存在
    if (file_exists($cache_file) && (time() - filemtime($cache_file) < 3600*24)) { // 缓存有效期为24小时
        $html = file_get_contents($cache_file); // 从缓存文件读取内容
    } else {
        // 使用curl获取网页内容
        $ch = curl_init($link);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 忽略SSL证书验证
        $html = curl_exec($ch);
        curl_close($ch);

        // 将获取的内容写入缓存文件
        if ($html) {
            file_put_contents($cache_file, $html);
        }
    }

    // 提取标题
    if ($html) {
        return $html;
    }
    return null; // 如果无法获取标题，则返回null
}

function fetch_pub_title_from_html($html) {
    
    // 提取标题
    if ($html) {
        // 优先匹配 og:title
        if (preg_match('/<meta\s+property="og:title"\s+content="([^"]+)"/i', $html, $matches)) {
            $title = mb_substr($matches[1], 0, 30, 'utf-8'); // 截取前30个汉字
            return $title;
        }

        // 如果没有 og:title，尝试匹配 twitter:title
        if (preg_match('/<meta\s+property="twitter:title"\s+content="([^"]+)"/i', $html, $matches)) {
            $title = mb_substr($matches[1], 0, 30, 'utf-8'); // 截取前30个汉字
            return $title;
        }

        // 如果都没有，回退到传统的 <title> 标签
        if (preg_match('/<title>(.*?)<\/title>/i', $html, $matches)) {
            $title = mb_substr($matches[1], 0, 30, 'utf-8'); // 截取前30个汉字
            return $title;
        }
    }
    return null; // 如果无法获取标题，则返回null
}

function fetch_title_from_script($html) {
    $pattern = '/htmlDecode\(["\']([^"\']+)["\']\)/i';
    if (preg_match($pattern, $html, $matches)) {
        $title = mb_substr($matches[1], 0, 30, 'utf-8');
        return $title;
    }
    return null;
}


function get_public_account_name($user_id) {
    // 查询指定用户ID的公众号名称
    $sql = "SELECT public_account_name FROM cck_users WHERE id = ?i";
    $sql = prepare($sql, array($user_id)); // 准备 SQL 语句
    $result = get_line($sql); // 执行查询并获取结果

    // 检查是否获取到结果
    if ($result && !empty($result['public_account_name'])) {
        return $result['public_account_name']; // 返回公众号名称
    } else {
        return null; // 如果没有找到记录或公众号名称为空，返回 null
    }
}


function get_hot_groups($limit = 5) {
    $sql = "
        SELECT 
            g.group_id,
            g.group_name,
            g.description,
            (COUNT(DISTINCT u.id) * 50 + COUNT(DISTINCT l.link_id) * 30 + COUNT(DISTINCT v.view_id) * 20) AS hot_score
        FROM cck1_groups g
        LEFT JOIN cck1_users u ON g.group_id = u.group_id
        LEFT JOIN cck1_links l ON g.group_id = l.group_id
        LEFT JOIN cck1_link_views v ON l.link_id = v.link_id
        GROUP BY g.group_id
        ORDER BY hot_score DESC ,g.group_name asc
    ";
    $sql .=' LIMIT '.intval($limit);
    //$sql = prepare($sql, array($limit));
    //if(DEBUG_MODE) echo __METHOD__.'() sql='.$sql.'<br>';
    return get_data($sql);
}


function fn_mini_link($link_org) {
    // 定义需要检查的前缀
    $prefix = 'https://mp.weixin.qq.com/s/';
    
    // 检查链接是否以指定的前缀开头（忽略大小写）
    if (stripos($link_org, $prefix) === 0) {
        // 如果是，返回链接的其余部分
        return substr($link_org, strlen($prefix));
    } else {
        // 如果不是，返回完整的链接
        return $link_org;
    }
}


function addSpaceIfNoSpace($str,$max=8) {
    // 初始化计数器
    $nonSpaceCount = 0;
    // 初始化结果字符串
    $result = '';
    $str_ret = '';
    // 遍历字符串中的每个字符
    for ($i = 0; $i < strlen($str); $i++) {
        // 如果当前字符不是空格
        if ($str[$i] !== ' ') {
            // 增加非空格字符计数
            $nonSpaceCount++;
            // 如果非空格字符数量达到 
            if ($nonSpaceCount >= $max ) {
                // 在当前字符前插入一个空格
                $result .= '<br>';
                $nonSpaceCount = 0;
            }
        }
        // 将当前字符添加到结果字符串中
        $result .= $str[$i];
    }
    return $result;
}



// 提交留言链接和预设留言
function submit_message_link($user_id, $url, $title1, $messages) {
    // 检查是否已存在相同的链接
    $table_name_links = get_table_name('message_links');
    $sql_check_link = "SELECT link_id FROM $table_name_links WHERE url = ?s AND user_id = ?i";
    $sql_check_link = prepare($sql_check_link, array($url, $user_id));
    $existing_link = get_line($sql_check_link);

    if ($existing_link) {
        // 如果链接已存在，直接获取现有的 link_id
        $link_id = $existing_link['link_id'];
    } else {
        // 如果链接不存在，插入新的留言链接

        if(empty($title1)){
            $tmpHtm = fetch_link_html($url); // 获取链接的 HTML 内容
            $title = fetch_pub_title_from_html($tmpHtm); // 获取链接标题
            $title2 = fetch_title_from_script($tmpHtm); // 获取 $title2

            if (!empty($title2)) {
                $title = '【' . $title2 . '】' . $title; // 将 $title2 添加到标题中
            }
        }else{
            $title=$title1;
        }


        $sql_links = "INSERT INTO $table_name_links (user_id, url, title) VALUES (?i, ?s, ?s)";
        $sql_links = prepare($sql_links, array($user_id, $url, $title));
        run_sql($sql_links);
        $link_id = get_last_insert_id();
    }

    // 插入预设留言
    $table_name_previews = get_table_name('message_previews');
    foreach ($messages as $message1) {
        $message=trim($message1);
        if(!empty($message)){
            $uuid = md5($message); // 生成 uuid
            // 检查是否已存在相同的 uuid
            $sql_check_preview = "SELECT COUNT(*) AS count FROM $table_name_previews WHERE link_id = ?i AND uuid = ?s";
            $sql_check_preview = prepare($sql_check_preview, array($link_id, $uuid));
            $result = get_var($sql_check_preview);
            if ($result == 0) { // 如果不存在相同的 uuid
                $sql_previews = "INSERT INTO $table_name_previews (link_id, message, uuid) VALUES (?i, ?s, ?s)";
                $sql_previews = prepare($sql_previews, array($link_id, $message, $uuid));
                run_sql($sql_previews);
                echo $sql_previews.'<hr>';
            }
        }
        
    }
    return $link_id;
}
// 获取某个链接的所有预设留言
function get_message_previews($link_id) {
    $table_name = get_table_name('message_previews');
    $sql = "SELECT preview_id as id,* FROM $table_name WHERE link_id = ?i";
    $sql = prepare($sql, array($link_id));
    return get_data($sql);
}

function get_user_info($user_id) {
    $table_name = get_table_name('users');
    $sql = "SELECT * FROM $table_name WHERE user_id = ?i LIMIT 1";
    $sql = prepare($sql, array($user_id));
    return get_line($sql);
}

function update_user_info($user_id, $username, $email, $phone, $public_account_name, $wechat_name, $wechat_openid) {
    $table_name = get_table_name('users');
    $sql = "UPDATE $table_name SET username = ?s, email = ?s, phone = ?s, public_account_name = ?s, wechat_name = ?s, wechat_openid = ?s WHERE user_id = ?i";
    $sql = prepare($sql, array($username, $email, $phone, $public_account_name, $wechat_name, $wechat_openid, $user_id));
    return run_sql($sql);
}

function echo_session_message(){

    /*
    if(isset($_SESSION['success_message'])){
        $tmp=$_SESSION['success_message'];
        if(!empty($tmp)){
            echo $tmp;
            
        }
    }else if(isset($_SESSION['error_message'])){
        $tmp=$_SESSION['error_message'];
        if(!empty($tmp)){        
            echo $tmp;
            unset($_SESSION['error_message']);
        }
    }
    unset($_SESSION['success_message']);
    unset($_SESSION['error_message']);
*/
    echo get_session_message();
    unset($_SESSION['success_message']);
    unset($_SESSION['error_message']);

}
function show_session_message_clear(){

    $ret= get_session_message();
    unset($_SESSION['success_message']);
    unset($_SESSION['error_message']);
    return $ret;
}

function get_session_message(){
    $ret='';
    if (isset($_SESSION['success_message'])) {
        $tmp=$_SESSION['success_message'];
        if(!empty($tmp)){
            $ret .='<div style="color: green; text-align: center;">操作成功提示：' . $tmp . '</div>';
        } 
    }
    if (isset($_SESSION['error_message'])) {
        $tmp=$_SESSION['error_message'];
        if(!empty($tmp)){
            $ret .='<div style="color: red; text-align: center;">操作失败提示：' . $tmp . '</div>';
        } 
    }
    return $ret;
}

function get_err_msg_div($tmp){
    return '<div style="color: red; text-align: center;">操作失败提示：' . $tmp . '</div>';
}
function clear_temp_article(){
    unset($_SESSION['temp_article_id']);
    unset($_SESSION['temp_start_time']);   
}

function read_time_tips($mode,$tmp_pass_time){
    if($mode==1){
        $tmp3='阅读时间不足 30 秒 !';
    }else{
        $tmp3='阅读时间不足 30 秒 ，无法提交。';
    }
    
    $tmp3.=' (阅读时间'.$tmp_pass_time.'秒) ';
    return $tmp3;

}
function read_time_check(&$articles){
    // 检查是否有临时页面的记录
    $temp_article_id = $_SESSION['temp_article_id'] ?? null;
    $tmp_pass_time = $_SESSION['tmp_pass_time'] ?? 0;
    clear_temp_article();
    // 如果存在临时页面的记录，将该文章移到列表最前面
    if ($temp_article_id && is_array($articles)) {
        foreach ($articles as $key => $article) {
            if ($article['id'] == $temp_article_id) {
                // 检查时间是否超过 30 秒                
                if ($tmp_pass_time < 30) {
                    $tmp3=read_time_tips(1,$tmp_pass_time);
                    echo get_err_msg_div($tmp3);
                } else {
                    // 将该文章移到列表最前面
                    $temp_article = $article;
                    unset($articles[$key]);
                    array_unshift($articles, $temp_article);
                }
                break;
            }
        }
    }

}

function get_arr_var($arr,$key){
    return isset($arr[$key]) ? htmlspecialchars($arr[$key]) : '';
}

function hide_today_ymd($str) {
    $today = date('Y-m-d'); // 获取今天的日期部分
    if (stripos($str, $today) !== false) {
        // 如果字符串包含今天的日期，则移除日期部分
        return preg_replace('/^' . preg_quote($today, '/') . '\s+/', '', $str);
    }

    $today = substr($today,0,5);
    if (stripos($str, $today) !== false) {
        // 如果字符串包含今天的日期，则移除日期部分
        return str_replace($today, '', $str);
    }
    return $str; // 如果不包含今天的日期，直接返回原字符串
}



function get_link_views_details($link_id, $limit = 50, $offset = 0) {
    $table_name_views = get_table_name('link_views');
    $table_name_users = get_table_name('users');
    $sql = "
        SELECT 
            v.view_id, v.viewed_at, v.read_count,v.duration, u.username
        FROM 
            $table_name_views v
        JOIN 
            $table_name_users u ON v.user_id = u.id
        WHERE 
            v.link_id = ?i
        ORDER BY 
            v.viewed_at DESC
        LIMIT 100
    ";
    $sql = prepare($sql, array($link_id, $limit, $offset));
    //echo $sql .'<hr>';
    return get_data($sql);
}