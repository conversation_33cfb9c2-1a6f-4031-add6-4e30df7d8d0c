<?php
require '_ks1.php';
@session_start();
if (!isset($_SESSION[SESSION_KEY]['user']['id']) || !in_array($_SESSION[SESSION_KEY]['user']['role'], ['user', 'admin'])) {
    header('Location: login.php');
    exit;
}


$user_id = $_SESSION[SESSION_KEY]['user']['id'];
$role = $_SESSION[SESSION_KEY]['user']['role']??null;
$username = $_SESSION[SESSION_KEY]['user']['username']??null;

$pub_name=get_public_account_name($user_id);



// 获取今天提交的链接记录
$today_submissions_checks = get_today_submissions_checks($user_id);
$today_submitted_links = get_today_submitted_links($user_id);
if($today_submitted_links && isset($today_submitted_links[0])){
    $today_submitted_num=count($today_submitted_links);
}else{
    $today_submitted_num=0;
}
/*
echo $today_submissions_checks;
echo  '-';
echo $today_submitted_num;
*/

?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><?php echo $appName;?>仪表盘</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        h1, h2 {
            text-align: center;
            color: #333;
            margin: 10px 0;
        }
        form {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px auto;
        }
        input[type="text"], input[type="number"], button {
            padding: 10px;
            margin: 10px 0;
            width: 80%;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        ul {
            list-style: none;
            padding: 0;
            margin: 0 auto;
            width: 80%;
        }
        li {
            background-color: white;
            padding: 20px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        a {
            color: #007bff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .read-count-input {
            margin-left: 10px;
        }

        /* 动态调整的样式 */
        <?php if ($large_text): ?>
            body {
                font-size: 22px; /* 增大字体 */
            }
            h1, h2 {
                font-size: 30px; /* 增大标题字体 */
            }
            input[type='text'], input[type='number'], button {
                font-size: 24px; /* 增大输入框和按钮字体 */
                padding: 15px; /* 增大按钮尺寸 */
            }
            li {
                padding: 25px; /* 增大列表项间距 */
            }
        <?php else: ?>
            body {
                font-size: 18px; /* 默认字体大小 */
            }
            h1, h2 {
                font-size: 24px; /* 默认标题字体 */
            }
            input[type='text'], input[type='number'], button {
                font-size: 18px; /* 默认输入框和按钮字体 */
                padding: 10px; /* 默认按钮尺寸 */
            }
            li {
                padding: 20px; /* 默认列表项间距 */
            }
        <?php endif; ?>
    </style>
</head>
<body>
    <div>
        <center>
            <h2>欢迎，<?php echo $pub_name ;?></h2>
            <span><?php echo $username.' '.$_SESSION[SESSION_KEY]['user']['email'].' (#'.$user_id; ?>)</span>
        </center>
        <?php echo $nav_html; ?>
    </div>

 
    <h2>提交新文章</h2>
    <?php if ($today_submitted_num < 2): ?>
        <form method="post" action="submit_article.php">
            <label for="link">文章链接：</label>
            <input type="text" name="link" id="link" required><br>
            <button type="submit">提交</button>
        </form>
    <?php else: ?>
        <p><center>您今天已经提交了<?php echo $today_submitted_num; ?>条记录，无法再提交新链接。</center></p>
    <?php endif; ?>
    <hr>
    <h2>今日提交的链接</h2>
    <ul>
        <?php if ($today_submitted_links): ?>
            <?php foreach ($today_submitted_links as $link): ?>
                <li>
                    <a href="<?php echo htmlspecialchars($link['url']); ?>" target="_blank"><?php 
//id,link_id,url, created_at,title
    if(isset($link['title']) && !empty($link['title'])){
        echo htmlspecialchars($link['title']); // 显示标题
    }else{
        $tmp3=fn_mini_link($link['url']);
        echo htmlspecialchars($tmp3);
    }
                     ?></a>
                    <br><small>提交时间：<?php echo htmlspecialchars($link['created_at']); ?></small>
                </li>
            <?php endforeach; ?>
        <?php else: ?>
            <li>今天还没有提交任何链接。</li>
        <?php endif; ?>
    </ul>
    <hr>
    
  
    <div>
        <?php echo $nav_html; ?>
    </div>
</body>
</html>