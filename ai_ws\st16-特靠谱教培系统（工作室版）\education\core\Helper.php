<?php
/**
 * 辅助函数文件
 * 包含系统中常用的辅助函数
 */

/**
 * 获取配置项
 * 
 * @param string $key 配置键名，支持点语法，如'db.host'
 * @param mixed $default 默认值，当配置项不存在时返回
 * @return mixed 配置值
 */
function config($key, $default = null) {
    global $config;
    
    if (strpos($key, '.') !== false) {
        $keys = explode('.', $key);
        $value = $config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    return isset($config[$key]) ? $config[$key] : $default;
}

/**
 * 获取当前URL
 * 
 * @param bool $withQueryString 是否包含查询字符串
 * @return string 当前URL
 */
function current_url($withQueryString = true) {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $url = $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    
    if (!$withQueryString && strpos($url, '?') !== false) {
        $url = substr($url, 0, strpos($url, '?'));
    }
    
    return $url;
}

/**
 * 生成URL
 * 
 * @param string $path 路径
 * @param array $params 查询参数
 * @return string 生成的URL
 */
function url($path, $params = []) {
    $baseUrl = config('app.base_url', '');
    $url = rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    return $url;
}

/**
 * 重定向到指定URL
 * 
 * @param string $url 目标URL
 * @param int $statusCode HTTP状态码
 * @return void
 */
function redirect($url, $statusCode = 302) {
    header('Location: ' . $url, true, $statusCode);
    exit;
}

/**
 * 获取资源文件URL
 * 
 * @param string $path 资源路径
 * @return string 资源URL
 */
function asset($path) {
    $baseUrl = config('app.base_url', '');
    return rtrim($baseUrl, '/') . '/public/' . ltrim($path, '/');
}

/**
 * 输出JSON响应
 * 
 * @param mixed $data 响应数据
 * @param int $statusCode HTTP状态码
 * @return void
 */
function json_response($data, $statusCode = 200) {
    header('Content-Type: application/json');
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

/**
 * 过滤输入数据
 * 
 * @param mixed $data 输入数据
 * @return mixed 过滤后的数据
 */
function filter_input_data($data) {
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            $data[$key] = filter_input_data($value);
        }
        return $data;
    }
    
    if (is_string($data)) {
        // 移除危险的HTML标签和属性
        $data = strip_tags($data);
        // 转换特殊字符为HTML实体
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
    
    return $data;
}

/**
 * 生成随机字符串
 * 
 * @param int $length 字符串长度
 * @param string $chars 字符集
 * @return string 随机字符串
 */
function random_string($length = 10, $chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ') {
    $string = '';
    $max = strlen($chars) - 1;
    
    for ($i = 0; $i < $length; $i++) {
        $string .= $chars[mt_rand(0, $max)];
    }
    
    return $string;
}

/**
 * 格式化日期时间
 * 
 * @param mixed $timestamp 时间戳或日期字符串
 * @param string $format 日期格式
 * @return string 格式化后的日期时间
 */
function format_date($timestamp = null, $format = 'Y-m-d H:i:s') {
    if ($timestamp === null) {
        $timestamp = time();
    } elseif (!is_numeric($timestamp)) {
        $timestamp = strtotime($timestamp);
    }
    
    return date($format, $timestamp);
}

/**
 * 获取客户端IP地址
 * 
 * @return string IP地址
 */
function get_client_ip() {
    $ip = '0.0.0.0';
    
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && $_SERVER['HTTP_X_FORWARDED_FOR']) {
        $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        $ip = trim($ips[0]);
    } elseif (isset($_SERVER['HTTP_CLIENT_IP']) && $_SERVER['HTTP_CLIENT_IP']) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR']) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    
    // 验证IP地址格式
    if (!filter_var($ip, FILTER_VALIDATE_IP)) {
        $ip = '0.0.0.0';
    }
    
    return $ip;
}

/**
 * 加密密码
 * 
 * @param string $password 原始密码
 * @return string 加密后的密码
 */
function password_hash_custom($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * 验证密码
 * 
 * @param string $password 原始密码
 * @param string $hash 加密后的密码
 * @return bool 验证结果
 */
function password_verify_custom($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * 截取字符串
 * 
 * @param string $string 原始字符串
 * @param int $length 截取长度
 * @param string $suffix 后缀
 * @return string 截取后的字符串
 */
function str_truncate($string, $length = 100, $suffix = '...') {
    if (mb_strlen($string, 'UTF-8') <= $length) {
        return $string;
    }
    
    return mb_substr($string, 0, $length, 'UTF-8') . $suffix;
}

/**
 * 检查是否为AJAX请求
 * 
 * @return bool 是否为AJAX请求
 */
function is_ajax_request() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * 获取文件扩展名
 * 
 * @param string $filename 文件名
 * @return string 扩展名
 */
function get_file_extension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

/**
 * 检查文件是否为图片
 * 
 * @param string $filename 文件名
 * @return bool 是否为图片
 */
function is_image_file($filename) {
    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    return in_array(get_file_extension($filename), $imageExtensions);
}

/**
 * 生成缩略图
 * 
 * @param string $source 源图片路径
 * @param string $destination 目标图片路径
 * @param int $width 宽度
 * @param int $height 高度
 * @param bool $crop 是否裁剪
 * @return bool 是否成功
 */
function create_thumbnail($source, $destination, $width = 150, $height = 150, $crop = true) {
    if (!function_exists('imagecreatetruecolor')) {
        return false;
    }
    
    // 获取图片信息
    $imageInfo = getimagesize($source);
    if ($imageInfo === false) {
        return false;
    }
    
    list($srcWidth, $srcHeight, $type) = $imageInfo;
    
    // 创建图片资源
    switch ($type) {
        case IMAGETYPE_JPEG:
            $srcImage = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $srcImage = imagecreatefrompng($source);
            break;
        case IMAGETYPE_GIF:
            $srcImage = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    if (!$srcImage) {
        return false;
    }
    
    // 计算缩放比例
    if ($crop) {
        $ratio = max($width / $srcWidth, $height / $srcHeight);
        $dstX = ($width - $srcWidth * $ratio) / 2;
        $dstY = ($height - $srcHeight * $ratio) / 2;
        $dstWidth = $width;
        $dstHeight = $height;
    } else {
        $ratio = min($width / $srcWidth, $height / $srcHeight);
        $dstX = 0;
        $dstY = 0;
        $dstWidth = $srcWidth * $ratio;
        $dstHeight = $srcHeight * $ratio;
    }
    
    // 创建目标图片
    $dstImage = imagecreatetruecolor($dstWidth, $dstHeight);
    
    // 保持PNG透明度
    if ($type == IMAGETYPE_PNG) {
        imagealphablending($dstImage, false);
        imagesavealpha($dstImage, true);
        $transparent = imagecolorallocatealpha($dstImage, 255, 255, 255, 127);
        imagefilledrectangle($dstImage, 0, 0, $dstWidth, $dstHeight, $transparent);
    }
    
    // 调整图片大小
    imagecopyresampled(
        $dstImage, $srcImage,
        $dstX, $dstY, 0, 0,
        $dstWidth - 2 * $dstX, $dstHeight - 2 * $dstY,
        $srcWidth, $srcHeight
    );
    
    // 保存图片
    $result = false;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($dstImage, $destination, 90);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($dstImage, $destination);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($dstImage, $destination);
            break;
    }
    
    // 释放资源
    imagedestroy($srcImage);
    imagedestroy($dstImage);
    
    return $result;
}

/**
 * 发送邮件
 * 
 * @param string $to 收件人
 * @param string $subject 主题
 * @param string $message 内容
 * @param array $headers 邮件头
 * @return bool 是否成功
 */
function send_mail($to, $subject, $message, $headers = []) {
    // 设置默认邮件头
    $defaultHeaders = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=utf-8',
        'From: ' . config('mail.from_name') . ' <' . config('mail.from_address') . '>'
    ];
    
    // 合并邮件头
    $headers = array_merge($defaultHeaders, $headers);
    
    // 发送邮件
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

/**
 * 记录日志
 * 
 * @param string $message 日志消息
 * @param string $level 日志级别
 * @param array $context 上下文数据
 * @return bool 是否成功
 */
function log_message($message, $level = 'info', $context = []) {
    // 日志文件路径
    $logDir = __DIR__ . '/../logs/';
    $logFile = $logDir . date('Y-m-d') . '.log';
    
    // 确保日志目录存在
    if (!is_dir($logDir)) {
        mkdir($logDir, 0777, true);
    }
    
    // 格式化上下文数据
    if (!empty($context)) {
        $replace = [];
        foreach ($context as $key => $val) {
            $replace['{' . $key . '}'] = is_scalar($val) ? $val : json_encode($val);
        }
        $message = strtr($message, $replace);
    }
    
    // 格式化日志消息
    $log = '[' . date('Y-m-d H:i:s') . '] [' . strtoupper($level) . '] ' . $message . PHP_EOL;
    
    // 写入日志文件
    return file_put_contents($logFile, $log, FILE_APPEND);
}

/**
 * 获取上传文件的临时路径
 * 
 * @param string $fieldName 表单字段名
 * @return string|false 临时路径或失败返回false
 */
function get_uploaded_file($fieldName) {
    if (!isset($_FILES[$fieldName]) || $_FILES[$fieldName]['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    return $_FILES[$fieldName]['tmp_name'];
}

/**
 * 移动上传的文件到指定位置
 * 
 * @param string $fieldName 表单字段名
 * @param string $destination 目标路径
 * @param array $allowedTypes 允许的文件类型
 * @param int $maxSize 最大文件大小（字节）
 * @return string|false 成功返回文件路径，失败返回false
 */
function move_uploaded_file_custom($fieldName, $destination, $allowedTypes = [], $maxSize = 0) {
    // 检查上传是否成功
    if (!isset($_FILES[$fieldName]) || $_FILES[$fieldName]['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    $file = $_FILES[$fieldName];
    
    // 检查文件大小
    if ($maxSize > 0 && $file['size'] > $maxSize) {
        return false;
    }
    
    // 检查文件类型
    if (!empty($allowedTypes)) {
        $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($ext, $allowedTypes)) {
            return false;
        }
    }
    
    // 确保目标目录存在
    $dir = dirname($destination);
    if (!is_dir($dir)) {
        mkdir($dir, 0777, true);
    }
    
    // 移动文件
    if (move_uploaded_file($file['tmp_name'], $destination)) {
        return $destination;
    }
    
    return false;
}

/**
 * 生成分页HTML
 * 
 * @param int $totalItems 总条目数
 * @param int $currentPage 当前页码
 * @param int $perPage 每页条目数
 * @param string $url 分页URL模板，使用{page}作为页码占位符
 * @return string 分页HTML
 */
function pagination($totalItems, $currentPage, $perPage, $url = '?page={page}') {
    // 计算总页数
    $totalPages = ceil($totalItems / $perPage);
    
    if ($totalPages <= 1) {
        return '';
    }
    
    // 确保当前页码有效
    $currentPage = max(1, min($currentPage, $totalPages));
    
    // 生成分页HTML
    $html = '<nav aria-label="Page navigation"><ul class="pagination">';
    
    // 上一页
    if ($currentPage > 1) {
        $prevUrl = str_replace('{page}', $currentPage - 1, $url);
        $html .= '<li class="page-item"><a class="page-link" href="' . $prevUrl . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
    } else {
        $html .= '<li class="page-item disabled"><a class="page-link" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
    }
    
    // 页码
    $range = 2; // 当前页前后显示的页码数
    
    // 起始页码
    $startPage = max(1, $currentPage - $range);
    // 结束页码
    $endPage = min($totalPages, $currentPage + $range);
    
    // 如果起始页码不是1，显示省略号
    if ($startPage > 1) {
        $firstUrl = str_replace('{page}', 1, $url);
        $html .= '<li class="page-item"><a class="page-link" href="' . $firstUrl . '">1</a></li>';
        if ($startPage > 2) {
            $html .= '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
        }
    }
    
    // 页码链接
    for ($i = $startPage; $i <= $endPage; $i++) {
        $pageUrl = str_replace('{page}', $i, $url);
        if ($i == $currentPage) {
            $html .= '<li class="page-item active"><a class="page-link" href="#">' . $i . '</a></li>';
        } else {
            $html .= '<li class="page-item"><a class="page-link" href="' . $pageUrl . '">' . $i . '</a></li>';
        }
    }
    
    // 如果结束页码不是最后一页，显示省略号
    if ($endPage < $totalPages) {
        if ($endPage < $totalPages - 1) {
            $html .= '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
        }
        $lastUrl = str_replace('{page}', $totalPages, $url);
        $html .= '<li class="page-item"><a class="page-link" href="' . $lastUrl . '">' . $totalPages . '</a></li>';
    }
    
    // 下一页
    if ($currentPage < $totalPages) {
        $nextUrl = str_replace('{page}', $currentPage + 1, $url);
        $html .= '<li class="page-item"><a class="page-link" href="' . $nextUrl . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
    } else {
        $html .= '<li class="page-item disabled"><a class="page-link" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
    }
    
    $html .= '</ul></nav>';
    
    return $html;
}