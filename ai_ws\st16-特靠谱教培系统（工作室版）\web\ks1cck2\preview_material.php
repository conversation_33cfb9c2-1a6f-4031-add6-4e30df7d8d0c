<?php
require '_ks1.php';
@session_start();

if (!isset($_SESSION[SESSION_KEY]['user']['id']) || !in_array($_SESSION[SESSION_KEY]['user']['role'], ['user', 'admin'])) {
    header('Location: login.php');
    exit;
}

$material_id = $_GET['material_id'] ?? null;
if (!$material_id) {
    header('Location: topic_user_materials.php');
    exit;
}

// 获取资料信息
function get_material_info($material_id) {
    $table_materials = get_table_name('materials');
    $sql = "SELECT * FROM $table_materials WHERE material_id = ?i";
    $sql = prepare($sql, array($material_id));
    return get_data($sql);
}

$material_info = get_material_info($material_id);
if (!$material_info) {
    header('Location: topic_user_materials.php');
    exit;
}

// 获取文件路径
$file_path = $material_info[0]['file_path'];
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资料预览</title>
</head>
<body>
    <h1>资料预览</h1>
    <p>资料ID: <?php echo $material_info[0]['material_id']; ?></p>
    <p>选题ID: <?php echo $material_info[0]['topic_id']; ?></p>
    <p>文件名: <?php echo $material_info[0]['file_name']; ?></p>
    <p>创建时间: <?php echo $material_info[0]['created_at']; ?></p>

    <!-- 根据文件类型显示内容 -->
    <?php
    $file_type = pathinfo($file_path, PATHINFO_EXTENSION);
    if (in_array(strtolower($file_type), ['jpg', 'png', 'gif'])) {
        echo '<img src="' . $file_path . '" alt="Preview" style="max-width:100%;">';
    } elseif (in_array(strtolower($file_type), ['pdf'])) {
        echo '<iframe src="' . $file_path . '" width="100%" height="600px"></iframe>';
    } else {
        echo '<a href="' . $file_path . '" target="_blank">查看文件</a>';
    }
    ?>

    <a href="topic_user_materials.php">返回</a>
</body>
</html>