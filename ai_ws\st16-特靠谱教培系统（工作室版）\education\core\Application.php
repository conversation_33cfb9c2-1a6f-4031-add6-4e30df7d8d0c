<?php
namespace Core;

/**
 * 应用核心类
 */
class Application
{
    private static $instance = null;
    private $router;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        self::$instance = $this;
        $this->router = new Router();
    }
    
    /**
     * 获取应用实例
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 运行应用
     */
    public function run()
    {
        // 解析请求
        $request = new Request();
        
        // 路由分发
        $this->router->dispatch($request);
    }
    
    /**
     * 获取路由器
     */
    public function getRouter()
    {
        return $this->router;
    }
}