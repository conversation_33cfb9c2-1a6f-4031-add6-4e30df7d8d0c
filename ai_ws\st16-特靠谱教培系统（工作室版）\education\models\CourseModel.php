<?php
/**
 * 课程模型
 * 处理课程数据的操作
 */
class CourseModel extends Model {
    /**
     * 表名
     * @var string
     */
    protected $table = 'courses';
    
    /**
     * 根据ID获取课程信息
     * @param int $id 课程ID
     * @return array|null 课程信息
     */
    public function getCourseById($id) {
        $sql = "SELECT c.*, t.subject, u.name as teacher_name 
                FROM {$this->table} c 
                LEFT JOIN teachers t ON c.teacher_id = t.id 
                LEFT JOIN users u ON t.user_id = u.id 
                WHERE c.id = ?";
        return $this->db->selectOne($sql, [$id]);
    }
    
    /**
     * 获取课程列表
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array 课程列表
     */
    public function getCourses($page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $sql = "SELECT c.*, t.subject, u.name as teacher_name 
                FROM {$this->table} c 
                LEFT JOIN teachers t ON c.teacher_id = t.id 
                LEFT JOIN users u ON t.user_id = u.id 
                ORDER BY c.id DESC 
                LIMIT {$offset}, {$perPage}";
        return $this->db->select($sql);
    }
    
    /**
     * 获取课程总数
     * @return int 课程总数
     */
    public function getCourseCount() {
        return $this->db->count("SELECT COUNT(*) FROM {$this->table}");
    }
    
    /**
     * 创建课程
     * @param array $data 课程数据
     * @return int 新课程的ID
     */
    public function createCourse($data) {
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * 更新课程信息
     * @param int $id 课程ID
     * @param array $data 课程数据
     * @return bool 是否成功
     */
    public function updateCourse($id, $data) {
        return $this->db->update($this->table, $data, "id = ?", [$id]);
    }
    
    /**
     * 删除课程
     * @param int $id 课程ID
     * @return bool 是否成功
     */
    public function deleteCourse($id) {
        // 先删除课程与学生的关联
        $this->db->delete('course_student', "course_id = ?", [$id]);
        
        // 删除课程的课表
        $this->db->delete('schedules', "course_id = ?", [$id]);
        
        // 删除课程
        return $this->db->delete($this->table, "id = ?", [$id]);
    }
    
    /**
     * 搜索课程
     * @param string $keyword 关键词
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array 课程列表
     */
    public function searchCourses($keyword, $page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $keyword = "%{$keyword}%";
        $sql = "SELECT c.*, t.subject, u.name as teacher_name 
                FROM {$this->table} c 
                LEFT JOIN teachers t ON c.teacher_id = t.id 
                LEFT JOIN users u ON t.user_id = u.id 
                WHERE c.name LIKE ? OR c.description LIKE ? OR u.name LIKE ? OR t.subject LIKE ? 
                ORDER BY c.id DESC 
                LIMIT {$offset}, {$perPage}";
        return $this->db->select($sql, [$keyword, $keyword, $keyword, $keyword]);
    }
    
    /**
     * 获取搜索结果总数
     * @param string $keyword 关键词
     * @return int 结果总数
     */
    public function getSearchCount($keyword) {
        $keyword = "%{$keyword}%";
        $sql = "SELECT COUNT(*) FROM {$this->table} c 
                LEFT JOIN teachers t ON c.teacher_id = t.id 
                LEFT JOIN users u ON t.user_id = u.id 
                WHERE c.name LIKE ? OR c.description LIKE ? OR u.name LIKE ? OR t.subject LIKE ?";
        return $this->db->count($sql, [$keyword, $keyword, $keyword, $keyword]);
    }
    
    /**
     * 获取教师的课程列表
     * @param int $teacherId 教师ID
     * @return array 课程列表
     */
    public function getCoursesByTeacher($teacherId) {
        $sql = "SELECT c.*, t.subject, u.name as teacher_name 
                FROM {$this->table} c 
                LEFT JOIN teachers t ON c.teacher_id = t.id 
                LEFT JOIN users u ON t.user_id = u.id 
                WHERE c.teacher_id = ? 
                ORDER BY c.id DESC";
        return $this->db->select($sql, [$teacherId]);
    }
    
    /**
     * 获取学生的课程列表
     * @param int $studentId 学生ID
     * @return array 课程列表
     */
    public function getCoursesByStudent($studentId) {
        $sql = "SELECT c.*, t.subject, u.name as teacher_name, cs.enrollment_date, cs.status as enrollment_status 
                FROM {$this->table} c 
                LEFT JOIN teachers t ON c.teacher_id = t.id 
                LEFT JOIN users u ON t.user_id = u.id 
                JOIN course_student cs ON c.id = cs.course_id 
                WHERE cs.student_id = ? 
                ORDER BY c.id DESC";
        return $this->db->select($sql, [$studentId]);
    }
    
    /**
     * 获取课程的课表
     * @param int $courseId 课程ID
     * @return array 课表列表
     */
    public function getCourseSchedules($courseId) {
        $sql = "SELECT s.*, c.name as classroom_name 
                FROM schedules s 
                LEFT JOIN classrooms c ON s.classroom_id = c.id 
                WHERE s.course_id = ? 
                ORDER BY s.day_of_week, s.start_time";
        return $this->db->select($sql, [$courseId]);
    }
    
    /**
     * 添加学生到课程
     * @param array $data 关联数据
     * @return int 新关联的ID
     */
    public function addStudentToCourse($data) {
        return $this->db->insert('course_student', $data);
    }
    
    /**
     * 从课程中移除学生
     * @param int $courseId 课程ID
     * @param int $studentId 学生ID
     * @return bool 是否成功
     */
    public function removeStudentFromCourse($courseId, $studentId) {
        return $this->db->delete('course_student', "course_id = ? AND student_id = ?", [$courseId, $studentId]);
    }
    
    /**
     * 检查学生是否已报名课程
     * @param int $courseId 课程ID
     * @param int $studentId 学生ID
     * @return bool 是否已报名
     */
    public function isStudentEnrolled($courseId, $studentId) {
        $sql = "SELECT COUNT(*) FROM course_student WHERE course_id = ? AND student_id = ?";
        return $this->db->count($sql, [$courseId, $studentId]) > 0;
    }
    
    /**
     * 获取课程已报名学生数量
     * @param int $courseId 课程ID
     * @return int 学生数量
     */
    public function getEnrolledStudentCount($courseId) {
        $sql = "SELECT COUNT(*) FROM course_student WHERE course_id = ?";
        return $this->db->count($sql, [$courseId]);
    }
    
    /**
     * 获取特定日期的课程列表
     * @param string $date 日期 (Y-m-d)
     * @return array 课程列表
     */
    public function getCoursesByDate($date) {
        // 获取星期几 (0-6, 0表示星期日)
        $dayOfWeek = date('w', strtotime($date));
        
        $sql = "SELECT c.*, t.subject, u.name as teacher_name, s.start_time, s.end_time, cl.name as classroom_name 
                FROM schedules s 
                JOIN {$this->table} c ON s.course_id = c.id 
                LEFT JOIN teachers t ON c.teacher_id = t.id 
                LEFT JOIN users u ON t.user_id = u.id 
                LEFT JOIN classrooms cl ON s.classroom_id = cl.id 
                WHERE s.day_of_week = ? 
                AND c.start_date <= ? AND c.end_date >= ? 
                ORDER BY s.start_time";
        return $this->db->select($sql, [$dayOfWeek, $date, $date]);
    }
    
    /**
     * 获取特定教室在特定日期的课程列表
     * @param int $classroomId 教室ID
     * @param string $date 日期 (Y-m-d)
     * @return array 课程列表
     */
    public function getCoursesByClassroomAndDate($classroomId, $date) {
        // 获取星期几 (0-6, 0表示星期日)
        $dayOfWeek = date('w', strtotime($date));
        
        $sql = "SELECT c.*, t.subject, u.name as teacher_name, s.start_time, s.end_time 
                FROM schedules s 
                JOIN {$this->table} c ON s.course_id = c.id 
                LEFT JOIN teachers t ON c.teacher_id = t.id 
                LEFT JOIN users u ON t.user_id = u.id 
                WHERE s.classroom_id = ? AND s.day_of_week = ? 
                AND c.start_date <= ? AND c.end_date >= ? 
                ORDER BY s.start_time";
        return $this->db->select($sql, [$classroomId, $dayOfWeek, $date, $date]);
    }
}