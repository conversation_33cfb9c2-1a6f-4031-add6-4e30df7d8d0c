article,aside,details,figcaption,figure,footer,header,hgroup,main,nav,section,summary{display:block;}audio,canvas,video{display:inline-block;}audio:not([controls]){display:none;height:0;}[hidden]{display:none;}html{font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;}body{margin:0;}a:focus{outline:thin dotted;}a:active,a:hover{outline:0;}h1{font-size:2em;margin:0.67em 0;}abbr[title]{border-bottom:1px dotted;}b,strong{font-weight:bold;}dfn{font-style:italic;}hr{-moz-box-sizing:content-box;box-sizing:content-box;height:0;}mark{background:#ff0;color:#000;}code,kbd,pre,samp{font-family:monospace,serif;font-size:1em;}pre{white-space:pre-wrap;}q{quotes:"\201C" "\201D" "\2018" "\2019";}small{font-size:80%;}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline;}sup{top:-0.5em;}sub{bottom:-0.25em;}img{border:0;}svg:not(:root){overflow:hidden;}figure{margin:0;}fieldset{border:1px solid #c0c0c0;margin:0 2px;padding:0.35em 0.625em 0.75em;}legend{border:0;padding:0;}button,input,select,textarea{font-family:inherit;font-size:100%;margin:0;}button,input{line-height:normal;}button,select{text-transform:none;}button,html input[type="button"],input[type="reset"],input[type="submit"]{-webkit-appearance:button;cursor:pointer;}button[disabled],html input[disabled]{cursor:default;}input[type="checkbox"],input[type="radio"]{box-sizing:border-box;padding:0;}input[type="search"]{-webkit-appearance:textfield;-moz-box-sizing:content-box;-webkit-box-sizing:content-box;box-sizing:content-box;}input[type="search"]::-webkit-search-cancel-button,input[type="search"]::-webkit-search-decoration{-webkit-appearance:none;}button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0;}textarea{overflow:auto;vertical-align:top;}table{border-collapse:collapse;border-spacing:0;}



*, *:after, *:before { -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; }

body {
    font-family: 'Lato', Arial, sans-serif;
    color: #7c8d87;
    background: #f8f8f8;
}

a {
    color: #31bc86;
    text-decoration: none;
}

a:hover, a:focus {
    color: #7c8d87;
}

.container > header {
    margin: 0 auto;
    padding: 2em;
    text-align: center;
    background: rgba(0,0,0,0.01);
}

.container > header h1 {
    font-size: 2.625em;
    line-height: 1.3;
    margin: 0;
    font-weight: 300;
}

.container > header span {
    display: block;
    font-size: 60%;
    opacity: 0.7;
    padding: 0 0 0.6em 0.1em;
}

/* To Navigation Style */
.codrops-top {
    background: #fff;
    background: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    width: 100%;
    font-size: 0.69em;
    line-height: 2.2;
}

.codrops-top a {
    text-decoration: none;
    padding: 0 1em;
    letter-spacing: 0.1em;
    display: inline-block;
}

.codrops-top a:hover {
    background: rgba(255,255,255,0.95);
}

.codrops-top span.right {
    float: right;
}

.codrops-top span.right a {
    float: left;
    display: block;
}

.codrops-icon:before {
    font-family: 'codropsicons';
    margin: 0 4px;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
}

.codrops-icon-drop:before {
    content: "\e001";
}

.codrops-icon-prev:before {
    content: "\e004";
}

/* Demo Buttons Style */
.codrops-demos {
    padding-top: 1em;
    font-size: 0.8em;
}

.codrops-demos a {
    display: inline-block;
    margin: 0.5em;
    padding: 0.7em 1.1em;
    outline: none;
    border: 2px solid #31bc86;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 700;
}

.codrops-demos a:hover,
.codrops-demos a.current-demo,
.codrops-demos a.current-demo:hover {
    border-color: #7c8d87;
    color: #7c8d87;
}

.related {
    text-align: center;
    font-size: 1.5em;
    padding-bottom: 3em;
}

@media screen and (max-width: 25em) {

    .codrops-icon span {
        display: none;
    }

}


/* Component styles */
.component {
    line-height: 1.5em;
    margin: 0 auto;
    /*padding: 2em 0 3em;*/
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}
.component .filler {
    font-family: "Blokk", Arial, sans-serif;
    color: #d3d3d3;
}
table {
    border-collapse: collapse;
    /*margin-bottom: 3em;*/
    width: 100%;
    background: #fff;
}
td, th {
    padding: 0.75em 1.5em;
    text-align: left;
}
td.err {
    background-color: #e992b9;
    color: #fff;
    font-size: 0.75em;
    text-align: center;
    line-height: 1;
}
th {
    background-color: white;
    font-weight: bold;
    color: #3E8FBA;
    white-space: nowrap;
}
tbody th {
    background-color: #2ea879;
}
tbody tr:nth-child(2n-1) {
    background-color: #f5f5f5;
    transition: all .125s ease-in-out;
}
tbody tr:hover {
    background-color: rgba(129,208,177,.3);
}

/* For appearance */
.sticky-wrap {
    overflow-x: auto;
    overflow-y: hidden;
    position: relative;
    /*margin: 3em 0;*/
    width: 100%;
}
.sticky-wrap .sticky-thead,
.sticky-wrap .sticky-col,
.sticky-wrap .sticky-intersect {
    opacity: 0;
    position: absolute;

    transition: all 0s ease-in-out;
    z-index: 50;
    width: auto; /* Prevent table from stretching to full size */
}
.sticky-wrap .sticky-thead {
    box-shadow: 0 0.25em 0.1em -0.1em rgba(0,0,0,.125);
    z-index: 100;
    position: absolute;
    width: 100%; /* Force stretch */
}
.sticky-wrap .sticky-intersect {
    opacity: 1;
    z-index: 150;

}
.sticky-wrap .sticky-intersect th {
    background-color: #666;
    color: #eee;
}
.sticky-wrap td,
.sticky-wrap th {
    box-sizing: border-box;
}

/* Not needed for sticky header/column functionality */
td.user-name {
    text-transform: capitalize;
}
.sticky-wrap.overflow-y {
    overflow-y: auto;
    max-height: 50vh;
}





