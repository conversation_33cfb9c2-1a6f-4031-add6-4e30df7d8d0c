<?php
require '_ks1.php';
@session_start();
if (is_logged_in()===false) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION[SESSION_KEY]['user']['id'];
$role = $_SESSION[SESSION_KEY]['user']['role'] ?? null;
$username = $_SESSION[SESSION_KEY]['user']['username'] ?? null;
$email=$_SESSION[SESSION_KEY]['user']['email']?? null;

$pub_name = get_public_account_name($user_id);

$_SESSION['temp_group_mode']="0";

// 获取文章列表
$articles = get_unchecked_articles($user_id);

/*
echo '<pre>';
var_dump($articles);
echo '</pre>';
*/
read_time_check($articles);

?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><?php echo $appName; ?>仪表盘</title>
    <!-- 引入 CSS 文件 -->
    <link rel="stylesheet" href="res/wx_style.css">    
    <link rel="stylesheet" href="res/ks1table.css">
</head>
<body>
    <div>
        <center>
            <h2>欢迎，<?php echo $pub_name; ?> 大厅模式</h2>
            <span><?php 

if ($username=='default_user'){
    echo '请先到[profile]中修改昵称!';
}else{
    echo $username;
}        
if (!empty($$email)) echo ' '.$email;
if (!empty($user_group_id)) echo ' 组id='.$user_group_id;

?></span>
        </center>
        <?php echo $nav_html; ?>
    </div>

    <h2>未阅读文章</h2>
    <ul>
        <?php
        if ($articles) {
            foreach ($articles as $article) {
                //var_dump($article);
                $link_id=$article['id'];
                $post_uid=$article['user_id'];
                if($post_uid==$user_id){
                    $tmp_td2="<span class='red-txt'>*</span>";
                }else{
                    $tmp_td2=" ";
                }
                $tmp_td2.='#'.$post_uid .' '. htmlspecialchars($article['username']) ;
                echo '<li id="li_'.$link_id.'">';
                echo '<a href="temporary_page.php?article_id=' . $article['id'] . '&link=' . urlencode($article['link']) . '">' . htmlspecialchars(fn_mini_link($article['link'])) . '</a> 提交人：' .$tmp_td2 . ' ';
               
                if(isset($article['title']) && !empty($article['title']) ){
                    // 显示标题
                    echo '<br><strong>标题：</strong>' . htmlspecialchars($article['title'] ?? '无标题'); 
                }
                echo '<form method="post" action="check_article.php" style="display:inline;">';
                echo '<input type="hidden" name="article_id" value="' . $article['id'] . '">';
                echo '<hr><label for="read_count">文章已有阅读数：</label>';
                echo '<input type="number" name="read_count" id="read_count" required placeholder="输入数字（如1.7万需写17000）" class="read-count-input">';
                echo '<button type="submit">提交阅读记录</button>';                
                echo '</form>';

                echo '<input type="hidden" id="arti_link_'.$article['id'].'" value="' . $article['link'] . '">';
                //echo '<a href="javascript:void(0);" onclick="showViewDetails('.$article['id'].')">打开模式2</a>';
                echo '<a href="javascript:void(0);" onclick="showViewDetails2('.$article['id'].')">提交模式2：不计时，需要填原因！</a>';
                echo '</li>';
            }
        } else {
            echo '<li>暂无未阅读文章。</li>';
        }
        ?>
    </ul>

    <div class="view-details-layer" id="viewDetailsLayer">
        <div class="view-details-content" id="viewDetailsContent">
            <span class="view-details-close" onclick="closeViewDetails()">×</span>
            <div id="viewDetailsTable">
            建议阅读30秒以上，注意查看当前阅读数，之后要输入页面才能提交</div>
        </div>
    </div>

    <div>
        <?php echo $nav_html; ?>
    </div>

<script>
    function showViewDetails2(linkId) {
        const kk= document.getElementById('arti_link_'+linkId).value;
        let vDT= document.getElementById('viewDetailsTable');
        vDT.innerHTML='';
        vDT.style.display = 'block';
        document.getElementById('viewDetailsLayer').style.display = 'block';

        // 增加备注输入框
        vDT.innerHTML+='<br><label for="remark">备注（采用模式2的原因）：</label>';
        vDT.innerHTML+='<input type="text" id="remark" name="remark" required>';
        vDT.innerHTML+='<button type="button" onclick="submitForm('+linkId+')">提交</button>';

    }

    // 增加submitForm函数
function submitForm(linkId) {
    const remark = document.getElementById('remark').value;
    const readCount = document.getElementById('read_count').value;
    const articleId = linkId;

    // 创建FormData对象
    const formData = new FormData();
    formData.append('article_id', articleId);
    formData.append('read_count', readCount);
    formData.append('remark', remark);

    // 发送POST请求
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'check_article2.php', true);
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4 && xhr.status === 200) {
            // 请求完成后的处理逻辑
            //alert(xhr.responseText);
            let vDT= document.getElementById('viewDetailsTable');
            vDT.innerHTML=xhr.responseText;
            closeViewDetails();
            hideLinkById(linkId);
        }
    };
    xhr.send(formData);
}

function hideLinkById(linkId){
    document.getElementById('li_'+linkId).style.display = 'none';
}

    function showViewDetails(linkId) {
        let vDT= document.getElementById('viewDetailsTable');
        vDT.innerHTML='建议阅读30秒以上，注意查看当前阅读数，之后要输入页面才能提交';
        vDT.style.display = 'block';
        const kk= document.getElementById('arti_link_'+linkId).value;
        var xhr = new XMLHttpRequest();
        xhr.open('GET', 'temporary_page.php?mode=2&article_id=' + linkId+'&link='+encodeURIComponent(kk), true);
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4 && xhr.status === 200) {
                //document.getElementById('viewDetailsLayer').innerHTML='建议阅读30秒以上，注意查看当前阅读数，之后要输入页面才能提交';
                try{
                    setTimeout(function(){                        
                        //window.open(kk, "_self");

var startTime = Date.now(); // 获取当前时间（毫秒级时间戳）
sessionStorage.setItem('previousPage', kk);
sessionStorage.setItem('ppstartTime', startTime);
vDT.style.display = 'block';
window.open(kk, '_blank');

                    }, 586); 

                }catch(error){
                    console.log('showViewDetails error')
                    console.error(error);
                }
                document.getElementById('viewDetailsLayer').style.display = 'block';
            }
        };
        xhr.send();
    }

    function closeViewDetails() {
        try{
            document.getElementById('viewDetailsLayer').style.display = 'none';
            let vDT=document.getElementById('viewDetailsTable');
            if(vDT){
                vDT.innerHTML='';
            }
        }catch(error){
            console.log('closeViewDetails error')
            console.error(error);
        }
    }
</script>
</body>
</html>