<?php
require '_ks1.php';
@session_start();

// 检查用户是否为管理员
if (!isset($_SESSION[SESSION_KEY]['user']['id']) || $_SESSION[SESSION_KEY]['user']['role'] != 'admin') {
    header('Location: login.php');
    exit;
}

// 从URL获取topic_id参数
$topic_id = isset($_GET['topic_id']) ? intval($_GET['topic_id']) : 0;

// 如果topic_id无效，重定向到错误页面或提示
if ($topic_id <= 0) {
    $_SESSION['error_message'] = '无效的选题ID！';
    header('Location: topic_admin.php'); // 假设有一个选题列表页面
    exit;
}

// 定义统一的上传目录
$upload_dir = 'uploads/';
if (!is_dir($upload_dir)) {
    mkdir($upload_dir, 0755, true); // 如果目录不存在，则创建
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_material'])) {
    // 获取上传文件信息
    $originalFileName = $_FILES['file']['name'];
    $fileExtension = pathinfo($originalFileName, PATHINFO_EXTENSION);
    $safeFileName = md5(uniqid(rand(), true)) . '.' . $fileExtension;
    $file_path = $upload_dir . $safeFileName; // 使用统一目录
    $file_type = pathinfo($file_name, PATHINFO_EXTENSION); // 获取文件扩展名作为文件类型
    $version = isset($_POST['version']) ? $_POST['version'] : '1.0'; // 允许自定义版本号，默认为 '1.0'
    $is_latest = isset($_POST['is_latest']) ? 1 : 0; // 是否为最新版本，默认为 1

    // 验证文件类型（仅允许特定类型）
    $allowed_types = ['pdf', 'txt', 'jpg', 'png', 'docx'];
    if (!in_array(strtolower($file_type), $allowed_types)) {
        $_SESSION['error_message'] = '不支持的文件类型！';
        header('Location: topic_upload_material.php?topic_id=' . $topic_id);
        exit;
    }

    // 上传文件到指定目录
    if (move_uploaded_file($_FILES['file']['tmp_name'], $file_path)) {
        $table = get_table_name('materials');
        $sql = "
            INSERT INTO $table (
                topic_id, file_name, file_path, file_type, created_by, version, is_latest
            ) VALUES (
                ?i, ?s, ?s, ?s, ?i, ?s, ?i
            )
        ";
        $sql = prepare($sql, array(
            $topic_id, // 确保topic_id正确传递到数据库
            $file_name,
            $file_path,
            $file_type,
            $_SESSION[SESSION_KEY]['user']['id'],
            $version,
            $is_latest
        ));
        run_sql($sql);

        $_SESSION['success_message'] = '资料上传成功！';
    } else {
        $_SESSION['error_message'] = '资料上传失败，请检查文件！';
    }

    header('Location: topic_upload_material.php?topic_id=' . $topic_id);
    exit;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传资料</title>
</head>
<body>
    <link rel="stylesheet" href="<?php echo $css_file; ?>">
    <h1>上传资料（选题ID：<?php echo $topic_id; ?>）</h1>
    <div><?php echo $nav_html; ?></div>

    <?php if (isset($_SESSION['success_message'])): ?>
        <div style="color: green;"><?php echo $_SESSION['success_message']; ?></div>
    <?php endif; ?>
    <?php if (isset($_SESSION['error_message'])): ?>
        <div style="color: red;"><?php echo $_SESSION['error_message']; ?></div>
    <?php endif; ?>

    <form action="" method="post" enctype="multipart/form-data">
        <input type="hidden" name="topic_id" value="<?php echo $topic_id; ?>">

        <label for="file">选择文件：</label>
        <input type="file" name="file" id="file" required><br>

        <label for="version">版本号（可选）：</label>
        <input type="text" name="version" id="version" placeholder="例如：1.0" value="1.0"><br>

        <label for="is_latest">是否为最新版本：</label>
        <input type="checkbox" name="is_latest" id="is_latest" checked><br>

        <button type="submit" name="upload_material">上传资料</button>
    </form>
    <div><?php echo $nav_html; ?></div>
</body>
</html>