<?php
define('IN_API2', true);
require '_ks1.php';
$mode = $_GET['mode'] ?? '1';
$article_id = $_GET['article_id'] ?? '';
$article_link = $_GET['link'] ?? '';

if (empty($article_id) || empty($article_link)) {
    echo "Invalid request.";
    exit;
}


if(isset($_SESSION['temp_article_id']) && $_SESSION['temp_article_id']==$article_id){
    //$_SESSION['tmp_start_time']=$_SESSION['temp_start_time'];
    $_SESSION['tmp_pass_time']=time()-$_SESSION['temp_start_time'];
    unset($_SESSION['temp_start_time']);
    if(isset($_SESSION['temp_group_mode'])  && $_SESSION['temp_group_mode']=='0'){
        
        header('Location: dashboard.php');     exit;

    }
    header('Location: enter_group.php');     exit;
}


// 记录进入临时页面的时间和文章 ID
$_SESSION['temp_article_id'] = $article_id;
$_SESSION['temp_start_time'] = time();
$_SESSION['temp_art_link'] = $article_link;

if($mode==2){
    echo $_SESSION['temp_article_id'];
    echo '<br>';
    echo $_SESSION['temp_start_time'];
    echo '<br>';
    echo $_SESSION['temp_art_link'];
    //exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Loading Page</title>
</head>
<body>
    <h1>Loading</h1>

    <pre><?php 
    //echo $referer;
    //var_dump($_SERVER); exit;

/*

Loading
array(100) {
  
  ["HTTP_COOKIE"]=>
  string(145) "PHPSESSID=5un98jsdq85qpn0u3pmm20p7m8; userrndstr=U2FsdGVkX19K6%2BuDSjQ%2BZ%2FB2Mbf7vLJTFq8gatcNfAtdHj4CJaJ4mmE72FNK%2BrNv; check_id=1743145343149"

  ["HTTP_REFERER"]=>
  string(48) "http://pubhelper.shdic.com/ks1cck2/dashboard.php"
 
  ["HTTP_USER_AGENT"]=>
  string(205) "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c33) XWEB/13487 Flue"
  
  ["HTTP_HOST"]=>
  string(19) "pubhelper.shdic.com"
  ["REDIRECT_STATUS"]=>
  string(3) "200"
  ["SERVER_NAME"]=>
  string(19) "pubhelper.shdic.com"

  ["REMOTE_ADDR"]=>
  string(13) "*************"
  
  ["DOCUMENT_URI"]=>
  string(27) "/ks1cck2/temporary_page.php"
  ["REQUEST_URI"]=>
  string(105) "/ks1cck2/temporary_page.php?article_id=2&link=https%3A%2F%2Fmp.weixin.qq.com%2Fs%2FDCnEYE4tRUQsTg15J_WHxg"
  ["SCRIPT_NAME"]=>
  string(27) "/ks1cck2/temporary_page.php"
  ["CONTENT_LENGTH"]=>
  string(0) ""
  ["CONTENT_TYPE"]=>
  string(0) ""
  ["REQUEST_METHOD"]=>
  string(3) "GET"
  ["QUERY_STRING"]=>
  string(77) "article_id=2&link=https%3A%2F%2Fmp.weixin.qq.com%2Fs%2FDCnEYE4tRUQsTg15J_WHxg"
  ["PATH_TRANSLATED"]=>
  string(43) "C:/phpstudy_pro/WWW/pubhelper.shdic.com/web"
  ["PATH_INFO"]=>
  string(0) ""
  ["SCRIPT_FILENAME"]=>
  string(70) "C:/phpstudy_pro/WWW/pubhelper.shdic.com/web/ks1cck2/temporary_page.php"
  ["FCGI_ROLE"]=>
  string(9) "RESPONDER"
  ["PHP_SELF"]=>
  string(27) "/ks1cck2/temporary_page.php"
  ["REQUEST_TIME_FLOAT"]=>
  float(1743256659.7978)
  ["REQUEST_TIME"]=>
  int(1743256659)
}

*/


?></pre>
    <p>建议阅读30秒以上，注意查看当前阅读数，之后要输入页面才能提交</p>
    <script>

window.onload = function () {
    window.open("<?= htmlspecialchars($article_link) ?>", "_self");
};        
    </script>

    <a href="dashboard.php">返回仪表盘</a>
</body>
</html>