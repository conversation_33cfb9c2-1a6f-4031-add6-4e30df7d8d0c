<?php
set_time_limit(0);
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);
require_once('check_admin.php');
require_once('mysqlconn.php');
if ((isset($_POST["action"])) && ($_POST["action"] == "addnew")) {
    $conn->insert('cardtype',[
        'cardname'=>$_POST["cardname"],
        'price'=>$_POST["price"],
        'quota'=>$_POST["quota"],
        'extenddays'=>$_POST["extenddays"],
        'createtime'=>date('Y-m-d H:i:s'),
        'memo'=>$_POST["memo"],
        'paymenturl'=>$_POST["paymenturl"]
    ]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('添加成功！');parent.location.href='rechargecardtype.php';</script>";
    exit;
} elseif ((isset($_GET["action"])) && ($_GET["action"] == "delete")) {
    $conn->update('cardtype',['ishidden'=>1],['id'=>$_GET['id']]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('删除成功！');parent.location.href='rechargecardtype.php';</script>";
    exit;
} elseif ((isset($_POST["action"])) && ($_POST["action"] == "update")) {
    $conn->update('cardtype',['memo'=>$_POST['memo'],'paymenturl'=>$_POST['paymenturl']],['id'=>$_POST['id']]);
    echo "<html><head><meta charset=utf-8></head><body><script>parent.document.getElementById('onthego').style.display='none';</script></body></html>";
    exit;
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>套餐管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        form {
            margin: 0px;
            display: inline
        }

        select {
            vertical-align: bottom;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑';overflow-x: auto">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-money"></i> 充值管理</li>
                <li class="active">充值卡类型</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px;">
            <div class="row">
                <div class="space-6"></div>
                <div class="col-xs-12">
                    <div style="border-radius: 10px;padding:10px 20px;line-height:20px;margin-bottom:20px;border:1px solid silver;">
                        <p>卡种一旦建立可能会与生成的充值卡和微信支付订单关联，因此部分信息不允许修改，删除操作其实也是隐藏。</p>
                        <p>强制修改或删除可以通过phpmyadmin进行，没搞明白的话建议不要乱改。</p>
                    </div>
                    <button class="btn btn-sm btn-info" style="padding:2px 10px;margin-bottom:10px;" onclick="$('#addnew').show();">添加卡种</button>
                    <div style="margin:20px 0;display:none;" id="addnew">
                        <form method=post name=addtype target="temp" onsubmit="return checkform();">
                            <input name="action" value="addnew" type=hidden>
                            卡种名称：<input name="cardname" class="bg-focus" autoComplete="off">&nbsp;&nbsp;
                            价格：<input name="price" size=4 class="bg-focus" autoComplete="off">&nbsp;&nbsp;
                            额度：<input name="quota" size=4 class="bg-focus" autoComplete="off">&nbsp;&nbsp;
                            天数：<input name="extenddays" size=4 class="bg-focus" autoComplete="off">&nbsp;&nbsp;
                            备注：<input name=memo class="bg-focus" autoComplete="off"><br><br>
                            购买链接：<input style="width:500px;" name=paymenturl class="bg-focus" autoComplete="off">
                            <button type=submit class="btn-primary" style="width:80px;">确认添加</button>
                        </form>
                    </div>
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th class="text-center" style="width:60px;">ID</th>
                                <th class="text-center" style="width:120px;">卡种名称</th>
                                <th class="text-center" style="width:100px;">价格</th>
                                <th class="text-center" style="width:100px;">额度</th>
                                <th class="text-center" style="width:100px;">天数</th>
                                <th class="text-center" style="width:160px;">创建时间</th>
                                <th class="text-center">备注</th>
                                <th class="text-center">购买链接</th>
                                <th class="text-center" style="width:60px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $totalnumber = $conn->count("cardtype", ["id"], ["ishidden" => 0]);
                            if ($totalnumber == 0) {
                                echo "<tr><td colspan=9>暂未设置充值卡类型。</td></tr>";
                            } else {
                                $count = 0;
                                $result = $conn->select("cardtype","*",["ishidden[!]"=>1,"ORDER"=>"id"]);
                                foreach ($result as $row) {
                                    $count++;
                            ?>
                                    <tr>
                                        <form id="form<?php echo $row["id"] ?>" method=post target="temp" action="rechargecardtype.php" onsubmit="checkandsubmit('<?php echo $row["id"] ?>');">
                                            <input type=hidden name=id value="<?php echo $row["id"] ?>"><input type=hidden name=action value=update>
                                            <td class="text-center"><?php echo $count ?></td>
                                            <td class="text-center"><?php echo $row["cardname"] ?></td>
                                            <td class="text-center"><?php echo $row["price"] ?> 元</td>
                                            <td class="text-center"><?php echo $row["quota"] ?> 积分</td>
                                            <td class="text-center"><?php echo $row["extenddays"] ?> 天</td>
                                            <td class="text-center"><?php echo $row["createtime"] ?></td>
                                            <td class="text-center"><input name=memo style="width:100%;" value="<?php echo $row["memo"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><input name=paymenturl style="width:100%;" value="<?php echo $row["paymenturl"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><input type=button style="width:45px;" onclick="deleteid('<?php echo $row["id"] ?>');" value="删除"></td>
                                        </form>
                                    </tr>
                            <?php
                                }
                            }
                            
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function checkandsubmit(boxid) {
            var newid = "form" + boxid;
            document.getElementById('onthego').style.display = 'block';
            document.getElementById(newid).submit();
        }

        function checkform() {
            if ((window.addtype.cardname.value == "") || (window.addtype.price.value == "") || (window.addtype.quota.value == "") || (window.addtype.extenddays.value == "")) {
                alert("套餐名称、价格、额度、天数不能为空！");
                return false;
            } else {
                return true;
            }
        }

        function deleteid(id) {
            if (confirm("确认删除该套餐类型吗？")) {
                document.getElementById("result").src = "rechargecardtype.php?action=delete&id=" + id;
            }
        }
    </script>

    <iframe style="display:none;" id=result name=temp></iframe>
</body>

</html>