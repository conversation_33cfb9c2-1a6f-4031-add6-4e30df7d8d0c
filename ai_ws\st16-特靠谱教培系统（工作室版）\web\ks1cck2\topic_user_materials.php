<?php
require '_ks1.php';
@session_start();

// 检查用户是否已登录且角色为用户或管理员
if (!isset($_SESSION[SESSION_KEY]['user']['id']) || !in_array($_SESSION[SESSION_KEY]['user']['role'], ['user', 'admin'])) {
    header('Location: login.php');
    exit;
}

// 从URL获取topic_id参数
$topic_id = isset($_GET['topic_id']) ? intval($_GET['topic_id']) : 0;

// 如果topic_id无效，重定向到选题列表页面
if ($topic_id <= 0) {
    $_SESSION['error_message'] = '无效的选题ID！';
    header('Location: topic_list.php'); // 假设有一个选题列表页面
    exit;
}

// 获取未被选用的资料（仅显示与当前topic_id相关的资料）
function get_available_materials($topic_id) {
    $table_materials = get_table_name('materials');
    $table_assignments = get_table_name('material_assignments');
    $sql = "
        SELECT m.material_id, m.topic_id, m.file_name, m.created_at
        FROM $table_materials m
        LEFT JOIN $table_assignments a ON m.material_id = a.material_id
        WHERE a.material_id IS NULL AND m.topic_id = ?i
    ";
    $sql = prepare($sql, array($topic_id));
    return get_data($sql);
}

// 获取用户已选用的资料（仅显示与当前topic_id相关的资料）
function get_assigned_materials($user_id, $topic_id) {
    $table_materials = get_table_name('materials');
    $table_assignments = get_table_name('material_assignments');
    $sql = "
        SELECT m.material_id, m.topic_id, m.file_name, m.created_at, a.viewed_at
        FROM $table_materials m
        JOIN $table_assignments a ON m.material_id = a.material_id
        WHERE a.user_id = ?i AND m.topic_id = ?i
    ";
    $sql = prepare($sql, array($user_id, $topic_id));
    return get_data($sql);
}

// 用户签出资料
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['checkout_material'])) {
    $material_id = $_POST['material_id'];
    $user_id = $_SESSION[SESSION_KEY]['user']['id'];
    $table = get_table_name('material_assignments');

    // 检查是否已经签出
    $sql = "SELECT * FROM $table WHERE material_id = ?i AND user_id = ?i";
    $sql = prepare($sql, array($material_id, $user_id));
    $result = get_data($sql);

    if (!$result) {
        $sql = "INSERT INTO $table (material_id, user_id) VALUES (?i, ?i)";
        $sql = prepare($sql, array($material_id, $user_id));
        run_sql($sql);
        $_SESSION['success_message'] = '资料已签出！';
    } else {
        $_SESSION['error_message'] = '该资料已被您签出！';
    }

    header('Location: topic_user_materials.php?topic_id=' . $topic_id);
    exit;
}

// 用户查看资料
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['view_material'])) {
    $material_id = $_POST['material_id'];
    $user_id = $_SESSION[SESSION_KEY]['user']['id'];
    $table = get_table_name('material_assignments');

    $sql = "UPDATE $table SET viewed_at = NOW() WHERE material_id = ?i AND user_id = ?i";
    $sql = prepare($sql, array($material_id, $user_id));
    run_sql($sql);
    $_SESSION['success_message'] = '资料已查看！';
    header('Location: topic_user_materials.php?topic_id=' . $topic_id);
    exit;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户资料列表</title>
    <link rel="stylesheet" href="res/ks1table.css">
</head>
<body>
    <h1>用户资料列表（选题ID：<?php echo $topic_id; ?>）</h1>
    <div><?php echo $nav_html; ?></div>

    <?php if (isset($_SESSION['success_message'])): ?>
        <div style="color: green;"><?php echo $_SESSION['success_message']; ?></div>
    <?php endif; ?>
    <?php if (isset($_SESSION['error_message'])): ?>
        <div style="color: red;"><?php echo $_SESSION['error_message']; ?></div>
    <?php endif; ?>

    <h2>可用资料</h2>
    <table border="1">
        <tr>
            <th>资料ID</th>
            <th>选题ID</th>
            <th>文件名</th>
            <th>创建时间</th>
            <th>操作</th>
        </tr>
        <?php
        $available_materials = get_available_materials($topic_id);
        if ($available_materials) {
            foreach ($available_materials as $material) {
                echo '<tr>';
                echo '<td>' . $material['material_id'] . '</td>';
                echo '<td>' . $material['topic_id'] . '</td>';
                echo '<td>' . $material['file_name'] . '</td>';
                echo '<td>' . $material['created_at'] . '</td>';
                echo '<td>';
                echo '<form action="preview_material.php" method="get" style="display:inline;">';
                echo '<input type="hidden" name="material_id" value="' . $material['material_id'] . '">';
                echo '<button type="submit">预览</button>';
                echo '</form>';
                echo '<form action="" method="post" style="display:inline;">';
                echo '<input type="hidden" name="material_id" value="' . $material['material_id'] . '">';
                echo '<button type="submit" name="checkout_material">签出</button>';
                echo '</form>';
                echo '</td>';
                echo '</tr>';
            }
        } else {
            echo '<tr><td colspan="5">暂无可用资料。</td></tr>';
        }
        ?>
    </table>

    <h2>已签出的资料</h2>
    <table border="1">
        <tr>
            <th>资料ID</th>
            <th>选题ID</th>
            <th>文件名</th>
            <th>创建时间</th>
            <th>操作</th>
        </tr>
        <?php
        $assigned_materials = get_assigned_materials($_SESSION[SESSION_KEY]['user']['id'], $topic_id);
        if ($assigned_materials) {
            foreach ($assigned_materials as $material) {
                echo '<tr>';
                echo '<td>' . $material['material_id'] . '</td>';
                echo '<td>' . $material['topic_id'] . '</td>';
                echo '<td>' . $material['file_name'] . '</td>';
                echo '<td>' . $material['created_at'] . '</td>';
                echo '<td>';
                if (!$material['viewed_at']) {
                    echo '<form action="" method="post" style="display:inline;">';
                    echo '<input type="hidden" name="material_id" value="' . $material['material_id'] . '">';
                    echo '<button type="submit" name="view_material">查看</button>';
                    echo '</form>';
                } else {
                    echo '已查看';
                }
                echo '</td>';
                echo '</tr>';
            }
        } else {
            echo '<tr><td colspan="5">暂无已签出的资料。</td></tr>';
        }
        ?>
    </table>

    <?php if ($_SESSION[SESSION_KEY]['user']['role'] == 'admin'): ?>
        <h2>上传新资料</h2>
        <form action="topic_upload_material.php?topic_id=<?php echo $topic_id; ?>" method="get">
            <button type="submit">上传新资料（选题ID：<?php echo $topic_id; ?>）</button>
        </form>
    <?php endif; ?>

    <div><?php echo $nav_html; ?></div>
</body>
</html>