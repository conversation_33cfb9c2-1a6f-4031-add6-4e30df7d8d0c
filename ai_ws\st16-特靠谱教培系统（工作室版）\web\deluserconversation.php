<?php
if ((isset($_GET["type"])) && ($_GET["type"] == "all")) {
    if (isset($_GET["userrndstr"])) {
        require_once('admin/mysqlconn.php');
        $row = $conn->get('user','*',['rndstr'=>$_GET["userrndstr"]]);
        if (empty($row)) {
            echo '{"success":false,"message":"当前用户登录状态已过期，请刷新页面重新登录"}';
        } else {
            $conn->update('chathistory',['ishidden'=>1],['userid'=>$row["id"]]);
            if ($conn->error) {
                echo '{"success":false,"message":"找不到对话记录"}';
            } else {
                echo '{"success":true}';
            }
        }
    }
}

if ((isset($_GET["type"])) && ($_GET["type"] == "single")) {
    if (isset($_GET["userrndstr"])) {
        require_once('admin/mysqlconn.php');
        $row = $conn->get('user','*',['rndstr'=>$_GET["userrndstr"]]);
        if (empty($row)) {
            echo '{"success":false,"message":"当前用户登录状态已过期，请刷新页面重新登录"}';
        } else {
            $conn->update('chathistory',['ishidden'=>1],['userid'=>$row["id"],'conversationid'=>$_GET["conversationid"]]);
            if ($conn->error) {
                echo '{"success":false,"message":"找不到该对话记录"}';
            } else {
                echo '{"success":true}';
            }
        }
    }
}
