<?php
/**
 * 用户模型
 */

namespace Models;

class UserModel extends \Core\Model
{
    /**
     * 表名
     */
    protected $table = 'user';
    
    /**
     * 根据ID查找用户
     */
    public function findById($id)
    {
        return $this->findOne("SELECT * FROM {$this->table} WHERE id = ?", [$id]);
    }
    
    /**
     * 根据邮箱查找用户
     */
    public function findByEmail($email)
    {
        return $this->findOne("SELECT * FROM {$this->table} WHERE email = ?", [$email]);
    }
    
    /**
     * 根据微信openid查找用户
     */
    public function findByOpenid($openid)
    {
        return $this->findOne("SELECT * FROM {$this->table} WHERE openid = ?", [$openid]);
    }
    
    /**
     * 检查邮箱是否存在
     */
    public function emailExists($email)
    {
        $result = $this->findOne("SELECT id FROM {$this->table} WHERE email = ?", [$email]);
        return !empty($result);
    }
    
    /**
     * 创建用户
     */
    public function create($data)
    {
        return $this->insert($this->table, $data);
    }
    
    /**
     * 创建微信用户
     */
    public function createWxUser($data)
    {
        return $this->insert($this->table, $data);
    }
    
    /**
     * 更新用户
     */
    public function update($id, $data)
    {
        return $this->updateById($this->table, $id, $data);
    }
    
    /**
     * 更新登录信息
     */
    public function updateLoginInfo($id, $data)
    {
        return $this->updateById($this->table, $id, $data);
    }
    
    /**
     * 删除用户
     */
    public function delete($id)
    {
        return $this->deleteById($this->table, $id);
    }
    
    /**
     * 获取用户列表
     */
    public function getList($page = 1, $limit = 10, $where = '')
    {
        $offset = ($page - 1) * $limit;
        $whereClause = empty($where) ? '' : "WHERE {$where}";
        
        $sql = "SELECT * FROM {$this->table} {$whereClause} ORDER BY id DESC LIMIT {$offset}, {$limit}";
        return $this->findAll($sql);
    }
    
    /**
     * 获取用户总数
     */
    public function getTotal($where = '')
    {
        $whereClause = empty($where) ? '' : "WHERE {$where}";
        
        $sql = "SELECT COUNT(*) as total FROM {$this->table} {$whereClause}";
        $result = $this->findOne($sql);
        
        return $result ? $result['total'] : 0;
    }
    
    /**
     * 根据角色获取用户列表
     */
    public function getListByRole($role, $page = 1, $limit = 10)
    {
        return $this->getList($page, $limit, "role = '{$role}'");
    }
    
    /**
     * 根据角色获取用户总数
     */
    public function getTotalByRole($role)
    {
        return $this->getTotal("role = '{$role}'");
    }
}