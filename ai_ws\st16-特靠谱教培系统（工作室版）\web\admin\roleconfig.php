<?php
set_time_limit(0);
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);
require_once('check_admin.php');
require_once('mysqlconn.php');
if ((isset($_POST["action"])) && ($_POST["action"] == "addnew")) {
    $conn->insert('role',[
        'rolename'=>$_POST["rolename"],
        'rolevalue'=>$_POST["rolevalue"],
        'rolecode'=>addslashes(trim($_POST["rolecode"])),
        'isvalid'=>$_POST["isvalid"],
        'createtime'=>date('Y-m-d H:i:s'),
        'memo'=>$_POST["memo"]
    ]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('添加成功！');parent.location.href='roleconfig.php';</script>";
    exit;
}
if ((isset($_GET["action"])) && ($_GET["action"] == "delete")) {
    if ($conn->has('role',['id'=>$_GET['id']])) {
        $conn->delete('role',['id'=>$_GET['id']]);
        echo "<html><head><meta charset=utf-8></head><body><script>alert('删除成功！');parent.location.href='roleconfig.php';</script>";
    } else {
        echo "<html><head><meta charset=utf-8></head><body><script>alert('角色不存在！');parent.location.reload();</script>";
    }
    exit;
}
if ((isset($_POST["action"])) && ($_POST["action"] == "update")) {
    if ($conn->has('role',['id'=>$_POST['id']])) {
        $conn->update('role',['rolename'=>$_POST['rolename'],'rolevalue'=>$_POST['rolevalue'],'rolecode'=>addslashes(trim($_POST["rolecode"])),'isvalid'=>$_POST['isvalid'],'memo'=>$_POST['memo']],['id'=>$_POST['id']]);
        echo "<html><head><meta charset=utf-8></head><body><script>parent.document.getElementById('onthego').style.display='none';</script></body></html>";
    } else {
        echo '<html><head><meta charset=utf-8></head><body><script>alert("角色不存在！");parent.location.reload();</script>';
    }
    exit;
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>角色配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        form {
            margin: 0px;
            display: inline
        }

        select {
            vertical-align: bottom;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑';overflow-x: auto">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">角色配置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px;">
            <div class="row">
                <div class="space-6"></div>
                <div class="col-xs-12">
                    <button class="btn btn-sm btn-info" style="padding:2px 10px;margin-bottom:10px;" onclick="$('#addnew').show();">添加角色</button>
                    <div style="margin:20px 0;display:none;" id="addnew">
                        <form method=post name=addtype target="temp" onsubmit="return checkform();">
                            <input name="action" value="addnew" type=hidden>
                            角色名称：<input name="rolename" class="bg-focus" autoComplete="off">&nbsp;&nbsp;
                            角色参数值：<input name="rolevalue" class="bg-focus" autoComplete="off">&nbsp;&nbsp;
                            首页展示：<input type=checkbox checked onchange='var checkbox=document.getElementById("isvalid");if (this.checked) {checkbox.value = "1";} else {checkbox.value = "0";}'><input id="isvalid" value=1 type=hidden name=isvalid>&nbsp;&nbsp;
                            备注：<input name=memo class="bg-focus" autoComplete="off"><br><br>
                            <label style="vertical-align: top;">角色代码：</label><textarea style="width:600px;height:100px;" name="rolecode" class="bg-focus"></textarea>
                            <button type=submit class="btn-primary" style="width:80px;">确认添加</button>
                        </form>
                    </div>
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th class="text-center" style="width:60px;">顺序</th>
                                <th class="text-center" style="width:120px;">角色名称</th>
                                <th class="text-center" style="width:120px;">角色参数值</th>
                                <th class="text-center">角色代码</th>
                                <th class="text-center" style="width:100px;">生效</th>
                                <!--<th class="text-center" style="width:160px;">创建时间</th>-->
                                <th class="text-center" style="width:120px;">备注</th>
                                <th class="text-center" style="width:140px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $totalnumber = $conn->count("role");
                            if ($totalnumber == 0) {
                                echo "<tr><td colspan=8>暂未设置任何角色。</td></tr>";
                            } else {
                                $count = 0;
                                $result = $conn->select("role","*",["ORDER"=>"id"]);
                                foreach ($result as $row) {
                                    $count++;
                            ?>
                                    <tr>
                                        <form id="form<?php echo $row["id"] ?>" method=post target="temp" action="roleconfig.php" onsubmit="checkandsubmit('<?php echo $row["id"] ?>');">
                                            <input type=hidden name=id value="<?php echo $row["id"] ?>"><input type=hidden name=action value=update>
                                            <td class="text-center"><?php echo $count ?></td>
                                            <td class="text-center"><input name=rolename style="width:100%;" value="<?php echo $row["rolename"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><input name=rolevalue style="width:100%;" value="<?php echo $row["rolevalue"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><textarea name=rolecode style="padding:0;height:70px;width:100%;" onchange="checkandsubmit('<?php echo $row["id"] ?>');"><?php echo $row["rolecode"] ?></textarea></td>
                                            <td class="text-center"><select name=isvalid onchange="checkandsubmit('<?php echo $row["id"] ?>');">
                                                    <option value=1 <?php echo ($row["isvalid"]) ? "selected" : "" ?>>是</option>
                                                    <option value=0 <?php echo ($row["isvalid"]) ? "" : "selected" ?>>否</option>
                                                </select></td>
                                            <!--<td class="text-center"><?php echo $row["createtime"] ?></td>-->
                                            <td class="text-center"><input name=memo style="width:100%;" value="<?php echo $row["memo"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><input type=submit value="更新" style="width:45px;">&nbsp;&nbsp;<input type=button style="width:45px;" onclick="deleteid('<?php echo $row["id"] ?>');" value="删除"></td>
                                        </form>
                                    </tr>

                            <?php
                                }
                            }
                            
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function checkandsubmit(boxid) {
            var newid = "form" + boxid;
            document.getElementById('onthego').style.display = 'block';
            document.getElementById(newid).submit();
        }

        function deleteid(id) {
            if (confirm("确认删除该角色吗？")) {
                document.getElementById("result").src = "roleconfig.php?action=delete&id=" + id;
            }
        }

        function checkform() {
            if ((window.addtype.rolename.value == "") || (window.addtype.rolevalue.value == "")) {
                alert("角色名称、参数值不能为空！");
                return false;
            } else {
                return true;
            }
        }
    </script>
    <iframe style="display:none;" id=result name=temp></iframe>
</body>

</html>