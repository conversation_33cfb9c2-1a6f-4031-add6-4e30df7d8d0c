<?php
namespace Core;

/**
 * 数据库连接类
 */
class Database
{
    private static $instance = null;
    private $pdo;
    
    /**
     * 构造函数
     */
    private function __construct()
    {
        $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=' . DB_CHARSET;
        $options = [
            \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
            \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
            \PDO::ATTR_EMULATE_PREPARES => false,
        ];
        
        try {
            $this->pdo = new \PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (\PDOException $e) {
            throw new \Exception('Database connection failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取实例
     * 
     * @return Database 实例
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 查询
     * 
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return array|bool 结果集或失败
     */
    public function query($sql, $params = [])
    {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            if (DEBUG_MODE) {
                throw new \Exception('Query failed: ' . $e->getMessage() . ' SQL: ' . $sql);
            }
            return false;
        }
    }
    
    /**
     * 执行
     * 
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return int|bool 影响行数或失败
     */
    public function execute($sql, $params = [])
    {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (\PDOException $e) {
            if (DEBUG_MODE) {
                throw new \Exception('Execute failed: ' . $e->getMessage() . ' SQL: ' . $sql);
            }
            return false;
        }
    }
    
    /**
     * 查询单条记录
     * 
     * @param string $table 表名
     * @param string $columns 列名
     * @param array $conditions 条件
     * @return array|null 记录
     */
    public function selectOne($table, $columns = '*', $conditions = [])
    {
        $sql = "SELECT $columns FROM $table";
        $params = [];
        
        if (!empty($conditions)) {
            $sql .= " WHERE ";
            $whereClauses = [];
            
            foreach ($conditions as $key => $value) {
                $whereClauses[] = "$key = :$key";
                $params[":$key"] = $value;
            }
            
            $sql .= implode(' AND ', $whereClauses);
        }
        
        $sql .= " LIMIT 1";
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $result = $stmt->fetch();
            return $result !== false ? $result : null;
        } catch (\PDOException $e) {
            if (DEBUG_MODE) {
                throw new \Exception('SelectOne failed: ' . $e->getMessage() . ' SQL: ' . $sql);
            }
            return null;
        }
    }
    
    /**
     * 查询多条记录
     * 
     * @param string $table 表名
     * @param string $columns 列名
     * @param array $conditions 条件
     * @param string $orderBy 排序
     * @param int $limit 限制
     * @param int $offset 偏移
     * @return array 记录列表
     */
    public function select($table, $columns = '*', $conditions = [], $orderBy = '', $limit = 0, $offset = 0)
    {
        $sql = "SELECT $columns FROM $table";
        $params = [];
        
        if (!empty($conditions)) {
            $sql .= " WHERE ";
            $whereClauses = [];
            
            foreach ($conditions as $key => $value) {
                $whereClauses[] = "$key = :$key";
                $params[":$key"] = $value;
            }
            
            $sql .= implode(' AND ', $whereClauses);
        }
        
        if (!empty($orderBy)) {
            $sql .= " ORDER BY $orderBy";
        }
        
        if ($limit > 0) {
            $sql .= " LIMIT $limit";
            
            if ($offset > 0) {
                $sql .= " OFFSET $offset";
            }
        }
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            if (DEBUG_MODE) {
                throw new \Exception('Select failed: ' . $e->getMessage() . ' SQL: ' . $sql);
            }
            return [];
        }
    }
    
    /**
     * 插入记录
     * 
     * @param string $table 表名
     * @param array $data 数据
     * @return int|bool 插入ID或失败
     */
    public function insert($table, $data)
    {
        $columns = implode(', ', array_keys($data));
        $placeholders = implode(', ', array_map(function($key) {
            return ":$key";
        }, array_keys($data)));
        
        $sql = "INSERT INTO $table ($columns) VALUES ($placeholders)";
        $params = [];
        
        foreach ($data as $key => $value) {
            $params[":$key"] = $value;
        }
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $this->pdo->lastInsertId();
        } catch (\PDOException $e) {
            if (DEBUG_MODE) {
                throw new \Exception('Insert failed: ' . $e->getMessage() . ' SQL: ' . $sql);
            }
            return false;
        }
    }
    
    /**
     * 更新记录
     * 
     * @param string $table 表名
     * @param array $data 数据
     * @param array $conditions 条件
     * @return int|bool 影响行数或失败
     */
    public function update($table, $data, $conditions)
    {
        $sql = "UPDATE $table SET ";
        $setClauses = [];
        $params = [];
        
        foreach ($data as $key => $value) {
            $setClauses[] = "$key = :set_$key";
            $params[":set_$key"] = $value;
        }
        
        $sql .= implode(', ', $setClauses);
        
        if (!empty($conditions)) {
            $sql .= " WHERE ";
            $whereClauses = [];
            
            foreach ($conditions as $key => $value) {
                $whereClauses[] = "$key = :where_$key";
                $params[":where_$key"] = $value;
            }
            
            $sql .= implode(' AND ', $whereClauses);
        }
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (\PDOException $e) {
            if (DEBUG_MODE) {
                throw new \Exception('Update failed: ' . $e->getMessage() . ' SQL: ' . $sql);
            }
            return false;
        }
    }
    
    /**
     * 删除记录
     * 
     * @param string $table 表名
     * @param array $conditions 条件
     * @return int|bool 影响行数或失败
     */
    public function delete($table, $conditions)
    {
        $sql = "DELETE FROM $table";
        $params = [];
        
        if (!empty($conditions)) {
            $sql .= " WHERE ";
            $whereClauses = [];
            
            foreach ($conditions as $key => $value) {
                $whereClauses[] = "$key = :$key";
                $params[":$key"] = $value;
            }
            
            $sql .= implode(' AND ', $whereClauses);
        }
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (\PDOException $e) {
            if (DEBUG_MODE) {
                throw new \Exception('Delete failed: ' . $e->getMessage() . ' SQL: ' . $sql);
            }
            return false;
        }
    }
    
    /**
     * 计数
     * 
     * @param string $table 表名
     * @param array $conditions 条件
     * @return int 数量
     */
    public function count($table, $conditions = [])
    {
        $sql = "SELECT COUNT(*) as count FROM $table";
        $params = [];
        
        if (!empty($conditions)) {
            $sql .= " WHERE ";
            $whereClauses = [];
            
            foreach ($conditions as $key => $value) {
                $whereClauses[] = "$key = :$key";
                $params[":$key"] = $value;
            }
            
            $sql .= implode(' AND ', $whereClauses);
        }
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $result = $stmt->fetch();
            return (int)$result['count'];
        } catch (\PDOException $e) {
            if (DEBUG_MODE) {
                throw new \Exception('Count failed: ' . $e->getMessage() . ' SQL: ' . $sql);
            }
            return 0;
        }
    }
    
    /**
     * 开始事务
     * 
     * @return bool 是否成功
     */
    public function beginTransaction()
    {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * 提交事务
     * 
     * @return bool 是否成功
     */
    public function commit()
    {
        return $this->pdo->commit();
    }
    
    /**
     * 回滚事务
     * 
     * @return bool 是否成功
     */
    public function rollBack()
    {
        return $this->pdo->rollBack();
    }
}