<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">欢迎使用特靠谱教培系统</h2>
                <p class="card-text">特靠谱教培系统是一款专为教育培训机构设计的管理系统，提供课程管理、教师管理、学生管理、课表安排、请假管理等功能。</p>
                <?php if (!isset($_SESSION['user_id'])): ?>
                <div class="mt-3">
                    <a href="/login" class="btn btn-primary me-2"><i class="fas fa-sign-in-alt"></i> 登录系统</a>
                    <a href="/register" class="btn btn-outline-primary"><i class="fas fa-user-plus"></i> 注册账号</a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if (isset($_SESSION['user_id'])): ?>
<!-- 快捷功能区 -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-calendar-alt fa-3x mb-3 text-primary"></i>
                <h5 class="card-title">课表查询</h5>
                <p class="card-text">查看课程安排和时间表</p>
                <a href="/schedule" class="btn btn-sm btn-primary">查看课表</a>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-calendar-times fa-3x mb-3 text-warning"></i>
                <h5 class="card-title">请假管理</h5>
                <p class="card-text">申请请假或查看请假记录</p>
                <a href="/leave" class="btn btn-sm btn-warning">请假管理</a>
            </div>
        </div>
    </div>
    <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'teacher'): ?>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-user-graduate fa-3x mb-3 text-success"></i>
                <h5 class="card-title">学生管理</h5>
                <p class="card-text">管理学生信息和考勤</p>
                <a href="/student" class="btn btn-sm btn-success">学生管理</a>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-book fa-3x mb-3 text-info"></i>
                <h5 class="card-title">课程管理</h5>
                <p class="card-text">管理课程信息和安排</p>
                <a href="/course" class="btn btn-sm btn-info">课程管理</a>
            </div>
        </div>
    </div>
    <?php elseif (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'student'): ?>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-book fa-3x mb-3 text-info"></i>
                <h5 class="card-title">我的课程</h5>
                <p class="card-text">查看已选课程和学习进度</p>
                <a href="/course/my" class="btn btn-sm btn-info">我的课程</a>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-id-card fa-3x mb-3 text-secondary"></i>
                <h5 class="card-title">个人信息</h5>
                <p class="card-text">查看和修改个人资料</p>
                <a href="/profile" class="btn btn-sm btn-secondary">个人信息</a>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- 最近课程 -->
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-calendar-day"></i> 今日课程</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>课程名称</th>
                                <th>教师</th>
                                <th>教室</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 这里将通过PHP动态生成课程数据 -->
                            <tr>
                                <td>08:00 - 09:30</td>
                                <td>示例课程1</td>
                                <td>张老师</td>
                                <td>A101</td>
                                <td><span class="badge bg-success">已完成</span></td>
                            </tr>
                            <tr>
                                <td>10:00 - 11:30</td>
                                <td>示例课程2</td>
                                <td>李老师</td>
                                <td>B203</td>
                                <td><span class="badge bg-warning">进行中</span></td>
                            </tr>
                            <tr>
                                <td>14:00 - 15:30</td>
                                <td>示例课程3</td>
                                <td>王老师</td>
                                <td>C305</td>
                                <td><span class="badge bg-primary">待上课</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- 系统公告 -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-bullhorn"></i> 系统公告</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">系统上线通知</h5>
                            <small>3天前</small>
                        </div>
                        <p class="mb-1">特靠谱教培系统正式上线，欢迎使用！</p>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">功能更新公告</h5>
                            <small>1周前</small>
                        </div>
                        <p class="mb-1">新增微信登录功能，使用更加便捷。</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>