<?php
/**
 * 课表控制器
 * 处理课表相关的请求
 */
class ScheduleController extends Controller {
    /**
     * 课表模型实例
     * @var ScheduleModel
     */
    private $scheduleModel;
    
    /**
     * 课程模型实例
     * @var CourseModel
     */
    private $courseModel;
    
    /**
     * 教室模型实例
     * @var ClassroomModel
     */
    private $classroomModel;
    
    /**
     * 教师模型实例
     * @var TeacherModel
     */
    private $teacherModel;
    
    /**
     * 构造函数
     */
    public function __construct() {
        parent::__construct();
        $this->scheduleModel = new ScheduleModel();
        $this->courseModel = new CourseModel();
        $this->classroomModel = new ClassroomModel();
        $this->teacherModel = new TeacherModel();
        
        // 检查用户是否登录，除了特定方法外
        $noAuthMethods = ['index', 'weekly', 'daily'];
        if (!in_array($this->getMethod(), $noAuthMethods)) {
            $this->checkLogin();
        }
    }
    
    /**
     * 课表首页
     */
    public function index() {
        // 默认重定向到周视图
        redirect('schedule/weekly');
    }
    
    /**
     * 周课表视图
     */
    public function weekly() {
        // 获取当前日期，如果没有指定，则使用当前日期
        $date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
        
        // 计算本周的开始日期（星期一）和结束日期（星期日）
        $weekStart = date('Y-m-d', strtotime('monday this week', strtotime($date)));
        $weekEnd = date('Y-m-d', strtotime('sunday this week', strtotime($date)));
        
        // 获取本周的所有日期
        $weekDates = [];
        $currentDate = $weekStart;
        while ($currentDate <= $weekEnd) {
            $weekDates[] = $currentDate;
            $currentDate = date('Y-m-d', strtotime('+1 day', strtotime($currentDate)));
        }
        
        // 获取本周的课表
        $schedules = $this->scheduleModel->getSchedulesByDateRange($weekStart, $weekEnd);
        
        // 按日期和时间段组织课表数据
        $weeklySchedule = [];
        foreach ($weekDates as $day) {
            $dayOfWeek = date('w', strtotime($day)); // 0（星期日）到 6（星期六）
            $weeklySchedule[$day] = [];
            
            // 获取当天的课程
            foreach ($schedules as $schedule) {
                if ($schedule['day_of_week'] == $dayOfWeek) {
                    $weeklySchedule[$day][] = $schedule;
                }
            }
        }
        
        // 设置视图变量
        $this->setVar('weeklySchedule', $weeklySchedule);
        $this->setVar('weekDates', $weekDates);
        $this->setVar('currentDate', $date);
        $this->setVar('weekStart', $weekStart);
        $this->setVar('weekEnd', $weekEnd);
        
        // 渲染视图
        $this->render('schedule/weekly');
    }
    
    /**
     * 日课表视图
     */
    public function daily() {
        // 获取当前日期，如果没有指定，则使用当前日期
        $date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
        
        // 获取星期几 (0-6, 0表示星期日)
        $dayOfWeek = date('w', strtotime($date));
        
        // 获取当天的课表
        $schedules = $this->scheduleModel->getSchedulesByDay($dayOfWeek, $date);
        
        // 设置视图变量
        $this->setVar('schedules', $schedules);
        $this->setVar('currentDate', $date);
        $this->setVar('dayOfWeek', $dayOfWeek);
        
        // 渲染视图
        $this->render('schedule/daily');
    }
    
    /**
     * 添加课表页面
     */
    public function add() {
        // 检查权限
        $this->checkPermission();
        
        // 获取课程ID（如果有）
        $courseId = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
        
        // 获取所有课程
        $courses = $this->courseModel->getCourses(1, 1000);
        
        // 获取所有教室
        $classrooms = $this->classroomModel->getAllClassrooms();
        
        // 设置视图变量
        $this->setVar('courses', $courses);
        $this->setVar('classrooms', $classrooms);
        $this->setVar('courseId', $courseId);
        
        // 渲染视图
        $this->render('schedule/add');
    }
    
    /**
     * 处理添加课表请求
     */
    public function create() {
        // 检查权限
        $this->checkPermission();
        
        // 检查请求方法
        if (!$this->isPost()) {
            redirect('schedule/add');
        }
        
        // 获取表单数据
        $courseId = (int)$this->getPost('course_id');
        $classroomId = (int)$this->getPost('classroom_id');
        $dayOfWeek = (int)$this->getPost('day_of_week');
        $startTime = $this->getPost('start_time');
        $endTime = $this->getPost('end_time');
        
        // 验证数据
        if (!$courseId || !$classroomId || !isset($dayOfWeek) || empty($startTime) || empty($endTime)) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '请填写所有必填字段']);
            } else {
                $this->setVar('error', '请填写所有必填字段');
                $this->setVar('formData', $_POST);
                $this->add();
                return;
            }
        }
        
        // 检查课程是否存在
        $course = $this->courseModel->getCourseById($courseId);
        if (!$course) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '选择的课程不存在']);
            } else {
                $this->setVar('error', '选择的课程不存在');
                $this->setVar('formData', $_POST);
                $this->add();
                return;
            }
        }
        
        // 检查教室是否存在
        $classroom = $this->classroomModel->getClassroomById($classroomId);
        if (!$classroom) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '选择的教室不存在']);
            } else {
                $this->setVar('error', '选择的教室不存在');
                $this->setVar('formData', $_POST);
                $this->add();
                return;
            }
        }
        
        // 检查时间格式
        if (strtotime($startTime) >= strtotime($endTime)) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '开始时间必须早于结束时间']);
            } else {
                $this->setVar('error', '开始时间必须早于结束时间');
                $this->setVar('formData', $_POST);
                $this->add();
                return;
            }
        }
        
        // 检查教室在指定时间是否可用
        $isAvailable = $this->classroomModel->isClassroomAvailable(
            $classroomId, 
            $dayOfWeek, 
            $startTime, 
            $endTime
        );
        
        if (!$isAvailable) {
            // 获取冲突的课程
            $conflicts = $this->classroomModel->getClassroomConflicts(
                $classroomId, 
                $dayOfWeek, 
                $startTime, 
                $endTime
            );
            
            $conflictMessage = '教室在指定时间不可用，与以下课程冲突：<br>';
            foreach ($conflicts as $conflict) {
                $conflictMessage .= "- {$conflict['course_name']} ({$conflict['start_time']} - {$conflict['end_time']})<br>";
            }
            
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => $conflictMessage]);
            } else {
                $this->setVar('error', $conflictMessage);
                $this->setVar('formData', $_POST);
                $this->add();
                return;
            }
        }
        
        // 检查教师在指定时间是否可用
        $teacherId = $course['teacher_id'];
        $isTeacherAvailable = $this->teacherModel->isTeacherAvailable(
            $teacherId, 
            $dayOfWeek, 
            $startTime, 
            $endTime
        );
        
        if (!$isTeacherAvailable) {
            // 获取冲突的课程
            $conflicts = $this->teacherModel->getTeacherConflicts(
                $teacherId, 
                $dayOfWeek, 
                $startTime, 
                $endTime
            );
            
            $conflictMessage = '教师在指定时间不可用，与以下课程冲突：<br>';
            foreach ($conflicts as $conflict) {
                $conflictMessage .= "- {$conflict['course_name']} ({$conflict['start_time']} - {$conflict['end_time']})<br>";
            }
            
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => $conflictMessage]);
            } else {
                $this->setVar('error', $conflictMessage);
                $this->setVar('formData', $_POST);
                $this->add();
                return;
            }
        }
        
        // 准备数据
        $data = [
            'course_id' => $courseId,
            'classroom_id' => $classroomId,
            'day_of_week' => $dayOfWeek,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        // 创建课表
        $scheduleId = $this->scheduleModel->createSchedule($data);
        
        if ($scheduleId) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'success', 'message' => '课表创建成功', 'id' => $scheduleId]);
            } else {
                redirect('schedule/weekly');
            }
        } else {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课表创建失败，请重试']);
            } else {
                $this->setVar('error', '课表创建失败，请重试');
                $this->setVar('formData', $_POST);
                $this->add();
            }
        }
    }
    
    /**
     * 编辑课表页面
     */
    public function edit() {
        // 检查权限
        $this->checkPermission();
        
        // 获取课表ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if (!$id) {
            redirect('schedule/weekly');
        }
        
        // 获取课表信息
        $schedule = $this->scheduleModel->getScheduleById($id);
        
        if (!$schedule) {
            $this->setVar('error', '课表不存在');
            $this->render('error/index');
            return;
        }
        
        // 获取所有课程
        $courses = $this->courseModel->getCourses(1, 1000);
        
        // 获取所有教室
        $classrooms = $this->classroomModel->getAllClassrooms();
        
        // 设置视图变量
        $this->setVar('schedule', $schedule);
        $this->setVar('courses', $courses);
        $this->setVar('classrooms', $classrooms);
        
        // 渲染视图
        $this->render('schedule/edit');
    }
    
    /**
     * 处理更新课表请求
     */
    public function update() {
        // 检查权限
        $this->checkPermission();
        
        // 检查请求方法
        if (!$this->isPost()) {
            redirect('schedule/weekly');
        }
        
        // 获取课表ID
        $id = (int)$this->getPost('id');
        
        if (!$id) {
            redirect('schedule/weekly');
        }
        
        // 获取课表信息
        $schedule = $this->scheduleModel->getScheduleById($id);
        
        if (!$schedule) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课表不存在']);
            } else {
                $this->setVar('error', '课表不存在');
                $this->render('error/index');
            }
            return;
        }
        
        // 获取表单数据
        $courseId = (int)$this->getPost('course_id');
        $classroomId = (int)$this->getPost('classroom_id');
        $dayOfWeek = (int)$this->getPost('day_of_week');
        $startTime = $this->getPost('start_time');
        $endTime = $this->getPost('end_time');
        
        // 验证数据
        if (!$courseId || !$classroomId || !isset($dayOfWeek) || empty($startTime) || empty($endTime)) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '请填写所有必填字段']);
            } else {
                $this->setVar('error', '请填写所有必填字段');
                $this->setVar('formData', $_POST);
                $this->edit();
                return;
            }
        }
        
        // 检查课程是否存在
        $course = $this->courseModel->getCourseById($courseId);
        if (!$course) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '选择的课程不存在']);
            } else {
                $this->setVar('error', '选择的课程不存在');
                $this->setVar('formData', $_POST);
                $this->edit();
                return;
            }
        }
        
        // 检查教室是否存在
        $classroom = $this->classroomModel->getClassroomById($classroomId);
        if (!$classroom) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '选择的教室不存在']);
            } else {
                $this->setVar('error', '选择的教室不存在');
                $this->setVar('formData', $_POST);
                $this->edit();
                return;
            }
        }
        
        // 检查时间格式
        if (strtotime($startTime) >= strtotime($endTime)) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '开始时间必须早于结束时间']);
            } else {
                $this->setVar('error', '开始时间必须早于结束时间');
                $this->setVar('formData', $_POST);
                $this->edit();
                return;
            }
        }
        
        // 检查教室在指定时间是否可用（排除当前课表）
        $isAvailable = $this->classroomModel->isClassroomAvailable(
            $classroomId, 
            $dayOfWeek, 
            $startTime, 
            $endTime, 
            $id
        );
        
        if (!$isAvailable) {
            // 获取冲突的课程
            $conflicts = $this->classroomModel->getClassroomConflicts(
                $classroomId, 
                $dayOfWeek, 
                $startTime, 
                $endTime, 
                $id
            );
            
            $conflictMessage = '教室在指定时间不可用，与以下课程冲突：<br>';
            foreach ($conflicts as $conflict) {
                $conflictMessage .= "- {$conflict['course_name']} ({$conflict['start_time']} - {$conflict['end_time']})<br>";
            }
            
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => $conflictMessage]);
            } else {
                $this->setVar('error', $conflictMessage);
                $this->setVar('formData', $_POST);
                $this->edit();
                return;
            }
        }
        
        // 检查教师在指定时间是否可用（排除当前课表）
        $teacherId = $course['teacher_id'];
        $isTeacherAvailable = $this->teacherModel->isTeacherAvailable(
            $teacherId, 
            $dayOfWeek, 
            $startTime, 
            $endTime, 
            $id
        );
        
        if (!$isTeacherAvailable) {
            // 获取冲突的课程
            $conflicts = $this->teacherModel->getTeacherConflicts(
                $teacherId, 
                $dayOfWeek, 
                $startTime, 
                $endTime, 
                $id
            );
            
            $conflictMessage = '教师在指定时间不可用，与以下课程冲突：<br>';
            foreach ($conflicts as $conflict) {
                $conflictMessage .= "- {$conflict['course_name']} ({$conflict['start_time']} - {$conflict['end_time']})<br>";
            }
            
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => $conflictMessage]);
            } else {
                $this->setVar('error', $conflictMessage);
                $this->setVar('formData', $_POST);
                $this->edit();
                return;
            }
        }
        
        // 准备数据
        $data = [
            'course_id' => $courseId,
            'classroom_id' => $classroomId,
            'day_of_week' => $dayOfWeek,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 更新课表
        $result = $this->scheduleModel->updateSchedule($id, $data);
        
        if ($result) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'success', 'message' => '课表更新成功']);
            } else {
                redirect('schedule/weekly');
            }
        } else {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课表更新失败，请重试']);
            } else {
                $this->setVar('error', '课表更新失败，请重试');
                $this->setVar('formData', $_POST);
                $this->edit();
            }
        }
    }
    
    /**
     * 删除课表
     */
    public function delete() {
        // 检查权限
        $this->checkPermission();
        
        // 获取课表ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if (!$id) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '无效的课表ID']);
            } else {
                redirect('schedule/weekly');
            }
            return;
        }
        
        // 获取课表信息
        $schedule = $this->scheduleModel->getScheduleById($id);
        
        if (!$schedule) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课表不存在']);
            } else {
                $this->setVar('error', '课表不存在');
                $this->render('error/index');
            }
            return;
        }
        
        // 删除课表
        $result = $this->scheduleModel->deleteSchedule($id);
        
        if ($result) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'success', 'message' => '课表删除成功']);
            } else {
                redirect('schedule/weekly');
            }
        } else {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课表删除失败，请重试']);
            } else {
                $this->setVar('error', '课表删除失败，请重试');
                $this->render('error/index');
            }
        }
    }
    
    /**
     * 教师课表视图
     */
    public function teacher() {
        // 检查用户是否登录
        $this->checkLogin();
        
        // 获取教师ID
        $teacherId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        // 如果没有指定教师ID，且当前用户是教师，则使用当前用户的教师ID
        if (!$teacherId && isset($_SESSION['user']) && $_SESSION['user']['role'] == 'teacher') {
            $teacher = $this->teacherModel->getTeacherByUserId($_SESSION['user']['id']);
            if ($teacher) {
                $teacherId = $teacher['id'];
            }
        }
        
        if (!$teacherId) {
            redirect('schedule/weekly');
        }
        
        // 获取教师信息
        $teacher = $this->teacherModel->getTeacherById($teacherId);
        
        if (!$teacher) {
            $this->setVar('error', '教师不存在');
            $this->render('error/index');
            return;
        }
        
        // 获取当前日期，如果没有指定，则使用当前日期
        $date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
        
        // 计算本周的开始日期（星期一）和结束日期（星期日）
        $weekStart = date('Y-m-d', strtotime('monday this week', strtotime($date)));
        $weekEnd = date('Y-m-d', strtotime('sunday this week', strtotime($date)));
        
        // 获取本周的所有日期
        $weekDates = [];
        $currentDate = $weekStart;
        while ($currentDate <= $weekEnd) {
            $weekDates[] = $currentDate;
            $currentDate = date('Y-m-d', strtotime('+1 day', strtotime($currentDate)));
        }
        
        // 获取教师的课表
        $schedules = $this->scheduleModel->getSchedulesByTeacher($teacherId);
        
        // 按日期和时间段组织课表数据
        $weeklySchedule = [];
        foreach ($weekDates as $day) {
            $dayOfWeek = date('w', strtotime($day)); // 0（星期日）到 6（星期六）
            $weeklySchedule[$day] = [];
            
            // 获取当天的课程
            foreach ($schedules as $schedule) {
                if ($schedule['day_of_week'] == $dayOfWeek) {
                    $weeklySchedule[$day][] = $schedule;
                }
            }
        }
        
        // 设置视图变量
        $this->setVar('teacher', $teacher);
        $this->setVar('weeklySchedule', $weeklySchedule);
        $this->setVar('weekDates', $weekDates);
        $this->setVar('currentDate', $date);
        $this->setVar('weekStart', $weekStart);
        $this->setVar('weekEnd', $weekEnd);
        
        // 渲染视图
        $this->render('schedule/teacher');
    }
    
    /**
     * 学生课表视图
     */
    public function student() {
        // 检查用户是否登录
        $this->checkLogin();
        
        // 获取学生ID
        $studentId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        // 如果没有指定学生ID，且当前用户是学生，则使用当前用户的学生ID
        if (!$studentId && isset($_SESSION['user']) && $_SESSION['user']['role'] == 'student') {
            $studentModel = new StudentModel();
            $student = $studentModel->getStudentByUserId($_SESSION['user']['id']);
            if ($student) {
                $studentId = $student['id'];
            }
        }
        
        if (!$studentId) {
            redirect('schedule/weekly');
        }
        
        // 获取学生信息
        $studentModel = new StudentModel();
        $student = $studentModel->getStudentById($studentId);
        
        if (!$student) {
            $this->setVar('error', '学生不存在');
            $this->render('error/index');
            return;
        }
        
        // 获取当前日期，如果没有指定，则使用当前日期
        $date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
        
        // 计算本周的开始日期（星期一）和结束日期（星期日）
        $weekStart = date('Y-m-d', strtotime('monday this week', strtotime($date)));
        $weekEnd = date('Y-m-d', strtotime('sunday this week', strtotime($date)));
        
        // 获取本周的所有日期
        $weekDates = [];
        $currentDate = $weekStart;
        while ($currentDate <= $weekEnd) {
            $weekDates[] = $currentDate;
            $currentDate = date('Y-m-d', strtotime('+1 day', strtotime($currentDate)));
        }
        
        // 获取学生的课表
        $schedules = $this->scheduleModel->getSchedulesByStudent($studentId);
        
        // 按日期和时间段组织课表数据
        $weeklySchedule = [];
        foreach ($weekDates as $day) {
            $dayOfWeek = date('w', strtotime($day)); // 0（星期日）到 6（星期六）
            $weeklySchedule[$day] = [];
            
            // 获取当天的课程
            foreach ($schedules as $schedule) {
                if ($schedule['day_of_week'] == $dayOfWeek) {
                    $weeklySchedule[$day][] = $schedule;
                }
            }
        }
        
        // 设置视图变量
        $this->setVar('student', $student);
        $this->setVar('weeklySchedule', $weeklySchedule);
        $this->setVar('weekDates', $weekDates);
        $this->setVar('currentDate', $date);
        $this->setVar('weekStart', $weekStart);
        $this->setVar('weekEnd', $weekEnd);
        
        // 渲染视图
        $this->render('schedule/student');
    }
    
    /**
     * 检查用户权限
     * 只有管理员和教师可以管理课表
     */
    private function checkPermission() {
        if (!isset($_SESSION['user']) || ($_SESSION['user']['role'] != 'admin' && $_SESSION['user']['role'] != 'teacher')) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '您没有权限执行此操作']);
                exit;
            } else {
                $this->setVar('error', '您没有权限执行此操作');
                $this->render('error/index');
                exit;
            }
        }
    }
}