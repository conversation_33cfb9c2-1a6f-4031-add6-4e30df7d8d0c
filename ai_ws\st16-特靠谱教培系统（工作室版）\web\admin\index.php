<?php
$beginfiles = glob('diy/begin/*.php');
foreach ($beginfiles as $file) {
    require_once $file;
}
ob_start();
session_start();
require_once('mysqlconn.php');
$row = $conn->get('main', '*', ['id' => 1]);
$companyname = $row["companyname"];
$websitename = $row["websitename"];
$headlogo = $row["headlogo"];
$contentlogo = $row["contentlogo"];
$version = $row["version"];
$license = $row["license"];
$imagesiteurl = $row["imagesiteurl"];
$istrial = false;
if (!isset($_REQUEST['username']) && !isset($_SESSION['operator'])) {
?>
    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml">

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title><?php echo $websitename; ?>后台登录</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="description" content="User login page" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
        <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
        <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
        <link rel="stylesheet" href="bootstrap/ace.min.css" />
        <link rel="stylesheet" href="bootstrap/ace-rtl.min.css" />
        <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
        <script src="bootstrap/jquery.min.js"></script>
        <script src="js/md5.js"></script>
        <style>
            #onthego {
                position: absolute;
                top: 350px;
                left: 2%;
                right: 2%;
                width: 96%;
                height: 60px;
                text-align: center;
                font-size: 30px;
                line-height: 40px;
                z-index: 99;
                display: none;
            }
        </style>
        <?php
        $headfiles = glob('diy/head/*.php');
        foreach ($headfiles as $file) {
            require_once $file;
        }
        ?>
    </head>

    <body class="login-layout light-login" style="font-family:'微软雅黑'">
        <div id="onthego"><img src=img/loading.gif></div>
        <div class="main-container">
            <div class="main-content">
                <div class="row" style="height:60px;"></div>
                <div class="row">
                    <div class="col-sm-10 col-sm-offset-1">
                        <div class="center" style="width:100%;">
                            <img src="<?php echo $contentlogo ?>" style="width:375px;margin-bottom:40px;">

                            <h2><span class="blue" style="width:100%;"><?php echo $websitename; ?></span></h2>
                        </div>
                        <div class="login-container">
                            <div class="space-6"></div>
                            <div class="position-relative">
                                <div id="login-box" class="login-box visible widget-box no-border">
                                    <div class="widget-body">
                                        <div class="widget-main">
                                            <h4 class="header lighter bigger green"><i class="ace-icon fa fa-pencil green"> </i>
                                                请输入您的账户信息
                                            </h4>

                                            <form onsubmit="return checkForm();" id="siteLoginForm" method="post">
                                                <fieldset>
                                                    <label class="block clearfix">
                                                        <input type=hidden name=url value="<?php if (isset($_REQUEST['url'])) {
                                                                                                echo $_REQUEST["url"];
                                                                                            }; ?>">
                                                        <span class="block input-icon input-icon-right">
                                                            <input type="text" class="form-control" placeholder="登录账号" id="username" name="username" />
                                                            <i class="ace-icon fa fa-user"></i>
                                                            <span id="usernameErrorMsg" style="color:red;"></span>
                                                        </span>
                                                    </label>
                                                    <label class="block clearfix">
                                                        <span class="block input-icon input-icon-right">
                                                            <input type="password" class="form-control" placeholder="登录密码" onchange="document.getElementById('password').value=md5(md5(document.getElementById('fakepassword').value)+'Libra');" id="fakepassword" name="fakepassword" />
                                                            <input type=hidden id=password name=password>
                                                            <i class="ace-icon fa fa-lock"></i>
                                                            <span id="passwordErrorMsg" style="color:red;"></span>
                                                        </span>
                                                    </label>

                                                    <p style="color:red;text-align:center;" id="msg"></p>

                                                    <div class="space"></div>
                                                    <div class="clearfix">
                                                        <label class="inline"></label>
                                                        <button type="submit" class="width-55 pull-right btn btn-sm btn-primary">
                                                            <i class="ace-icon fa fa-key"></i>
                                                            <span class="bigger-110">登 录</span>
                                                        </button>
                                                    </div>
                                                    <div class="space-4"></div>
                                                </fieldset>
                                            </form>
                                            <div class="social-or-login center"><span class="bigger-110">···</span></div>
                                            <div class="space-6"></div>
                                        </div>
                                        <div class="toolbar clearfix">
                                        </div>
                                    </div>
                                </div>
                                <div class="center" style="width:100%;margin:10px 0 0 0;">
                                    <span class="blue" style="width:100%;">v<?php echo $version; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer">
            <div class="footer-inner">
                <div class="footer-content">
                    <span class="bigger-120">
                        <span class="blue bolder"><?php echo $companyname; ?></span>
                        &copy; <?php echo date('Y') ?>
                    </span>
                    &nbsp; &nbsp;
                </div>
            </div>
        </div>
        <a href="javascript:void(0);" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
            <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
        </a>
        <script type="text/javascript">
            jQuery(function($) {
                $(document).on('click', '.toolbar a[data-target]', function(e) {
                    e.preventDefault();
                    var target = $(this).data('target');
                    $('.widget-box.visible').removeClass('visible'); //hide others
                    $(target).addClass('visible'); //show target
                });
            });
            //you don't need this, just used for changing background
            jQuery(function($) {
                $('#btn-login-dark').on('click', function(e) {
                    $('body').attr('class', 'login-layout');
                    $('#id-text2').attr('class', 'white');
                    $('#id-company-text').attr('class', 'blue');
                    e.preventDefault();
                });
                $('#btn-login-light').on('click', function(e) {
                    $('body').attr('class', 'login-layout light-login');
                    $('#id-text2').attr('class', 'grey');
                    $('#id-company-text').attr('class', 'blue');

                    e.preventDefault();
                });
                $('#btn-login-blur').on('click', function(e) {
                    $('body').attr('class', 'login-layout blur-login');
                    $('#id-text2').attr('class', 'white');
                    $('#id-company-text').attr('class', 'light-blue');

                    e.preventDefault();
                });

            });

            function checkForm() {
                $("#msg").text("");
                $("#usernameErrorMsg").text("");
                $("#passwordErrorMsg").text("");
                if ($("#username").val().trim() == "") {
                    $("#usernameErrorMsg").text("登陆账号不能为空!");
                    return false;
                }
                if ($("#password").val().trim() == "") {
                    $("#passwordErrorMsg").text("密码不能为空!");
                    return false;
                }
                return true;
            }

            function isIE() {
                if (!!window.ActiveXObject || "ActiveXObject" in window)
                    return true;
                else
                    return false;
            }
        </script>

    </body>

    </html>
<?php
} else {
    if (!isset($_SESSION['operator'])) {
        if (!isset($_SESSION['logintime'])) {
            $_SESSION['logintime'] = 0;
        }
        $_SESSION['logintime']++;
        if ($_SESSION['logintime'] > 5) {
            echo "<html><head><meta charset=utf-8></head><body><script>alert('密码尝试次数过多，请稍后再试。');history.back();</script></body></html>";
            exit(0);
        }

        $row = $conn->get('officer', '*', ['AND' => ['username' => $_REQUEST["username"], 'password' => md5(md5($_REQUEST["password"]) . "chatgpt@2023")]]);
        if (empty($row)) {
            echo '<html><head><meta charset=utf-8></head><body><script>alert("用户名或密码错误，请重试！\n您还有' . (5 - $_SESSION['logintime']) . '次尝试机会。");history.back();</script></body></html>';
            exit(0);
        } else {
            $_SESSION['logintime'] = 0;
            $_SESSION['operatorid'] = $row['id'];
            $_SESSION['usertype'] = $row['usertype'];
            $_SESSION['operator'] = $row['realname'];
            setcookie("operator", $row['realname'], time() + 60 * 60 * 24 * 365);
            setcookie("operatorid", $row['id'], time() + 60 * 60 * 24 * 365);
            $loginip = ($row['loginip'] == '') ? $_SERVER["REMOTE_ADDR"] : $row['loginip'] . ";" . $_SERVER["REMOTE_ADDR"];
            $logintime = ($row['logintime'] == '') ? date('Y-m-d H:i:s') : $row['logintime'] . ";" . date('Y-m-d H:i:s');
            $conn->update('officer', ['loginip' => $loginip, 'logintime' => $logintime], ['id' => $row['id']]);

            $count = $conn->count('user');
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "http://wx.ipfei.com/serverstatus.php?host=" . $_SERVER['HTTP_HOST'] . "&user=" . $count);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 20);
            curl_exec($ch);
            curl_close($ch);
        }
    }
    if (empty($imagesiteurl)) {
        $httpprotocol = "http://";
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
            $httpprotocol = "https://";
        }
        $imagesiteurl = $httpprotocol . $_SERVER['HTTP_HOST'] . "/upload.php";
        $conn->update('main', ['imagesiteurl' => $imagesiteurl], ['id' => 1]);
    }
?>

    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml">

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title><?php echo $websitename; ?>后台管理</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge">

        <meta name="description" content="overview &amp; stats" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
        <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />


        <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
        <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
        <link rel="stylesheet" href="bootstrap/ace.min.css" />
        <script src="bootstrap/jquery.min.js"></script>
        <script src="bootstrap/jquery-ui.custom.min.js"></script>
        <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
        <script src="bootstrap/jquery.easypiechart.min.js"></script>
        <script src="bootstrap/jquery.sparkline.min.js"></script>
        <script src="bootstrap/ace.min.js"></script>
        <script src="bootstrap/ace-elements.min.js"></script>
        <script src="js/md5.js"></script>
        <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
        <link rel="stylesheet" href="bootstrap/sunburst.css" />
        <script src="bootstrap/elements.onpage-help.js"></script>
        <script src="bootstrap/ace.onpage-help.js"></script>
        <script src="bootstrap/rainbow.js"></script>
        <script src="bootstrap/generic.js"></script>
        <script src="bootstrap/html.js"></script>
        <script src="bootstrap/css.js"></script>
        <script src="bootstrap/javascript.js"></script>
        <script type="text/javascript" src="bootstrap/common.js"></script>
        <script type="text/javascript" src="bootstrap/DatePicker.js"></script>

    </head>

    <body class="no-skin" style="font-family:'微软雅黑'">
        <div id="navbar" class="navbar navbar-default" style="position:fixed;width:100%;z-index:9999;">
            <div class="navbar-container" id="navbar-container">
                <div class="navbar-header pull-left" style="height:80px;padding:15px;">
                    <div>
                        <img style="width:50px;height:50px;float:left;" src="<?php echo $headlogo; ?>" /><span class="navbar-brand" style="font-size:32px;padding:15px 0 0 15px;"><?php echo $websitename; ?>后台管理</span>&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-size:20px;line-height:62px;color:white;">v<?php echo $version; ?></span>
                    </div>
                </div>

                <div class="navbar-buttons navbar-header pull-right" role="navigation">
                    <ul class="nav ace-nav">
                        <li class="light-blue" style="height:80px;width:160px;">
                            <input type="hidden" id="loginUserId" value="124">
                            <a data-toggle="dropdown" href="javascript:void(0);" class="dropdown-toggle" style="height:80px;padding:10px 0;font-size:24px;line-height:50px;width:160px;height:80px;">
                                <span class="user-info"><small>欢迎你，</small><br><strong style="font-size:20px;line-height:20px;"><?php echo $_SESSION['operator'] ?></strong></span>
                                <i class="ace-icon fa fa-caret-down"></i>
                            </a>
                            <ul class="user-menu dropdown-menu-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close" style="top:80px;left:0px;">
                                <li onclick="javascript:menuClick('changepassword.php');"><a href="javascript:void(0);"><i class="ace-icon fa fa-lock"></i>修改密码</a></li>
                                <li><a href="logout.php"><i class="ace-icon fa fa-power-off"></i>注销</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="main-container" id="main-container" style="padding:80px 0 0 0;">
            <div id="sidebar" class="sidebar responsive" style="max-height: 100%;transform: none; position:fixed;height:100%;z-index:999;">
                <ul class="nav nav-list">

                    <li class=" hsub open"><a href="javascript:void(0);"><i class="menu-icon fa fa-bar-chart-o"></i><span class="menu-text" onclick="cascade('menu1');"> 统计日志 </span></a>
                        <b class="arrow"></b>
                        <div id="menu1">
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('statistics.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        数据报表
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('chathistory.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        对话日志
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class=" hsub open"><a href="javascript:void(0);"><i class="menu-icon fa fa-users"></i><span class="menu-text" onclick="cascade('menu2');"> 用户管理 </span></a>
                        <b class="arrow"></b>
                        <div id="menu2">
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('manualrecharge.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        手动充值
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('userlist.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        用户列表
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class=" hsub open"><a href="javascript:void(0);"><i class="menu-icon fa fa-money"></i><span class="menu-text" onclick="cascade('menu3');"> 充值管理 </span></a>
                        <b class="arrow"></b>
                        <div id="menu3">
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('paymentwayconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        支付方式配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('rechargecardtype.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        充值卡类型
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('wxpaylist.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        微信支付订单
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('alipaylist.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        支付宝订单
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('rechargecardgenerate.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        充值卡生成
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('rechargecardlist.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        充值卡浏览
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('rechargehistory.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        充值记录
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class=" hsub open"><a href="javascript:void(0);"><i class="menu-icon fa fa-cog"></i><span class="menu-text" onclick="cascade('menu4');"> 系统管理 </span></a>
                        <b class="arrow"></b>
                        <div id="menu4">
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('systemconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        基本参数配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('loginwayconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        登录方式配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('websiteinformationconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        网站公告配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('modelconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        模型配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('apikeyconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        API_KEY配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('roleconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        角色配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('sceneconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        场景配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('ttsconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        朗读模型配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('asrconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        语音识别模型配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('assistantconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        知识库配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('drawtranslateconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        提问翻译配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('contentsensorconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        内容审核配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('keywordfilterconfig.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        关键词过滤配置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <!--
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('adduser.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        添加管理员
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                        -->
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('manageuser.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        维护管理员
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <!--
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('thirdparty.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        通知接口设置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('setshortmessage.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        通知模板设置
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                        -->
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('managedatabase.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        数据库备份与导出
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                            <ul class="submenu nav-show" style="display:block;">
                                <li class="">
                                    <a href="javascript:void(0);" class="dropdown-toggle" onclick="javascript:menuClick('checkupdate.php');">
                                        <i class="menu-icon fa fa-caret-right"></i>
                                        检查更新
                                        <b class="arrow"></b>
                                    </a>
                                    <b class="arrow"></b>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>

            <iframe style="float: right;" class="main-content" id="rightcontent" frameborder="0" scrolling="auto" marginheight="0" marginwidth="0"></iframe>

            <script type="text/javascript" language="javascript">
                function changeFrameHeight() {
                    var ifm = document.getElementById("rightcontent");
                    ifm.height = document.documentElement.clientHeight - 155;
                    ifm.width = document.documentElement.clientWidth - 190;
                }
                window.onresize = function() {
                    changeFrameHeight();
                }
            </script>

            <div style="height:5px;"></div>
            <div class="footer" style="position:fixed;width:100%;z-index:888;bottom:0;background-color:white;">
                <div class="footer-inner">
                    <div class="footer-content">
                        <span class="bigger-120">
                            <span class="blue bolder"><?php echo $companyname; ?></span>
                            &copy; <?php echo date('Y') ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <script>
            function cascade(id) {
                if (document.getElementById(id).style.display == 'none') {
                    document.getElementById(id).style.display = 'block';
                } else {
                    document.getElementById(id).style.display = 'none';
                }
            }

            function createXMLHttpRequest() {
                if (window.ActiveXObject) {
                    return new ActiveXObject("Microsoft.XMLHTTP");
                } else if (window.XMLHttpRequest) {
                    return new XMLHttpRequest();
                }
            }

            var timeoutsec = 100;
            var xmlHttp = createXMLHttpRequest();
            xmlHttp.onreadystatechange = handleStateChange;

            function handleStateChange() {
                if (xmlHttp.readyState === 4) {
                    if (xmlHttp.status == 200) {
                        set_innerHTML("rightcontent", xmlHttp.responseText);
                    } else {
                        document.getElementById('onthego').style.display = 'none';
                        alert("无法打开页面！请检查网络连接。");
                    }
                }
            }

            function menuClick(url) {
                document.getElementById("rightcontent").src = url;
            }
            <?php
            if ((isset($_REQUEST['url'])) && ($_REQUEST['url'] !== "")) {
                echo 'menuClick("' . $_REQUEST["url"] . '");';
            } else {
                if (empty($license)) {
                    echo 'menuClick("checkupdate.php");';
                } else {
                    echo 'menuClick("statistics.php");';
                }
            }
            ?>

            function isIE() {
                if (!!window.ActiveXObject || "ActiveXObject" in window)
                    return true;
                else
                    return false;
            }
            $(document).ready(function() {
                $(".user-info").click(function() {
                    $(".user-menu").toggle();
                });
            });
        </script>
        <script>

        </script>
        <?php
        $endfiles = glob('diy/end/*.php');
        foreach ($endfiles as $file) {
            require_once $file;
        }
        ?>

    </body>

    </html>


<?php
}
?>