<?php
define('IN_API', true);
require dirname(__FILE__) . '/_lp.php';

// 清理临时记录
if (isset($_GET['clear_temp'])) {
    clear_temp_article();
}

$appCode='ks1cck2.1';
$appTitle='慢读细品评(v2.1)';


// 自定义版本号
$version = '1'; // 根据需要切换版本号

// 动态设置表前缀
$tablePrefix = 'cck' . $version . '_';

$appName = 'cck' . $version ;

require 'func.inc.php';

// 获取用户代理
$user_agent = $_SERVER['HTTP_USER_AGENT'];
// 获取当前访问的域名
$current_domain = $_SERVER['HTTP_HOST'];

// 检测是否在微信中打开
$is_wechat = strpos($user_agent, 'MicroMessenger') !== false;

// 判断是否是开发用的本地域名  http://devpubhelper.shdic.com/
// 缓存判断结果
static $is_local_domain_cache = null;
if ($is_local_domain_cache === null) {
    $is_local_domain_cache = $current_domain === 'pubhelper4dev2.shdic.com';
}
$is_local_domain = $is_local_domain_cache;

//$password='taotao420601';echo  md5(md5($password) . "chatgpt@2023");

if(!defined('IN_API2') ){

    if ($is_wechat) {
        // 在微信中打开
        echo "<h3>您正在微信中打开此页面 $appTitle $appCode</h3>";
        // 可以添加微信特有的功能，例如分享按钮等
    } elseif ($is_local_domain) {
        // 在普通浏览器中打开，且是本地域名
        echo "<h1>您正在普通浏览器中打开此页面（<span style='color:#f00;'>开发模式</span>）$appTitle $appCode </h1>";
        // 可以添加普通浏览器的功能
    } else {
        // 在普通浏览器中打开，且不是本地域名

        $is_debug = isset($_GET['is_debug']) ? (int)$_GET['is_debug'] : 1;
        
        if($is_debug==420601){
            $_SESSION['is_debug']=1;
            //pass
        }else if(isset($_SESSION['is_debug']) && $_SESSION['is_debug']==1){
            //pass
        }else{
            echo "<h1>请在微信中打开此页面</h1>";    
            die();
        }
        
    }
}

$rnd_code=mt_rand(1000, 9999);

// 检测是否为大字版
$large_text = isset($_GET['large_text']) && $_GET['large_text'] == 'true';

// 生成切换链接
$nav_html = '<!-- 切换版本链接 -->
<p style="text-align: center;">';

$nav_html .= '<a href="index.php?'.$rnd_code.'" target="_self">首页</a>';

//$nav_html .=' | ';

$css_file = 'res/style.css'; // 标准版样式
/*
if ($large_text) {
    // 移除large_text参数，保留其他参数
    $query_params = array_diff($_GET, ['large_text' => 'true']);
    $nav_html .= '<a href="?'.http_build_query($query_params).'">切换到标准版</a>';
    $css_file = 'res/large_text.css'; // 大字版样式
} else {
    // 添加large_text=true参数，保留其他参数
    $query_params = $_GET;
    $query_params['large_text'] = 'true';
    $nav_html .= '<a href="?'.http_build_query($query_params).'">切换到大字版</a>';
    $css_file = 'res/style.css'; // 标准版样式
}

$nav_html .=' | ';
*/


if (isset($_SESSION[SESSION_KEY]['user']['id'])) {
    $role = $_SESSION[SESSION_KEY]['user']['role']??null;
    $username = $_SESSION[SESSION_KEY]['user']['username']??null;

    //$nav_html .= '<a href="logout.php?'.$rnd_code.'" target="_self">退出登录</a>';    

    if($role=='user' || $role=='admin' ){
        /*
        $nav_html .=' | ';
        $nav_html .= '<a href="dashboard.php?'.$rnd_code.'" target="_self">dashboard</a>';
        */

        $nav_html .=' | ';
        $nav_html .= '<a href="edit_profile.php?'.$rnd_code.'" target="_self">profile</a>';

        $nav_html .=' | ';
        $nav_html .= '<a href="add_link.php?'.$rnd_code.'" target="_self">提交文章</a>';  

        $nav_html .=' | ';
        $nav_html .= '<a href="my_link.php?'.$rnd_code.'" target="_self">查看共创</a>'; 

        /*
        $nav_html .=' | ';
        $nav_html .= '<a href="index.php?mode=group&'.$rnd_code.'" target="_self">群组首页</a>'; 
        */

        $nav_html .=' | ';
        $nav_html .= '<a href="../ks1xpp/index.php?'.$rnd_code.'" target="_self">to 细品评</a>';
        
    }

    if($role=='admin'){
        $nav_html .=' | ';
        $nav_html .= '<a href="admin.php?'.$rnd_code.'" target="_self">admin</a>';

        $nav_html .=' | ';
        $nav_html .= '<a href="groups_list.php?'.$rnd_code.'" target="_self">groups</a>';

        $nav_html .=' | ';
        $nav_html .= '<a href="record_structure.php?'.$rnd_code.'" target="_self">record_structure</a>';

        $nav_html .=' | ';
        $nav_html .= '<a href="check_structure.php?'.$rnd_code.'" target="_self">check_structure</a>';

    }


}else{
    $nav_html .= '<a href="login.php?'.$rnd_code.'" target="_self">登陆</a>';
}

$nav_html .= show_session_message_clear();

$nav_html .= '</p>';

// 退出登录功能
$logout_html = '<p style="text-align: center;">
    <a href="logout.php">退出登录</a>
</p>';