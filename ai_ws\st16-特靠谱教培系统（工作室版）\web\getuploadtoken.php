<?php

if (isset($_GET["userrndstr"])) {
    require_once('admin/mysqlconn.php');
    if (!$conn->has('user',['rndstr'=>$_GET["userrndstr"]])) {
        http_response_code(403); // 返回403错误，表示禁止访问
    }
}

$row = $conn->get('main','*',['id'=>1]);
$imagesiteurl = $row["imagesiteurl"];
$imagesitetoken = $row["imagesitetoken"];

$timestamp = file_get_contents($imagesiteurl . "?action=getnonce");
$sign = md5(md5($timestamp) . $imagesitetoken);
echo '{"nonce":"' . $timestamp . '","sign":"' . $sign . '"}';
