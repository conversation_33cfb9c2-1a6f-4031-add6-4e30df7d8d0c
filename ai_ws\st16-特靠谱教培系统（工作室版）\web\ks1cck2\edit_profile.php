<?php
require '_ks1.php';
@session_start();

// 检查用户是否已登录
if (is_logged_in() === false) {
    header('Location: login.php');
    exit;
}

// 获取当前用户信息
$user_id = $_SESSION[SESSION_KEY]['user']['id'];
$user_info = get_user_info($user_id);

// 处理用户提交的表单数据
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $public_account_name = trim($_POST['public_account_name']);
    $wechat_name = trim($_POST['wechat_name']);
    $wechat_openid = trim($_POST['wechat_openid']);

    // 验证数据有效性（可根据实际需求添加更多验证规则）
    if (empty($username)) {
        $_SESSION['error_message'] = '用户名不能为空';
        header('Location: edit_profile.php');
        exit;
    }
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $_SESSION['error_message'] = '邮箱格式不正确';
        header('Location: edit_profile.php');
        exit;
    }
    if (!empty($phone) && !preg_match('/^\d{11}$/', $phone)) {
        $_SESSION['error_message'] = '手机号码格式不正确';
        header('Location: edit_profile.php');
        exit;
    }

    // 更新用户信息
    $update_result = update_user_info($user_id, $username, $email, $phone, $public_account_name, $wechat_name, $wechat_openid);
    if ($update_result) {

        user_login_xp($user_id);
        
        $_SESSION['success_message'] = '用户信息更新成功';
        header('Location: edit_profile.php');
        exit;
    } else {
        $_SESSION['error_message'] = '用户信息更新失败';
        header('Location: edit_profile.php');
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改个人信息</title>
    <link rel="stylesheet" href="res/wx_style.css">
</head>
<body>
    <div>
        <center>
            <h2>修改个人信息</h2>
        </center>
        <?php echo $nav_html; ?>
    </div>

    <?php

    ?>

    <form action="edit_profile.php" method="post" style="margin: 20px;">
        <label for="username">用户名：</label>
<input type="text" id="username" name="username" value="<?php echo isset($user_info['username']) ? htmlspecialchars($user_info['username']) : ''; ?>" required><br>

<label for="email">邮箱：</label>
<input type="text" id="email" name="email" value="<?php echo isset($user_info['email']) ? htmlspecialchars($user_info['email']) : ''; ?>" required><br>

<label for="phone">手机号码：</label>
<input type="text" id="phone" name="phone" value="<?php echo isset($user_info['phone']) ? htmlspecialchars($user_info['phone']) : ''; ?>"><br>

<label for="public_account_name">公众号名称：</label>
<input type="text" id="public_account_name" name="public_account_name" value="<?php echo isset($user_info['public_account_name']) ? htmlspecialchars($user_info['public_account_name']) : ''; ?>"><br>

<label for="wechat_name">微信用户名称：</label>
<input type="text" id="wechat_name" name="wechat_name" value="<?php echo isset($user_info['wechat_name']) ? htmlspecialchars($user_info['wechat_name']) : ''; ?>"><br>
<?php
/*        <label for="wechat_openid">微信OpenID：</label>
        <input type="text" id="wechat_openid" name="wechat_openid" value="<?php echo htmlspecialchars($user_info['wechat_openid']); ?>"><br>
*/
?>
        <input type="submit" value="保存修改">
    </form>

    <div>
        <?php echo $nav_html; ?>
    </div>
</body>
</html>