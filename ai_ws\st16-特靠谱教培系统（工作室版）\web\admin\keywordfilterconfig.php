<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
$mykeywords = "";
if ((isset($_POST["action"])) && ($_POST["action"] == "search")) {
    $result = $conn->select("myfilter", "*", ["keyword[~]" => $_POST["searchword"]]);
    foreach ($result as $row) {
        $mykeywords .= "," . $row["keyword"];
    }
    if ($mykeywords != "") {
        $mykeywords = substr($mykeywords, 1);
        $mykeywordsarr = explode(",", $mykeywords);
    }
} elseif ((isset($_POST["action"])) && ($_POST["action"] == "set")) {
    $conn->update('main',['isquestionfilter'=>$_POST["isquestionfilter"],'isanswerfilter'=>$_POST["isanswerfilter"]],['id'=>1]);
    $strarray = explode(",", str_replace("\r", "", str_replace("\n", ",", $_POST["keywords"])));
    foreach ($strarray as $str) {
        $conn->insert('myfilter',['keyword'=>trim($str)]);
    }
    echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');parent.location.href='keywordfilterconfig.php';</script>";
    exit;
} elseif ((isset($_GET["action"])) && ($_GET["action"] == "delete")) {
    if ($conn->has('myfilter',['keyword'=>$_GET['word']])) {
        $conn->delete('myfilter',['keyword'=>$_GET['word']]);
        echo "<html><head><meta charset=utf-8></head><body><script>alert('删除成功！');</script>";
    } else {
        echo "<html><head><meta charset=utf-8></head><body><script>alert('该关键词不存在！');</script>";
    }
    exit;
}

$row = $conn->get('main','*',['id'=>1]);
$isquestionfilter = $row["isquestionfilter"];
$isanswerfilter = $row["isanswerfilter"];
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>关键词过滤配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />


    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }

        .namespan {
            border: 1px solid darkgrey;
            padding: 0 7px;
            margin: 2px 10px 0 0;
            vertical-align: top;
            line-height: 25px;
            white-space: nowrap;
            /*强制span不换行*/
            display: inline-block;
            /*将span当做块级元素对待*/
            max-width: 200px;
            /*限制宽度*/
            overflow: hidden;
            /*超出宽度部分隐藏*/
            text-overflow: ellipsis;
            /*超出部分以点号代替*/
            cursor: pointer;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>
    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">关键词过滤配置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px 0;">
            <div class="page-content-area">
                <div class="row">
                    <div class="space-6"></div>
                    <div class="col-xs-12">
                        <form class="form-horizontal" method=post style="border-radius: 10px;padding:20px 40px;line-height:30px;margin:0 40px;border:1px solid silver;">
                            <div class="form-group">
                                <input type=hidden name=action value=search>
                                <label class="col-lg-4 control-label">模糊搜索已添加的关键词：</label>

                                <div class="col-lg-2">
                                    <input type="text" style="text-align:left;" id="searchword" name="searchword" class="bg-focus form-control" autoComplete="off">
                                </div>
                                <button type=submit class="btn btn-primary" style="height:34px;padding:0 10px;">查找</button>
                            </div>

                            <div class="form-group">
                                <lable class="col-lg-2 control-label">搜索结果：</lable>
                                <div class="col-lg-6" id="searchresult" style="border:1px solid #eee;height:90px;width:70%;">
                                    <?php
                                    if (isset($mykeywordsarr)) {
                                        foreach ($mykeywordsarr as $word) {
                                            echo "<span class='namespan' title='点击删除该关键词' onclick='deletekeyword(this)'>" . $word . "</span>";
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                        </form>
                        <form class="form-horizontal" method=post target="temp" style="border-radius: 10px;padding:20px 40px;line-height:30px;margin:20px 40px;border:1px solid silver;">
                            <div style="margin-bottom:20px;">关键词过滤功能开启后，匹配到的关键词将统一替换为*</div>
                            <div class="form-group">
                                <input type=hidden name=action value=set>
                                <label class="col-lg-4 control-label">开启用户提问关键词过滤</label>

                                <div class="col-lg-4">
                                    <select name=isquestionfilter>
                                        <option value=1 <?php echo ($isquestionfilter) ? "selected" : "" ?>>是</option>
                                        <option value=0 <?php echo ($isquestionfilter) ? "" : "selected" ?>>否</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-4 control-label">开启AI回复内容关键词过滤</label>

                                <div class="col-lg-4">
                                    <select name=isanswerfilter>
                                        <option value=1 <?php echo ($isanswerfilter) ? "selected" : "" ?>>是</option>
                                        <option value=0 <?php echo ($isanswerfilter) ? "" : "selected" ?>>否</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-4 control-label">添加关键词列表：</label>

                                <div class="col-lg-4">
                                    <textarea id="keywords" name="keywords" class="col-lg-4 bg-focus form-control" placeholder="请用英文逗号或回车分隔关键词"></textarea>
                                </div>
                            </div>

                            <div class="form-group" align="center" style="margin-right:150px;margin-top:15px">
                                <div class="col-lg-6 col-lg-offset-3">
                                    <button type="submit" class="btn btn-primary">确认配置</submit>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        function deletekeyword(element) {
            if (confirm("确认删除关键词 " + element.innerText + " 吗？")) {
                document.getElementById("result").src = "keywordfilterconfig.php?action=delete&word=" + element.innerText;
                element.style.display = "none";
            }
        }
    </script>
    <iframe style="display:none;" id=result name=temp></iframe>
</body>

</html>