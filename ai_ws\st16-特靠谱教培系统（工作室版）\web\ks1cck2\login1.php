<?php
require '_ks1.php';
@session_start();

$error_log='';

// 接收参数
$userrndstr = input('get', 'userrndstr', 's');
if($userrndstr){
    $sql = "select * from user where rndstr='" . $userrndstr . "' and not isforbidden";
    $data=get_line($sql);
    var_dump($data);
    if($data && isset($data['id'])){
        login_success($data['id'], $data['username'], 'user',$data['email'],$data);
        //登陆成功后跳转到index ，否则继续执行
    }else{
        /*
        echo $sql;
        echo '<br>';
        var_dump($data);
        $error_log.='sql='.$sql.'<br>';
        die;
        */
    }
}else{
    //$error_log.='miss rndstr'.'<br>';

    /*
    echo $error_log;
    echo '<br>';
    die;
    */
}

// 登录逻辑
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];
    if (user_login($username, $password)) {

        //header('Location: index.php');

           $error='<script>
window.onload = function () {
    window.open("index.php", "_self");
};        
    </script>' ;


        //exit;
    } else {
        $error = '无效的用户名或密码';
    }
}

$css_file='res/wx_style.css';
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录</title>
    <link rel="stylesheet" href="<?php echo $css_file; ?>"> <!-- 动态引用CSS -->
</head>
<body>
    <h1>登录</h1>
    <?php if (!empty($error)): ?>
        <p style="color: red;"><?php echo $error; ?></p>
    <?php endif; ?>
    <?php if (!empty($error_log)): ?>
        <p style="color: red;"><?php echo $error_log; ?></p>
    <?php endif; ?>
    <form method="post">
        <label for="username">用户名：</label>
        <input type="text" name="username" id="username" required><br>
        <label for="password">密码：</label>
        <input type="password" name="password" id="password" required><br>
        <button type="submit">登录</button>
    </form> 

    <p>推荐从首页 <a href="http://pubhelper.shdic.com">pubhelper.shdic.com</a> 登录</p>

    <div>
        <?php echo $nav_html; ?>
    </div>
</body>
</html>