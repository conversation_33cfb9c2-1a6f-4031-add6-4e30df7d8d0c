<?php
header("Access-Control-Allow-Origin: *");
use Medoo\Medoo;
if ((isset($_GET["action"])) && ($_GET["action"] == "updateuserid")) {
    if (isset($_GET["userrndstr"])) {
        $userrndstr = $_GET["userrndstr"];
    } else {
        exit;
    }
    if (isset($_GET["userid"])) {
        $userid = $_GET["userid"];
    } else {
        exit;
    }
    require_once('admin/mysqlconn.php');
    $row = $conn->get('user','*',['rndstr'=>$userrndstr]);
    if (!empty($row["avatar"])) {
        echo "您目前登录的账号已绑定其他来自第三方登录的用户。您可以退出当前账号，重新从第三方登录并建立您的新账号。<br><a href='index.php'>回到首页</a>";
        exit;
    } else {
        $conn->update('user',['avatar'=>$userid],['rndstr'=>$userrndstr]);
        header("Location:index.php");
        exit;
    }
}
if (((isset($_POST["action"])) && ($_POST["action"] == "getnonce")) || ((isset($_GET["action"])) && ($_GET["action"] == "getnonce"))) {
    echo time();
    exit;
}
if ((isset($_POST["action"])) && ($_POST["action"] == "login")) {
    $userid = $_POST['userid'];
    $nonce = $_POST['nonce'];
    $sign = $_POST['sign'];
} else if ((isset($_GET["action"])) && ($_GET["action"] == "login")) {
    $userid = $_GET['userid'];
    $nonce = $_GET['nonce'];
    $sign = $_GET['sign'];
} else {
    echo "请带参数调用本页面";
    exit;
}

if (((time() - $nonce) > 60) || ((time() - $nonce) < 0)) {
    echo "第三方跳转参数nonce过期，请重新登陆";
    exit;
}
if (empty($userid)) {
    echo "第三方跳转参数userid为空";
    exit;
}

function randomString($len = 32)
{
    $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    $maxPos = strlen($chars);
    $pwd = '';
    for ($i = 0; $i < $len; $i++) {
        $pwd .= substr($chars, mt_rand(0, $maxPos - 1), 1);
    }
    return $pwd;
}

$userrndstr = randomString(16) . intval(microtime(true) * 1000);
require_once('admin/mysqlconn.php');
$row = $conn->get('main','*',['id'=>1]);
$token = $row["thirdpartytoken"];
$freetry = $row["freetry"];
$freedays = $row["freedays"];
if ($sign != md5(md5($userid . $nonce) . $token)) {
    echo "第三方跳转参数sign校验失败";
    
    exit;
}
$row = $conn->get('user','*',['avatar'=>$userid]);
if (empty($row)) {
    if (isset($_COOKIE['userrndstr'])) {
        echo '<script src="js/jquery.min.js"></script><script src="js/jquery.cookie.min.js"></script><script src="js/crypto-js.min.js"></script><script>location.href="thirdpartyuserlogin.php?action=updateuserid&userrndstr="+CryptoJS.AES.decrypt($.cookie("userrndstr"), "ChatGPT").toString(CryptoJS.enc.Utf8)+"&userid=' . $userid . '";</script>';
        exit();
    } else {
        $conn->insert('user',['rndstr'=>$userrndstr,'quota'=>$freetry,'expiretime'=>date('Y-m-d H:i:s', strtotime('+' . $freedays . ' day')),'registertime'=>date('Y-m-d H:i:s'),'loginip'=>$_SERVER["REMOTE_ADDR"],'logintime'=>date('Y-m-d H:i:s'),'ismobile'=>0]);
        $conn->update('user',['userid'=>Medoo::raw('id+1001'),'avatar'=>$userid],['rndstr'=>$userrndstr]);
        echo '<script src="js/jquery.min.js"></script><script src="js/jquery.cookie.min.js"></script><script src="js/crypto-js.min.js"></script><script>$.cookie("userrndstr",CryptoJS.AES.encrypt("' . $userrndstr . '", "ChatGPT").toString(),{expires:30, path:"/"});location.href="index.php";</script>';
        exit();
    }
} else {
    if ($row["isforbidden"]) {
        echo '该账号已被网站管理员封禁';
        exit();
    } else {
        $conn->update('user',['rndstr'=>$userrndstr,'ismobile'=>0,'loginip'=>$row['loginip'] . ";" . $_SERVER["REMOTE_ADDR"],'logintime'=>$row['logintime'] . ";" . date('Y-m-d H:i:s')],['id'=>$row['id']]);
        echo '<script src="js/jquery.min.js"></script><script src="js/jquery.cookie.min.js"></script><script src="js/crypto-js.min.js"></script><script>$.cookie("userrndstr",CryptoJS.AES.encrypt("' . $userrndstr . '", "ChatGPT").toString(),{expires:30, path:"/"});location.href="index.php";</script>';
        exit();
    }
}
